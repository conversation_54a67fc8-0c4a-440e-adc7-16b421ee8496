/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:04:29
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-02 21:02:13
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_cfg.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "read_map_point_cfg.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <jsoncpp/json/json.h>

ReadMapCfg::ReadMapCfg() {
}

ReadMapCfg::~ReadMapCfg() {
}

void ReadMapCfg::loadConfig(std::string& configPath) {
    setConfigPath(configPath);
    std::ifstream file(configPath);
    if (!file.is_open()) {
        std::cerr << "Error opening file." << std::endl;
        return;
    }
    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string jsonString = buffer.str();
    file.close();
    Json::Value root;
    Json::CharReaderBuilder readerBuilder;
    std::string errs;
    std::istringstream s(jsonString);
    if (!Json::parseFromStream(readerBuilder, s, &root, &errs)) {
        std::cerr << "Error parsing JSON: " << errs << std::endl;
        return;
    }
    if (root["mapping_point"].isNull()) {
        std::cerr << "Error parsing JSON:  not \"mapping_point\"" << errs << std::endl;
        return;
    }
    setPressPoints(root["mapping_point"]["press_point"].toStyledString());
    setPhonePoints(root["mapping_point"]["photo_point"].toStyledString());
    setPatrolPoints(root["mapping_point"]["patrol_point"].toStyledString());
    setFamilyMovePoints(root["mapping_point"]["familyMovePoint"].toStyledString());
    setDeliverCakePoints(root["mapping_point"]["deliverCake_point"].toStyledString());
    setGoHomePoints(root["mapping_point"]["goHome_point"].toStyledString());
    setBatteryChargingPoints(root["mapping_point"]["batteryChargingPoint"].toStyledString());
    setChargeNavPoints(root["mapping_point"]["chargeNavPoint"].toStyledString());
}



void ReadMapCfg::update() {
    std::ofstream file(getConfigPath().c_str());
    if (!file.is_open()) {
        std::cerr << "Error opening file!" << std::endl;
        return;
    }
    /*json文件按照格式组装，更新到本地的配置文件*/
    Json::Value root;
    Json::CharReaderBuilder reader;
    std::string errs;
    Json::Value valueTemp;
    std::istringstream sstream(getPressPoints());
    if (Json::parseFromStream(reader, sstream, &valueTemp, &errs)) {
        
    } else {
        std::cout << "Failed to parse JSON: " << errs << std::endl;
    }

    Json::StreamWriterBuilder writer;
    // 设置格式化缩进
    writer["indentation"] = "    "; 
    std::string jsonString = Json::writeString(writer, root);
    file << jsonString;
    file.close();
}
