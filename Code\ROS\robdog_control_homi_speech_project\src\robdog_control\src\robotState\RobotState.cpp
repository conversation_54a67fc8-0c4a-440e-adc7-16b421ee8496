#include "RobotState.h"
#include <iostream>
// #include <ros/ros.h>
#include <rclcpp/rclcpp.hpp>
#include <ament_index_cpp/get_package_share_directory.hpp>

RobotState::RobotState() {
}

RobotState::~RobotState() {
    saveConfig();
}

void RobotState::loadConfig(std::string& msg) {
    configPath = msg;
    XMLDocument doc;
    XMLError error = doc.LoadFile(configPath.c_str());
    RCLCPP_INFO_STREAM(rclcpp::get_logger("RobotState"),"Loading configuration file from: " << configPath.c_str());
    if (error != XML_SUCCESS) {
        // ROS_ERROR_STREAM ("Failed to load configuration file: " << configPath << std::endl);
        RCLCPP_ERROR_STREAM(rclcpp::get_logger("RobotState"), "Failed to load configuration file: " << configPath << ", Error: " << doc.ErrorID() << ", Message: " << doc.ErrorStr());
        return;
    }

    XMLElement* root = doc.FirstChildElement("robot_state");
    if (!root) {
        RCLCPP_ERROR_STREAM(rclcpp::get_logger("RobotState"), "Root element not found in configuration file." << std::endl);
        return;
    }

    XMLElement* elem = root->FirstChildElement("network_status");
    if (elem && nullptr != elem->GetText()) {
        setNetworkStatus(elem->GetText());
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot networkStatus is  %s",getNetworkStatus().c_str());
    }

    elem = root->FirstChildElement("wifi_name");
    if (elem && nullptr != elem->GetText()) {
        setWifiName(elem->GetText());
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot wifi_name is  %s",getWifiName().c_str());
    }

    elem = root->FirstChildElement("scene_mode");
    if (elem && nullptr != elem->GetText()) {
        setSceneMode(atoi(elem->GetText()));
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot scene_mode is  %d",getSceneMode());
    }

    elem = root->FirstChildElement("user_connect_status");
    if (elem && nullptr != elem->GetText()) {
        setUserConnectStatus(atoi(elem->GetText()));
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot user_connect_status is  %d",getUserConnectStatus());
    } 

    elem = root->FirstChildElement("user_phone_number");
    if (elem && nullptr != elem->GetText()) {
        setUserPhoneNumber(elem->GetText());
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot user_phone_number is  %s", getUserPhoneNumber().c_str());
    }

    elem = root->FirstChildElement("action_type");
    if (elem && nullptr != elem->GetText()) {
        setActionType(elem->GetText());
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot action_type is  %s",getActionType().c_str());
    }

    elem = root->FirstChildElement("flash_status");
    if (elem && nullptr != elem->GetText()) {
        setFlashStatus(elem->GetText());
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot flash_status is  %s",getFlashStatus().c_str());
    }

    elem = root->FirstChildElement("flash_brightness");
    if (elem && nullptr != elem->GetText()) {
        setFlashBrightness(atoi(elem->GetText()));
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot flash_brightness is  %d", getFlashBrightness());
    }

    elem = root->FirstChildElement("volume");
    if (elem && nullptr != elem->GetText()) {
        setVolume(atoi(elem->GetText()));
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot volume is  %d",getVolume());
    }

    elem = root->FirstChildElement("battery_charge_status");
    if (elem && nullptr != elem->GetText()) {
        setBatteryChargeStatus(atoi(elem->GetText()));
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot battery_charge_status is  %d",getBatteryChargeStatus());
    }

    elem = root->FirstChildElement("battery_level");
    if (elem && nullptr != elem->GetText()) {
        setBatteryLevel(atoi(elem->GetText()));
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot battery_level is  %d",getBatteryLevel());
    }

    elem = root->FirstChildElement("wifi_switch");
    if (elem && nullptr != elem->GetText()) {
        setWifiSwitch(elem->GetText());
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot wifi_switch is  %s",getWifiSwitch().c_str());
    }

    elem = root->FirstChildElement("mobile_data_switch");
    if (elem && nullptr != elem->GetText()) {
        setMobileDataSwitch(elem->GetText());
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot mobile_data_switch is  %s",getMobileDataSwitch().c_str());
    }

    elem = root->FirstChildElement("intelligent");
    if (elem && nullptr != elem->GetText()) {
        setIntelligentSwitch(elem->GetText());
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot intelligent is  %s",getIntelligentSwitch().c_str());
    }

    elem= root->FirstChildElement("timestamp");
    if (elem && nullptr != elem->GetText()) {
        setTimeStamp(std::stoll(elem->GetText()));
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot timestamp is  %lld",getTimeStamp());
    }

    elem = root->FirstChildElement("current_mapid");
    if (elem && nullptr != elem->GetText()) {
        setMapId(elem->GetText());
        RCLCPP_INFO(rclcpp::get_logger("RobotState"), "robot current_mapid is  %s",getMapId().c_str());
    }
}

void RobotState::saveConfig() {
    XMLDocument doc;
    XMLElement* root = doc.NewElement("robot_state");
    doc.InsertEndChild(root);

    XMLElement* elem = doc.NewElement("network_status");
    if(!getNetworkStatus().empty()) {
        elem->SetText(getNetworkStatus().c_str());
        root->InsertEndChild(elem);
    }
    elem = doc.NewElement("user_connect_status");
    elem->SetText(getUserConnectStatus() ? "1" : "0");
    root->InsertEndChild(elem);
    if(!getUserPhoneNumber().empty()) {
        elem = doc.NewElement("user_phone_number");
        elem->SetText(getUserPhoneNumber().c_str());
        root->InsertEndChild(elem);
    }
    if(!getWifiName().empty()) {
        elem = doc.NewElement("wifi_name");
        elem->SetText(getWifiName().c_str());
        root->InsertEndChild(elem);
    }
    if(!getActionType().empty()) {
        elem = doc.NewElement("action_type");
        elem->SetText(getActionType().c_str());
        root->InsertEndChild(elem);
    }
    if(!getFlashStatus().empty()) {
        elem = doc.NewElement("flash_status");
        elem->SetText(getFlashStatus().c_str());
        root->InsertEndChild(elem);
    }

    elem = doc.NewElement("flash_brightness");
    char buf[16];
    sprintf(buf, "%d", getFlashBrightness());
    elem->SetText(buf);
    root->InsertEndChild(elem);

    elem = doc.NewElement("volume");
    sprintf(buf, "%d", getVolume());
    elem->SetText(buf);
    root->InsertEndChild(elem);

    elem = doc.NewElement("scene_mode");
    sprintf(buf, "%d", getSceneMode());
    elem->SetText(buf);
    root->InsertEndChild(elem);

    elem = doc.NewElement("battery_charge_status");
    sprintf(buf, "%d", getBatteryChargeStatus());
    elem->SetText(buf);
    root->InsertEndChild(elem);

    elem = doc.NewElement("battery_level");
    sprintf(buf, "%d", getBatteryLevel());
    elem->SetText(buf);
    root->InsertEndChild(elem);

    if(!getWifiSwitch().empty()) {    
        elem = doc.NewElement("wifi_switch");
        elem->SetText(getWifiSwitch().c_str());
        root->InsertEndChild(elem);
    }
    if(!getMobileDataSwitch().empty()) {    
        elem = doc.NewElement("mobile_data_switch");
        elem->SetText(getMobileDataSwitch().c_str());
        root->InsertEndChild(elem);
    }
    if(!getIntelligentSwitch().empty()) {    
        elem = doc.NewElement("intelligent");
        elem->SetText(getIntelligentSwitch().c_str());
        root->InsertEndChild(elem);
    }
    elem = doc.NewElement("timestamp");
    sprintf(buf, "%lld", getTimeStamp());
    elem->SetText(buf);
    root->InsertEndChild(elem);

    if(!getMapId().empty()) {
        elem = doc.NewElement("current_mapid");
        elem->SetText(getMapId().c_str());
        root->InsertEndChild(elem);
    }

    XMLError error = doc.SaveFile(configPath.c_str());
    if (error != XML_SUCCESS) {
        RCLCPP_ERROR_STREAM(rclcpp::get_logger("RobotState"), 
            "Failed to save configuration file: " << configPath 
            << " error code: " << error);
    }
}

void RobotState::setEmergencyContacts(const std::vector<EmergencyContact>& contacts) {
    EmergencyContactInfo_ = contacts;
}

void RobotState::setFamilyMembers(const std::vector<FamilyMember>& members) {
    FamilyMembers_ = members;
}

std::string RobotState::getResourcePath(const std::string &file_name){
  static const std::string package_share_dir = ament_index_cpp::get_package_share_directory("robdog_control");
  auto pathstr= package_share_dir + "/resource/" + file_name;
//   RCLCPP_INFO(this->get_logger(), "file dir is : %s", pathstr.c_str());
  return pathstr;
}