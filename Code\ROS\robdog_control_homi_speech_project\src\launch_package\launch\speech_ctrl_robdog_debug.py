import launch
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration
from launch_ros.substitutions import FindPackageShare
from launch_ros.actions import Node
import os
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    config_share_dir = get_package_share_directory('launch_package')

    robdog_control = Node(
        package="robdog_control",
        executable="robdog_control_node",
        output='log',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
    )



    return LaunchDescription([
            # expression,
            robdog_control
            # audio_player_node,
            # # video_gst_node,
            # live_stream_node,
    ])