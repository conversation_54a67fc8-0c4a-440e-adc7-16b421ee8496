/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-09-10 14:22:33
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-09-11 19:19:17
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\RobotState.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#pragma once

#include <string>
#include <fstream>
#include <tinyxml2.h>
#include <geometry_msgs/msg/pose.hpp>
#include <jsoncpp/json/json.h>
#include <homi_com/singleton.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>
#include <boost/filesystem.hpp>


using namespace tinyxml2;

enum class RobotStateEnum {
    NORMAL, //默认状态
    MAPPING,
    FOLLOWME,
    MAP_INITIALIZING,
    NAVIGATION,  //导航状态,
    OUTDOOR_TRIP
};

// 定义紧急联系人和家庭成员结构
struct EmergencyContact {
    std::string nickName;
    std::string phone;
};

struct FamilyMember {
    std::string nickName;
    std::string roleName;
    std::string birthday;
    std::string phone;
    int sex; // 0 for female, 1 for male
    std::string picture;
};

class RobotState: public base::singleton<RobotState>  {
public:
    RobotState();
    ~RobotState();

    void loadConfig(std::string& configPath);
    void saveConfig();

    void setEmergencyContacts(const std::vector<EmergencyContact>& contacts);
    void setFamilyMembers(const std::vector<FamilyMember>& members);

    PropertyBuilderByName(std::string, NetworkStatus, "");     //获取当前连接哪个wifi or data
    PropertyBuilderByName(int, UserConnectStatus, 0);      
    PropertyBuilderByName(std::string, UserPhoneNumber, "");          
    PropertyBuilderByName(std::string, ActionType, "");      
    PropertyBuilderByName(std::string, FlashStatus, "");
    PropertyBuilderByName(std::string, WifiName, "");
    PropertyBuilderByName(std::string, WifiSwitch, "");
    PropertyBuilderByName(std::string, MobileDataSwitch, "");
    PropertyBuilderByName(int, SceneMode, 0);
    PropertyBuilderByName(int, FlashBrightness, 0);
    PropertyBuilderByName(int, Volume, 0);
    PropertyBuilderByName(int, BatteryChargeStatus, 0);
    PropertyBuilderByName(int, BatteryLevel, 0);
    PropertyBuilderByName(long long, TimeStamp, 0);
    PropertyBuilderByName(std::string,  IntelligentSwitch, "");
    //定点移动任务类型  "throwGarbage" 丢垃圾  "fetchExpress" 取快递   "takePhotos" 拍照  "welcomeHome" 欢迎回家
    PropertyBuilderByName(std::string, MoveTaskType, "");
    PropertyBuilderByName(Json::Value, CurRobotPose, Json::Value());
    PropertyBuilderByName(Json::Value, MoveTaskPose, Json::Value());
    PropertyBuilderByName(Json::Value, MoveTaskPath, Json::Value());
    PropertyBuilderByName(std::string, DeviceId, "");
    // 智能播报相关
    PropertyBuilderByName(long, RemindId, 0);                            
    PropertyBuilderByName(std::string, SectionId, "");
    // 导航相关
    PropertyBuilderByName(std::string, MapId, ""); 
    PropertyBuilderByName(std::string, batchId, "");
    PropertyBuilderByName(std::string, FollowMeStatus, "off"); 
    PropertyBuilderByName(std::string, PathEventId, ""); 
    //状态相关
    PropertyBuilderByName(RobotStateEnum,CurrentState,RobotStateEnum::NORMAL)
    PropertyBuilderByName(int, RobdogStatus, 0);  // 机器狗当前的模式（宅家或者外出等）
    // 应急联系人
    PropertyBuilderByName(EmergencyContact, EmergencyContactInfo_, EmergencyContact());
    // 家庭成员列表
    PropertyBuilderByName(std::vector<FamilyMember>, FamilyMembers_, std::vector<FamilyMember>());

    PropertyBuilderByName(std::string, UwbTag, ""); 

    std::string getResourcePath(const std::string &file_name);

    const boost::filesystem::path getFolderPath() const {
        return folder_path_;
    }

    void setFolderPath(const boost::filesystem::path& path) {
        folder_path_ = path;
    }

private:
    std::string configPath;
    std::vector<EmergencyContact> EmergencyContactInfo_;
    std::vector<FamilyMember> FamilyMembers_; 
    RobotStateEnum currentState; // 当前状态

    boost::filesystem::path folder_path_;
};