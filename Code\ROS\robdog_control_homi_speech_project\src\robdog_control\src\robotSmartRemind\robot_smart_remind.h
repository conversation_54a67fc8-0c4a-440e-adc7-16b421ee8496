#pragma once
#include <bits/stdint-intn.h>
#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/pose.hpp>
#include <std_msgs/msg/string.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2_ros/transform_listener.h>
#include <tf2/LinearMath/Quaternion.h>

#include <homi_speech_interface/msg/sigc_event.hpp>
#include <homi_speech_interface/msg/robdog_action.hpp>
#include <homi_speech_interface/srv/sigc_data.hpp>
#include <homi_speech_interface/srv/net_ctrl.hpp>
#include <homi_speech_interface/msg/propriety_set.hpp>
#include <homi_speech_interface/msg/proper_to_app.hpp>
#include <homi_speech_interface/msg/continue_move.hpp>
#include <homi_speech_interface/msg/assistant_event.hpp>
#include <homi_speech_interface/msg/wakeup.hpp>
#include <homi_speech_interface/srv/assistant_speech_text.hpp>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/assistant_take_photo.hpp>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>

#include <xiaoli_com/deep_cmd.h>
#include <jsoncpp/json/json.h>
#include <fstream>
#include <unordered_set>
#include <homi_com/singleton.hpp>
#include "sensor_msgs/msg/nav_sat_fix.hpp"


#define START_MOVE 1
#define GO_HOME_MOVE 2
#define GO_START_MOVE 3
#define RESTART_MOVE 4


#define GOING    1
#define STOP     2
#define PAUSE    3
#define CANCLE   4

class RobdogCtrlNode;

class RobdogSmartRemindCtrl : public base::singleton<RobdogSmartRemindCtrl>  {
public:
    // RobdogCenter(rclcpp::Node::SharedPtr node, const std::string &configPath);
    RobdogSmartRemindCtrl();
    ~RobdogSmartRemindCtrl();
    void init(RobdogCtrlNode* node);

    // ********************** 其他函数
    void Proceationtype(int actiontype, int step);
    void checkStatusWatchdog();
    // void loadConfig(const std::string& configFilePath); 
    // void loadConfig();     
    void deep_ctl(int cmdID,int cmdValue); //灯光、语音、动作等交互
    void handle_UDP_data(char *data, size_t length);

    // ************************ 和定点移动有关的
    void playAudio (const std::string& filePath);
    bool isAtTarget (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void checkTargetStatus();
    void checkTargetStatus_brocast();
    void checkPatrolStatus(const Json::Value &jBody);
    void moveToTarget (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void moveToTargetAndPlayAudio (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void moveToPatrolPoint (const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void readMappingPoints();
    void updateMapPoints(const std::string& event, const Json::Value& points);
    int mapCodeToStatus(int code);
    std::string getCodeName(int code);
    std::string getDescription(int code);

    // ************************ 和定时器相关的操作
    void setTotalCount(int count); // 设置发送次数的函数
    void triggerTimerCallback(); // 公开的函数用于手动调用定时器回调
    void timerCallback();
    void timerRobotPoseCallback();
    void timerRobotPathCallback();
    void sleepForDuration();  // 暂停函数 double seconds
    // void startTimer();
    void heartbeatTimerCallback();
    void tripTimerCallback();

    // ************************ 和设备信息相关的
    void deepStatusCallback(const homi_speech_interface::msg::ProprietySet::SharedPtr msg);
    std::string get_robot_properties(const Json::Value &inValue);
    std::string get_connect_info_request(const Json::Value &inValue);
    void setProperties(const Json::Value& request);

    void targetPoseCallBack(const std_msgs::msg::String::SharedPtr msg);
    void sendRequestData(const std::string &data);

    // ************************ 和感知主机相关的操作
    void navPositionCallback(const geometry_msgs::msg::Pose::SharedPtr msg);
    void navStatusCallback(const std_msgs::msg::String::SharedPtr msg);
    void navStatusNotifyCallback(const std_msgs::msg::String::SharedPtr msg);
    void actionFollow(int status); //0-停止跟随 1-开启跟随 2-开启UWB跟随

    // ************************  和拍照有关
    void callHelperPhoto();
    void takePhotoService();

    // ************************ 智能播报相关
    void BrocastIfAbortCallBack(const homi_speech_interface::msg::AssistantEvent::SharedPtr msg);
    void SendBrocastCallback();
    void sendStringToBrocast(const std::string& message);
    void timerRobotBroadcastCallback(); 
    void RobotBroadcastStatusToPlat(int status);   
    void moveToTargetAndBrocast(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void CreatAndUpdate(const Json::Value &jBody);
    void RemindOnTime(const Json::Value &jBody);
    // ************************ 和平台消息有关的处理
    void handleTakePhotos();
    void handleDeliverExpress();
    void handleFetchExpress();
    void handleReportFamilyMovePoint();
    void handleParkPatrol();
    void handleCancelMovement();

    void handleGestRec(const Json::Value &jBody);
    void handleFollowMe(const Json::Value &jBody);
    void handleSportMode(const Json::Value &jBody);
    void handleMotorSkill(const Json::Value &jBody);
    void handleRLSkill(const Json::Value &jBody);
    void handleGeneralAction(const Json::Value &jBody);
    void handleSpecificAction(const Json::Value &jBody);
    void handleRobotAction(const Json::Value &jBody);
    void handleRobotMove(const Json::Value &jBody);
    void handleRobotView(const Json::Value &jBody);
    void handleAcccompany(const Json::Value &jBody);
    void handleTripStart(const Json::Value &jBody);
    void handleTripPause(const Json::Value &jBody);
    void handleTripCancel(const Json::Value &jBody);
    
    void handleModeSet(const Json::Value &jBody);
    void handlePropertiesWrite(const Json::Value &inValue);
    void handlePropertiesRead(const Json::Value &inValue);
    void handleFollowMeStatus(const Json::Value &inValue);
    void handleConnectInfoRequest(const Json::Value &inValue);
    void handleUserConnectChange(const Json::Value &jBody,const std::string& topic_name);
    void handleMapDraw(const Json::Value &jBody);
    void handleDataUpdate(const Json::Value &jBody);
    void handleMovePoints(const Json::Value &jBody);
    void handlePointReport(const Json::Value &jBody);
    void handleRemindOnTime(const Json::Value &jBody);
    void handleNavigationNotify(const Json::Value &jBody);
    void handleUnbindNotify(const Json::Value &jBody);
    void handleUnbindNotifyVoice(const Json::Value &jBody);
    void handleUserInteraction(const Json::Value &jBody);
    void handleFinishTask(const Json::Value &jBody);
    void handleVoiceResponseNluRaw(const Json::Value &jBody);
    void handleNavigationRequest(const Json::Value &jBody);
    void handleTripSimpleQuery(const Json::Value &jBody);
    void handleBindStatusQuery();
    void handleBindStatusResponse(const Json::Value &jBody);
    void actionNavigationResponse(const std::string msg3);
    void actionRobdogPose(const Json::Value &params);
    void handleEvent(const std::string &eventType, const Json::Value &jBody,  const Json::Value &value,const std::string &name);
    
    // ************************  接收到服务器数据后的异步回调
    void plat_srv_callback(rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedFuture response);
    // void plat_srv_callback(std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> response);
    void takephoto_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantTakePhoto>::SharedFuture response);
    void net_srv_callback(rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response);
    void brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response);

    // ************************  一些发布函数
    void publishVelocity(const geometry_msgs::msg::Twist::SharedPtr velocity); // 发布者功能：发布速度命令
    void publishAction(const homi_speech_interface::msg::RobdogAction::SharedPtr robdogAction);// 发布特定运动信息
    // void publishStatusCtrl(int cmd,int value,int exvalue,std::string::SharedPtr exmsg);
    void publishStatusCtrl(int cmd,int value,int exvalue);
    void publishProperties2APP();    

    // ************************ 处理平台消息的回调函数
    void robctrlCallback(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg,const std::string& topic_name);
    void robctrlCallback_past(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg);
    void update_emergency_contacts(int updateType,const std::string& entityId, const Json::Value& emergencyContact) ;
    // ************************ 处理APP消息的回调函数   
    //处理webSocket消息
    void parseWsActionMsg(Json::Value& valueMsg);
    // 休眠处理
    void resetInactivityTimer();
    void handleInactivityTimeout();
    void handleSleepTimeout();
    void activeDog();
    void updateLastActiveTime();
    //**************************告警
    void devAlarmReportCallback(const std_msgs::msg::String::SharedPtr msg);

    int64_t getTripId() { return tripId; };
    void setTripId(int64_t id) { tripId = id; };
    std::string getUserPhone() { return userPhone; };
    void setUserPhone(const std::string& phone) { userPhone = phone; };

    long long  getTripStatus() { return tripStatus; };
    void setTripStatus(int status) { tripStatus = status; };


    std::string getEventId() { return eventId; };
    void setEventId(const std::string& id) { eventId = id; };

    int getDeviceMode() { return deviceMode; };
    void setDeviceMode(int mode) { deviceMode = mode; };

    float getLatitude() { return latitude; };
    void setLatitude(float lat) { latitude = lat; };
    float getLongitude() { return longitude; };
    void setLongitude(float lon) { longitude = lon; };

    std::string getSingleMessage(const std::vector<std::string>& messages);
    void checkInternetConnectStatus(const std_msgs::msg::String::SharedPtr msg);
    void navSatStatusCallback(const sensor_msgs::msg::NavSatFix::SharedPtr msg);


private:
    RobdogCtrlNode* remind_ctrl_ = nullptr;
    // 步进次数
    int total_count_ = 0;  // 总共发送的次数
    int send_count_ = 0;   // 已发送的次数
    // 智能播报次数
    int brocast_send_count_ = 0;
    int brocast_total_count_ = 0;
    std::string brocast_text; // 当前智能播报的文本
    // 设置初始状态为未到达目标
    bool at_target_ = false;
    // 初始化为 false 表示尚未到达单个点位
    bool atSinglePoint = false;  
    // 设置初始状态为导航任务未被取消
    bool moveCancel_ = false;
    // 设置初始状态为建图节点未开启
    bool map_node = false;
    // bool isMapping_ = false; // 设置初始状态为不在建图
    // 初始化导航节点未开启
    bool Navigation_node = false;

    bool is_internet_connected_ = true;//默认设置网络已连接
    bool is_wifi_connected_ = true;
    int current_mode_ = 0;  //默认宅家模式
    bool rtk_status_flag = false;

    bool inactivity_mode_ = true; // true表示当前是亲密互动模式
    bool asked_in_last_hour_ = false; // true表示最近一小时内已经主动求陪伴
    bool quiet_for_three_hours_ = false; // true表示3小时内保持安静，不再主动求陪伴
    std::string uid; // 导航任务下发点位对应id
    geometry_msgs::msg::Twist current_twist_msg_;  // 保存的消息（要传给控制节点的速度【语音控制】）
    homi_speech_interface::msg::ContinueMove current_continue_msg_; // 要传给控制节点的持续移动信息【摇杆控制】
    rclcpp::Time last_heartbeat_time;  // 上次接收心跳的时间
    rclcpp::Time lastMoveMessageTime; // 更新最后收到action消息的时间
    
    rclcpp::Time last_active_time_; // 记录上次活动的时间
    rclcpp::Time last_asking_time_; // 记录上次主动求陪伴的时间
    std::time_t quiet_for_three_hours_until_;//用于判断距离上次主动求陪伴是否已超过3小时

    int64_t tripId = 0; // 用于记录行程id
    std::string userPhone; // 用于记录用户电话号码
    int tripStatus = 0; // 用于记录行程状态

    std::string eventId; // 用于记录事件id

    int deviceMode = 0;

    float latitude = 0;
    float longitude = 0;

    // ************************ 用到的yaml的数据
    double fspeed_x = 0.6;
    double fspeed_y = 0.6;
    double fspeed_z = 0.5; // 大约是15度
    int timer_interval = 1;
    double resting_time = 0.5;
    bool  move_status_flag = false;
    int  expresstion_count = 0;
    bool watchDogMonitor=false; // 启动监测

    // std::string robotdog_file_path = "/home/<USER>/.config/config_robotdog.xml";
	//   std::string map_points_path = "/home/<USER>/.config/map_points.json";

    std::map<std::string,int> sportModeMap={
        {"walk",DEEP_CMD_ACTION_WALK},
        {"run",DEEP_CMD_ACTION_RUN},
        {"stairClimbe",DEEP_CMD_ACTION_STAIR},
        {"climbe",DEEP_CMD_ACTION_CLIMBE},
        {"traction",DEEP_CMD_ACTION_WALK},
        {"emergencyStop",DEEP_CMD_ACTION_SOFTSTOP}
    };  //平台运动模式字段

    // 动作映射
    std::map<std::string, std::string> actionMap = {
        {"standUp", "standUp"},
        {"getDown", "getDown"},
        {"greeting", "greeting"},
        {"twistBody", "twistBody"},
        {"backflip", "backflip"},
        {"jumpForward", "jumpForward"},
        {"turnOver", "turnOver"},
        {"twistJump", "twistJump"},
        {"sitDown", "sitDown"},
        {"fingerHeart", "fingerHeart"},
        {"makeBow", "makeBow"},
        {"dance", "dance"},
        {"shakeBody", "shakeBody"},
        {"twistAss", "twistAss"},
        {"twistBody_emergency", "twistBody_emergency"},
        {"greeting_emergency", "greeting_emergency"},
        {"stretch","stretch"},
        {"chestOut","chestOut"},
        {"newYearCall","newYearCall"},
    };

    std::vector<std::string> actionVector = {
        "standUp",
        "getDown",
        "greeting",
        "twistBody",
        "backflip",
        "jumpForward",
        "turnOver",
        "twistJump",
        "sitDown",
        "fingerHeart",
        "makeBow",
        "dance",
        "shakeBody",
        "twistAss",
        "twistBody_emergency",
        "greeting_emergency"
    };
    
    // 订阅器
    rclcpp::Subscription<homi_speech_interface::msg::SIGCEvent>::SharedPtr platCmd_sub_;
    rclcpp::Subscription<homi_speech_interface::msg::SIGCEvent>::SharedPtr app_sub;
    rclcpp::Subscription<homi_speech_interface::msg::ProprietySet>::SharedPtr deepCtrl_sub_;
    rclcpp::Subscription<geometry_msgs::msg::Pose>::SharedPtr navPosition_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navStatus_sub_;
    rclcpp::Subscription<homi_speech_interface::msg::AssistantEvent>::SharedPtr brocast_sub;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr targetPose_sub;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr devAlarmRep_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr internet_connect_status_sub_;
    rclcpp::Subscription<sensor_msgs::msg::NavSatFix>::SharedPtr rtk_status_sub_;

    // 发布器
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr velCmd_pub;
    rclcpp::Publisher<homi_speech_interface::msg::RobdogAction>::SharedPtr actionCmd_pub;
    rclcpp::Publisher<homi_speech_interface::msg::ContinueMove>::SharedPtr continueMoveCmd_pub;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr actionPlanningMove_pub;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr mappingControl_pub;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publishVirtualWall;
    rclcpp::Publisher<homi_speech_interface::msg::ProperToApp>::SharedPtr prope2app_pub;
    rclcpp::Publisher<homi_speech_interface::msg::ProprietySet>::SharedPtr status_pub_;
    rclcpp::Publisher<homi_speech_interface::msg::Wakeup>::SharedPtr wake_pub_;

    // 服务器
    rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedPtr platform_client;
    rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedPtr app_client;
    rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedPtr net_client;
    rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedPtr brocast_client;
    rclcpp::Client<homi_speech_interface::srv::AssistantAbort>::SharedPtr brocast_abort_client;
    rclcpp::Client<homi_speech_interface::srv::SetWakeEvent>::SharedPtr set_wake_client;
    rclcpp::Client<homi_speech_interface::srv::IotControl>::SharedPtr iot_control_client_;
    rclcpp::Client<homi_speech_interface::srv::AssistantTakePhoto>::SharedPtr take_photo_client_;

    // 定时器
    rclcpp::TimerBase::SharedPtr ws_heartbeat_timer_;
    rclcpp::TimerBase::SharedPtr timer_2;
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::TimerBase::SharedPtr robPoseStatusTimer_;
    rclcpp::TimerBase::SharedPtr timer_brocast;
    rclcpp::TimerBase::SharedPtr timerDog;
    rclcpp::TimerBase::SharedPtr robMoveStatusTimer_;
    rclcpp::TimerBase::SharedPtr robMoveStatusTimer_brocast;
    rclcpp::TimerBase::SharedPtr robActionCheckTimer_;
    rclcpp::TimerBase::SharedPtr robPatrolStatusTimer_;
    rclcpp::TimerBase::SharedPtr robPathStatusTimer_;
    rclcpp::TimerBase::SharedPtr inactivity_timer_; // 10分钟定时器，用于检测是否需要主动求陪伴
    rclcpp::TimerBase::SharedPtr sleep_timer_;       // 5分钟定时器，用于检测是否进入休眠状态
    rclcpp::TimerBase::SharedPtr trip_timer_;       // 自主出行的定时器
};