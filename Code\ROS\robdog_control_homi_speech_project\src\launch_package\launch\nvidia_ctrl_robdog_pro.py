import os
import launch
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, IncludeLaunchDescription, TimerAction
from launch.substitutions import LaunchConfiguration
from launch_ros.substitutions import FindPackageShare
from launch_ros.actions import Node
from launch.actions import Register<PERSON>vent<PERSON><PERSON><PERSON>, OpaqueFunction
from launch.event_handlers import OnProcessExit
from ament_index_python.packages import get_package_share_directory
import yaml
from configparser import ConfigParser

conf = ConfigParser()
conf.read('/etc/cmcc_robot/cmcc_dev.ini')

def create_rtc_node(context):
    cmcc_rtc_config = os.path.join(
        get_package_share_directory("cmcc_rtc"),
        "config",
        "param.yaml"
    )

    with open(cmcc_rtc_config, 'r') as f:
        cmcc_rtc_params = yaml.safe_load(f)

    cmcc_rtc_params['device_info']['sn'] = conf.get('factory', 'devSn')
    cmcc_rtc_params['device_info']['cmei'] = conf.get('factory', 'devCmei')
    cmcc_rtc_params['device_info']['mac'] = conf.get('factory', 'macAddr')
    cmcc_rtc_params['login_params']['device_id'] = "cmcc-" + conf.get('factory', 'devType') + "-" + conf.get('factory', 'devSn')
    
    cmcc_rtc_node = Node(
        package="cmcc_rtc",
        namespace="cmcc_rtc",
        name="cmcc_rtc_node",
        executable="cmcc_rtc_node",
        parameters=[
            cmcc_rtc_params,
            {"video_namespace": "/video_gst_nv",
             "forceIDR": True}
        ]
    )

    rtc_restart = RegisterEventHandler(
        event_handler=OnProcessExit(
            target_action=cmcc_rtc_node,
            on_exit=[
                LogInfo(msg='cmcc_rtc_restart_node restart'),
                OpaqueFunction(function=create_rtc_node)
            ]
        )
    )

    return [cmcc_rtc_node, rtc_restart]

def generate_launch_description():
    config_share_dir = get_package_share_directory('launch_package')
    DeclareLaunchArgument('angle_threshold', default_value='30', description='angle_threshold value'),

    homi_ws_server = Node(
        package="nvidia_control",
        executable="homi_ws_server",
        output='log',  
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
    )

    nvidia_control = TimerAction(
        #delay 1s
        period=1.0,  
        # output='log',  
        actions=[ Node(
            package="nvidia_control",
            executable="nvidia_control",
            parameters=[config_share_dir + '/configs/robot_config.yaml'],
        )]
    )

    follow_rcs = Node(
        package="follow_rcs",
        executable="follow_rcs",
        output='log',  
        parameters=[config_share_dir + '/configs/follow_config.yaml'],
    )
    
    control_catch_node = Node(
        package="follow_strategy",
        executable="control_catch_turtle_v1",
        output='log',  
        name="control_catch_turtle_v1"
    )

    target_twist_estimate_node = Node(
        package="follow_strategy",
        executable="target_twist_estimate",
        output='log',  
        name="target_twist_estimate"
    )

    pixel2pose_node = Node(
        package="pixel2world_pose",
        executable="pixel",
        output='log',  
        name="pixel"
    )

    video_config = os.path.join(
        get_package_share_directory("video_gst_nv"),
        "config",
        "param.yaml"
    )
    script_path = os.path.join(
        get_package_share_directory("video_gst_nv"),
        "script",
        "find_camera.sh"
    )
    video_gst_node = Node(
        package="video_gst_nv",
        namespace="video_gst_nv",
        name="video_gst_nv_node",
        executable="video_gst_nv_node",
        parameters=[
            video_config,
            {"script_path": script_path}
        ]
    )

    cmcc_rtc_config = os.path.join(
        get_package_share_directory("cmcc_rtc"),
        "config",
        "param.yaml"
    )

    with open(cmcc_rtc_config, 'r') as f:
        cmcc_rtc_params = yaml.safe_load(f)

    cmcc_rtc_params['device_info']['sn'] = conf.get('factory', 'devSn')
    cmcc_rtc_params['device_info']['cmei'] = conf.get('factory', 'devCmei')
    cmcc_rtc_params['device_info']['mac'] = conf.get('factory', 'macAddr')
    cmcc_rtc_params['login_params']['device_id'] = "cmcc-" + conf.get('factory', 'devType') + "-" + conf.get('factory', 'devSn')

    cmcc_rtc_node = Node(
        package="cmcc_rtc",
        namespace="cmcc_rtc",
        name="cmcc_rtc_node",
        executable="cmcc_rtc_node",
        parameters=[
            cmcc_rtc_params,
            {"video_namespace": "/video_gst_nv",
             "forceIDR": True}
        ]
    )

    rtc_restart = RegisterEventHandler(
        event_handler=OnProcessExit(
            target_action=cmcc_rtc_node,
            on_exit=[
                LogInfo(msg='cmcc_rtc_node restart'),
                OpaqueFunction(function=create_rtc_node)
            ]
        )
    )
    
    return LaunchDescription([
            homi_ws_server,
            nvidia_control,
            follow_rcs,
            control_catch_node,
            target_twist_estimate_node,
            pixel2pose_node,
            video_gst_node,
            cmcc_rtc_node,
            rtc_restart
    ])
