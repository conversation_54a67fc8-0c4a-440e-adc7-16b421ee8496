#ifndef __HOMI_TASK_STATUS_CONTAINER_H__
#define __HO_MI_TASK_STATUS_CONTAINER_H__
#include <chrono>
#include <memory>
#include <mutex>
#include <unordered_map>


class HomiTaskStatusContainer
{
public:
    struct KeyValue {
        std::chrono::steady_clock::time_point expirationTime;
    };
    // 设置键值对，带过期时间（毫秒）
    void set(const std::string& key, unsigned int milliseconds = 0)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        KeyValue kv;
        if(milliseconds!=0)
            kv.expirationTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(milliseconds);
        else
            kv.expirationTime = std::chrono::steady_clock::time_point{};
        data_[key] = std::move(kv);
    }
    bool isExpired()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        for (auto& kv : data_) {
            if (_isExpired(kv.second.expirationTime)) {
                return true;
            }
        }
        return false;
    }
    // 删除键值对
    void del(const std::string& key)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        data_.erase(key);
    }
    std::string get()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        const std::string emptyStr = R"({"voiceConfig":{}})";
        try
        {
            auto obj = nlohmann::json();
            auto jsonObj= nlohmann::json::array();
            for (auto& kv : data_) {
                if (!_isExpired(kv.second.expirationTime)) {
                    jsonObj.push_back(nlohmann::json{{"taskName", kv.first}});
                }
                else
                {
                    data_.erase(kv.first);
                }
            }  
            if(jsonObj.empty())   
                return emptyStr;
            obj["voiceConfig"]["executingTasks"] = jsonObj;   
            return obj.dump();
        }
        catch(const std::exception& e)
        {
            return emptyStr;
        }
    }
private:
    std::unordered_map<std::string, KeyValue> data_;
    std::mutex mutex_;

    // 检查是否过期
    bool _isExpired(const std::chrono::steady_clock::time_point& expirationTime) {
        return (std::chrono::steady_clock::now() >= expirationTime && expirationTime!=std::chrono::steady_clock::time_point{} );
    }
};
#endif // __HOMI_TASK_STATUS_CONTAINER_H__