/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: RequestIdentity_.idl
  Source: RequestIdentity_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_REQUESTIDENTITY__HPP
#define DDSCXX_REQUESTIDENTITY__HPP

#include <cstdint>

namespace unitree_api
{
namespace msg
{
namespace dds_
{
class RequestIdentity_
{
private:
 int64_t id_ = 0;
 int64_t api_id_ = 0;

public:
  RequestIdentity_() = default;

  explicit RequestIdentity_(
    int64_t id,
    int64_t api_id) :
    id_(id),
    api_id_(api_id) { }

  int64_t id() const { return this->id_; }
  int64_t& id() { return this->id_; }
  void id(int64_t _val_) { this->id_ = _val_; }
  int64_t api_id() const { return this->api_id_; }
  int64_t& api_id() { return this->api_id_; }
  void api_id(int64_t _val_) { this->api_id_ = _val_; }

  bool operator==(const RequestIdentity_& _other) const
  {
    (void) _other;
    return id_ == _other.id_ &&
      api_id_ == _other.api_id_;
  }

  bool operator!=(const RequestIdentity_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_api::msg::dds_::RequestIdentity_>::getTypeName()
{
  return "unitree_api::msg::dds_::RequestIdentity_";
}

template <> constexpr bool TopicTraits<::unitree_api::msg::dds_::RequestIdentity_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::RequestIdentity_>::type_map_blob_sz() { return 266; }
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::RequestIdentity_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::RequestIdentity_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x4b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0x8f,  0x78,  0x20,  0x91,  0xd8,  0x0b,  0x89, 
 0x0d,  0xad,  0xf3,  0xe7,  0xd6,  0x60,  0xe6,  0x00,  0x33,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0xb8,  0x0b,  0xb7,  0x74,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0xd3,  0x3a,  0xff,  0x5d,  0x00, 
 0x8d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0xf8,  0x8b,  0x48,  0x81,  0xc6,  0xcf,  0x5a, 
 0x29,  0xdc,  0xd6,  0x89,  0x3c,  0xdd,  0x22,  0x00,  0x75,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x31,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x29,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x61,  0x70,  0x69,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64, 
 0x73,  0x5f,  0x3a,  0x3a,  0x52,  0x65,  0x71,  0x75,  0x65,  0x73,  0x74,  0x49,  0x64,  0x65,  0x6e,  0x74, 
 0x69,  0x74,  0x79,  0x5f,  0x00,  0x00,  0x00,  0x00,  0x35,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x69,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x15,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x05,  0x00,  0x07,  0x00,  0x00,  0x00,  0x61,  0x70,  0x69,  0x5f,  0x69,  0x64,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0xf8,  0x8b,  0x48, 
 0x81,  0xc6,  0xcf,  0x5a,  0x29,  0xdc,  0xd6,  0x89,  0x3c,  0xdd,  0x22,  0xf1,  0x8f,  0x78,  0x20,  0x91, 
 0xd8,  0x0b,  0x89,  0x0d,  0xad,  0xf3,  0xe7,  0xd6,  0x60,  0xe6, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::RequestIdentity_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x8f,  0x78,  0x20,  0x91,  0xd8,  0x0b,  0x89,  0x0d,  0xad,  0xf3,  0xe7, 
 0xd6,  0x60,  0xe6,  0x00,  0x37,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0xf8,  0x8b,  0x48,  0x81,  0xc6,  0xcf,  0x5a,  0x29,  0xdc,  0xd6,  0x89, 
 0x3c,  0xdd,  0x22,  0x00,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_api::msg::dds_::RequestIdentity_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_api::msg::dds_::RequestIdentity_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_api::msg::dds_::RequestIdentity_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_api::msg::dds_::RequestIdentity_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_api::msg::dds_::RequestIdentity_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.api_id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_api::msg::dds_::RequestIdentity_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestIdentity_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_api::msg::dds_::RequestIdentity_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.api_id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_api::msg::dds_::RequestIdentity_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestIdentity_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_api::msg::dds_::RequestIdentity_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.api_id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_api::msg::dds_::RequestIdentity_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestIdentity_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_api::msg::dds_::RequestIdentity_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.api_id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_api::msg::dds_::RequestIdentity_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestIdentity_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_REQUESTIDENTITY__HPP
