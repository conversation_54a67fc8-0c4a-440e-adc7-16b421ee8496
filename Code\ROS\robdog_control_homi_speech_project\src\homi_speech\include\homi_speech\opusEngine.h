#ifndef __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_OPUSENGINE_H_
#define __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_OPUSENGINE_H_
#include <homi_sdk/AudioCode.h>
namespace homi::app{
    class IOpusEngine
    {
    public:
        virtual std::shared_ptr<IAudioDecode> createAudioDecode(const AudioCodeConfig &config) = 0;
        virtual std::shared_ptr<IAudioEncode> createAudioEncode(const AudioCodeConfig &config) = 0;
        virtual ~IOpusEngine() = default;
    };
    std::shared_ptr<IOpusEngine> createOpusEngine();
}

#endif  // __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_OPUSENGINE_H_
