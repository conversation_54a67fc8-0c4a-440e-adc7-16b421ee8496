#include "public/tools.h"
#include "robotMgr/robot_info_mgr.h" // 为了读取状态
#include "robdog_ctrl_node.h"
#include <shared_mutex>

bool initCheck = false;
bool avoidFlag = true;
bool autoRecover = true;


double last_x = 0.0;
double last_y = 0.0;
double last_yaw = 0.0;
double smooth = 1;

std::shared_mutex typeMutex;
std::shared_mutex gaitMutex;
std::shared_mutex motionMutex;

RobdogCtrlType g_CtrlType = ROBDOGCTRL_TYPE_INVALID;           // 控制指令类型
RobdogCtrlGait g_MoveGait = ROBDOGCTRL_GAIT_AICLASSIC;             // 步态信息
RobdogCtrlMotion g_Locomotion = ROBDOGCTRL_MOTION_INVALID;     // 动作信息

// 读控制指令类型
RobdogCtrlType RobDog_Ctrl_Unitree::rdCtrlType()
{
    std::shared_lock<std::shared_mutex> lock(typeMutex);

    return g_CtrlType;
}

// 写控制指令类型
void RobDog_Ctrl_Unitree::wrCtrlType(RobdogCtrlType type)
{
    std::unique_lock<std::shared_mutex> lock(typeMutex);

    g_CtrlType = type;

    return;
}

// 读步态信息
RobdogCtrlGait RobDog_Ctrl_Unitree::rdCtrlGait()
{
    std::shared_lock<std::shared_mutex> lock(gaitMutex);

    return g_MoveGait;
}

// 写步态信息
void RobDog_Ctrl_Unitree::wrCtrlGait(RobdogCtrlGait gait)
{
    std::unique_lock<std::shared_mutex> lock(gaitMutex);

    g_MoveGait = gait;

    return;
}

// 读动作信息
RobdogCtrlMotion RobDog_Ctrl_Unitree::rdCtrlMotion()
{
    std::shared_lock<std::shared_mutex> lock(motionMutex);

    return g_Locomotion;
}

// 写动作信息
void RobDog_Ctrl_Unitree::wrCtrlMotion(RobdogCtrlMotion motion)
{
    std::unique_lock<std::shared_mutex> lock(motionMutex);

    g_Locomotion = motion;

    return;
}

// 等待与运控板通信正常
bool RobDog_Ctrl_Unitree::utConnectWait()
{
    int cnt = 0;

    while ((RobotInfoMgr::getInstance().getUtRobotStatus() == UT_ROBDOG_STATUS_STATE) && (cnt < 60))
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        cnt++;
        RCLCPP_INFO(node_ctrl_->get_logger(), "Wait Connect: %d", cnt);
    }

    if (cnt == 60)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Sport Control Board Connection Error.");
        return false;
    }

    RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(), *node_ctrl_->get_clock(), 3000, "Sport Control Board Connected.");

    return true;
}

// 等待站立动作完成
bool RobDog_Ctrl_Unitree::utStandUpWait()
{
    int cnt = 0;

    while((false == RobotInfoMgr::getInstance().utStandCheck()) && (cnt < 60))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Wait Stand: %d", cnt);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        cnt++;
    }

    if (cnt == 60)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Not Stand Up.");
        return false;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Stand Up Now.");

    return true;
}

// 检查步态是否与用户设置一致
bool RobDog_Ctrl_Unitree::utGaitCheck()
{
    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;
    int ret = 0;
    RobdogCtrlGait gait;

    // 获取步态设置信息
    gait = rdCtrlGait();

    // 获取状态信息
    enLastStatus = RobotInfoMgr::getInstance().getUtRobotStatus();

    // 检查设置步态（常规步行）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_STATIC_WALK) && 
        (gait == ROBDOGCTRL_GAIT_WALK || gait == ROBDOGCTRL_GAIT_EXIT))
    {
        return true;
    }

    // 检查设置步态（常规跑步）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_TROT_RUN) && 
        (gait == ROBDOGCTRL_GAIT_RUN))
    {
        return true;
    }

    // 检查设置步态（经典）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_CLASSIC) && 
        (gait == ROBDOGCTRL_GAIT_AICLASSIC))
    {
        return true;
    }

    // 检查设置步态（灵动）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_FREE_WALK) && 
        (gait == ROBDOGCTRL_GAIT_AINIMBLE))
    {
        return true;
    }

    if ((enLastStatus == UT_ROBDOG_STATUS_FREE_JUMP) && 
        (gait == ROBDOGCTRL_GAIT_FREEJUMP))
    {
        return true;
    }

    if ((enLastStatus == UT_ROBDOG_STATUS_FREE_BOUND) && 
        (gait == ROBDOGCTRL_GAIT_FREEBOUND))
    {
        return true;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Gait State Not Match With User Set.");

    return false;
}

#if 0
// 更新避障状态
int32_t RobDog_Ctrl_Unitree::utAvoidUpdate()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    bool enable = false;
    int32_t attempt = 0;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 避免超时问题，多获取几次，不能在主线程中调用，避免阻塞
    ret = ov_client.SwitchGet(enable);
    while ((ret == UT_ERROR_TIMEOUT) && (attempt < UT_AVOID_MAX_ATTEMPT)) 
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "obAvoid get wait: %d!", attempt);
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        attempt++;

        ret = ov_client.SwitchGet(enable);
    }

    // 多次获取失败，退出
    if ((ret != ROBDOGCTRL_ERROR_SUCCESS) || (attempt == UT_AVOID_MAX_ATTEMPT)) 
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "obAvoid get failed. ret=%d, attempt=%d", ret, attempt);
        return ret;
    }

    // 更新全局记录
    avoidFlag = enable;

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 设置避障开关
int32_t RobDog_Ctrl_Unitree::utAvoidSet(bool enable)           
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    int32_t attempt = 0;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 避免超时问题，多设置几次，不能在主线程中调用，避免阻塞
    ret = ov_client.SwitchSet(enable);
    while ((UT_ERROR_TIMEOUT == ret) && (attempt < UT_AVOID_MAX_ATTEMPT))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "obAvoid set wait: %d!", attempt);
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        attempt++;

        ret = ov_client.SwitchSet(enable);
    }

    // 多次设置失败，退出
    if ((ret != ROBDOGCTRL_ERROR_SUCCESS) || (attempt == UT_AVOID_MAX_ATTEMPT))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "obAvoid set failed. ret=%d, attempt=%d", ret, attempt);
        return ret;
    }

    // 更新全局记录
    avoidFlag = enable;

    return ROBDOGCTRL_ERROR_SUCCESS;
}
#endif

// 起立
int32_t RobDog_Ctrl_Unitree::utStandUpProc()
{
    std::string form;
    std::string name;
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    if (true == RobotInfoMgr::getInstance().utSitCheck())
    {
        // 从坐下状态恢复到平衡站立
        ret = sport_client.RiseSit();
        if (ret != 0)
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "RiseSit Action failed! ret = %d", ret);
            return ret;
        }
    }
    else
    {
        // 正在执行动作，退出
        if (true == RobotInfoMgr::getInstance().utLocomotionCheck())
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion Now! Can not Stand Up");
            return ROBDOGCTRL_ERROR_INVALID_STATE;
        }

        // 如果狗子在运动状态，先停止运动
        if (true == RobotInfoMgr::getInstance().utMoveCheck())
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Moving Status, Stop Move!");
            robdogCtrl_StopMove();
            std::this_thread::sleep_for(std::chrono::milliseconds(400));
        }

        // 静止站立状态，无需重复站立
        if (false == RobotInfoMgr::getInstance().utStandCheck())
        {
            // 从翻倒或趴下状态恢复至平衡站立状态。不论是否翻倒，都会恢复至站立。
            RCLCPP_INFO(node_ctrl_->get_logger(), "Recover Stand Up!");
            ret = sport_client.RecoveryStand();
            if (ret != ROBDOGCTRL_ERROR_SUCCESS)
            {
                RCLCPP_ERROR(node_ctrl_->get_logger(), "RecoveryStand Action failed! ret = %d", ret);
                return ret;
            }
        }
    }

    // 狗子趴下站起来默认为灵动模式
    // 常规模式下，做完动作，默认为平衡站立，行走为原步态（趴下站起来除外）
    // ai步态下，做完动作，默认为平衡站立，行走为灵动步态（趴下站起来除外）

    // 检查是否站立成功
    if (false == utStandUpWait())
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Wait Standup Error");
    }

    if(!initCheck)
    {
        // 设置速度
        RCLCPP_INFO(node_ctrl_->get_logger(), "SpeedLevel 1!");
        ret = sport_client.SpeedLevel(1);
        if (ret != ROBDOGCTRL_ERROR_SUCCESS)
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "Speed Level Set failed! ret = %d", ret);
        }

        initCheck = true;
    }

    // 常规和ai步态下，趴下站起来，修改为原步态
    ret = utChangeGaitProc();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "utChangeGaitProc failed! ret = %d", ret);
        return ret;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Standup Proc Success!");

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 趴下
int32_t RobDog_Ctrl_Unitree::utGetDownProc()              
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    // 已经趴下
    if (true == RobotInfoMgr::getInstance().utStandDownCheck())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Already Stand Down!");
        return ROBDOGCTRL_ERROR_SUCCESS;
    }

    // 正在运动，或者做动作，不执行趴下
    if ((true == RobotInfoMgr::getInstance().utMoveCheck()) || (true == RobotInfoMgr::getInstance().utLocomotionCheck()))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Invalid State: Moving or Locomotion!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 非站立状态，先站立
    ret = utStandUpProc();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Stand Up failed! ret = %d", ret);
        return ret;
    }

    // 趴下
    ret = sport_client.StandDown();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "StandDown Action failed! ret = %d", ret);
        return ret;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "StandDown Proc Success!");
    
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 坐下
int32_t RobDog_Ctrl_Unitree::utSitProc()       	        
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    // 已坐下，无需处理
    if (true == RobotInfoMgr::getInstance().utSitCheck())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Already Sit!");
        return ROBDOGCTRL_ERROR_SUCCESS;
    }

    // 正在运动，或者做动作，不执行坐下
    if (true == RobotInfoMgr::getInstance().utLocomotionCheck() || true == RobotInfoMgr::getInstance().utMoveCheck())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Invalid State: Moving or Locomotion!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 非站立状态，先站立
    ret = utStandUpProc();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Stand Up failed! ret = %d", ret);
        return ret;
    }

    // 坐下
    ret = sport_client.Sit();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Sit Action failed! ret = %d", ret);
        return ret;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Sit Proc Success!");

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 动作处理函数
int32_t RobDog_Ctrl_Unitree::utLocomotionProc()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    RobdogCtrlMotion motion = ROBDOGCTRL_MOTION_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    // 获取用户下发动作
    motion = rdCtrlMotion();

    // 无效或者不支持动作，直接返回
    if ((motion == ROBDOGCTRL_MOTION_INVALID) || (motion == ROBDOGCTRL_MOTION_TURNOVER) ||
        (motion == ROBDOGCTRL_MOTION_TWISTJUMP) || (motion == ROBDOGCTRL_MOTION_BACKFLIP))
    {
        wrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
        return ROBDOGCTRL_ERROR_SUCCESS;
    }

    //正在做动作或运动中
    if ((true == RobotInfoMgr::getInstance().utLocomotionCheck()) || (true == RobotInfoMgr::getInstance().utMoveCheck()))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Already Locomotion or Moving!");
        wrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 非站立状态，先站立
    ret = utStandUpProc();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Stand Up failed! ret = %d", ret);
        wrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
        return ret;
    }

    //做动作
    switch(motion)
    {
        case ROBDOGCTRL_MOTION_TWIST:            //扭身体  -->开心
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Twist!");
            ret = sport_client.Content();
            break;
        }
        case ROBDOGCTRL_MOTION_DANCE:            // 跳舞
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Dance!");
            ret = sport_client.Dance1();
            break;
        }
        case ROBDOGCTRL_MOTION_FINGERHEART:      //比心
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Finger Heart!");
            ret = sport_client.Heart();
            break;
        }
        case ROBDOGCTRL_MOTION_HELLO:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Hello!");
            ret = sport_client.Hello();          //打招呼
            break;
        }
        case ROBDOGCTRL_MOTION_STRETCH:          //伸懒腰
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Stretch!");
            ret = sport_client.Stretch();
            break;
        }
        case ROBDOGCTRL_MOTION_NEWYEARCALL:      //拜年
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: New Year Call!");
            ret = sport_client.Scrape();
            break;
        }
        case ROBDOGCTRL_MOTION_HAPPY:            //开心
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Happy!");
            ret = sport_client.Content();
            break;
        }
        case ROBDOGCTRL_MOTION_JUMPFORWARD:      //向前跳
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Jump Forward!");
            ret = sport_client.FrontJump();
            break;
        }
        case ROBDOGCTRL_MOTION_LEAP:             //向前扑人
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Front Pounce!");
            ret = sport_client.FrontPounce();
            break;
        }
        case ROBDOGCTRL_MOTION_DANCEV2:          //跳舞2
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Dance v2!");
            ret = sport_client.Dance2();
            break;
        }
        case ROBDOGCTRL_MOTION_WALKUPRIGHT:       //站立
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Walk Upright!");
            ret = sport_client.WalkUpright(true);
            std::this_thread::sleep_for(std::chrono::milliseconds(3000));
            ret = sport_client.WalkUpright(false);
            break;
        }
        case ROBDOGCTRL_MOTION_HANDSTAND:         //倒立
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Hand Stand!");
            ret = sport_client.HandStand(true);
            std::this_thread::sleep_for(std::chrono::milliseconds(3000));
            ret = sport_client.HandStand(false);
            break;
        }
        default:
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "Invalid Sport Motion! motion = %d", motion);
            wrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
            return ROBDOGCTRL_ERROR_INVALID_PARA;
        }
    }

    wrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Local Motion failed! ret = %d, motion=%d", ret, motion);
        return ret;
    }

    // 检查是否站立成功
    if (false == utStandUpWait())
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Wait Standup Error");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 常规和ai步态下，执行完动作，修改为原步态
    ret = utChangeGaitProc();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "utChangeGaitProc failed! ret = %d", ret);
        return ret;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion Proc Success!");

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 步态切换处理
int32_t RobDog_Ctrl_Unitree::utChangeGaitProc()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    RobdogCtrlGait gait;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    // 趴下、坐下或者执行动作，不能切换步态
    if ((true == RobotInfoMgr::getInstance().utStandDownCheck()) || 
        (true == RobotInfoMgr::getInstance().utSitCheck()) || 
        (true == RobotInfoMgr::getInstance().utLocomotionCheck()))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Stand Down, Sit or Locomotion. Can not Change Gait!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 获取用户设置步态信息
    gait = rdCtrlGait();
    if (gait == ROBDOGCTRL_GAIT_INVALID)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Invalid Gait!");
        return ROBDOGCTRL_ERROR_INVALID_PARA;
    }

    // 检查当前步态和设置步态是否一致
    if (true == utGaitCheck())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Gait Match!");
        return ROBDOGCTRL_ERROR_SUCCESS;
    }

    switch(gait)
    {
        case ROBDOGCTRL_GAIT_WALK:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change Walk Gait!");
            ret = sport_client.StaticWalk();
            break;
        }
        case ROBDOGCTRL_GAIT_RUN:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change Run Gait!");
            ret = sport_client.TrotRun();
            break;
        }
        case ROBDOGCTRL_GAIT_AICLASSIC:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change AI Classic Gait!");
            ret = sport_client.ClassicWalk(true);
            break;
        }
        case ROBDOGCTRL_GAIT_AINIMBLE:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change AI Flex Gait!");
            ret = sport_client.FreeWalk();
            break;
        }
        case ROBDOGCTRL_GAIT_FREEBOUND:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change FreeBound Gait!");
            ret = sport_client.FreeBound(true);
            break;
        }
        case ROBDOGCTRL_GAIT_FREEJUMP:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change FreeJump Gait!");
            ret = sport_client.FreeJump(true);
            break;
        }
        case ROBDOGCTRL_GAIT_EXIT:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change exit ai Gait!");
            ret = sport_client.StaticWalk();
            break;
        }
        default:
        {
            // 步态信息有误，强制修改为默认步态
            RCLCPP_ERROR(node_ctrl_->get_logger(), "Invalid Gait! gait=%d", gait);
            wrCtrlGait(ROBDOGCTRL_GAIT_AICLASSIC);
            return ROBDOGCTRL_ERROR_INVALID_PARA;
        }
    }

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Change Gait failed! ret = %d, gait=%d", ret, gait);
        return ret;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Change Gait Proc Success!");

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 宇树线程处理函数
int32_t RobDog_Ctrl_Unitree::utThreadProc()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    RobdogCtrlType ctrlType = ROBDOGCTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    while(1)
    {
        // 等待运控板状态信息更新
        if (false == utConnectWait())
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "utThreadProc: Wait Connect Error");
            continue;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        ret = ROBDOGCTRL_ERROR_SUCCESS;
        
        // 获取指令类型
        ctrlType = rdCtrlType();
        RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(), *node_ctrl_->get_clock(), 3000, "===== Action: ctrlType %d =====", ctrlType);

        // 调用处理函数
        switch (ctrlType)
        {
            case ROBDOGCTRL_TYPE_STANDUP:
                ret = utStandUpProc();
                break;
            case ROBDOGCTRL_TYPE_GETDOWN:
                ret = utGetDownProc();
                break;
            case ROBDOGCTRL_TYPE_SIT:
                ret = utSitProc();
                break;
            case ROBDOGCTRL_TYPE_LOCOMOTION:
                ret = utLocomotionProc();
                break;
            case ROBDOGCTRL_TYPE_CHANGE_GAIT:
                ret = utChangeGaitProc();
                break;
            default:
                //do nothing
                break;
        }

        // 错误检查
        if (ret != ROBDOGCTRL_ERROR_SUCCESS)
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree Control Thread Proc Error. ctrlType = %d, ret = %d", ctrlType, ret);
        }

        // 清零，等待新指令
        wrCtrlType(ROBDOGCTRL_TYPE_INVALID);
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 起立
int32_t RobDog_Ctrl_Unitree::robdogCtrl_StandUp()              	
{
    RobdogCtrlType ctrlType = ROBDOGCTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 检查是否有其他动作在执行
    ctrlType = rdCtrlType();
    if (ctrlType != ROBDOGCTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入站立动作
    wrCtrlType(ROBDOGCTRL_TYPE_STANDUP);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 趴下
int32_t RobDog_Ctrl_Unitree::robdogCtrl_GetDown()              
{
    RobdogCtrlType ctrlType = ROBDOGCTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 检查是否有其他动作在执行
    ctrlType = rdCtrlType();
    if (ctrlType != ROBDOGCTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入动作
    wrCtrlType(ROBDOGCTRL_TYPE_GETDOWN);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 坐下
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Sit()       	        
{
    RobdogCtrlType ctrlType = ROBDOGCTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 检查是否有其他动作在执行
    ctrlType = rdCtrlType();
    if (ctrlType != ROBDOGCTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入动作
    wrCtrlType(ROBDOGCTRL_TYPE_SIT);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 做动作
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Locomotion(RobdogCtrlMotion motion)
{
    RobdogCtrlType ctrlType = ROBDOGCTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 检查是否有其他动作在执行
    ctrlType = rdCtrlType();
    if (ctrlType != ROBDOGCTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入动作
    wrCtrlMotion(motion);
    wrCtrlType(ROBDOGCTRL_TYPE_LOCOMOTION);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 切换步态
int32_t RobDog_Ctrl_Unitree::robdogCtrl_ChangeGait(RobdogCtrlGait gait)           	    
{
    RobdogCtrlType ctrlType = ROBDOGCTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 检查是否有其他动作在执行
    ctrlType = rdCtrlType();
    if (ctrlType != ROBDOGCTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入步态
    wrCtrlGait(gait);
    wrCtrlType(ROBDOGCTRL_TYPE_CHANGE_GAIT);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 语音指令（起立/趴下）
int32_t RobDog_Ctrl_Unitree::robdogCtrl_VoiceStand(int32_t cmd)        	
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    if (cmd == 1)
    {
        ret = robdogCtrl_StandUp();
    }
    else if (cmd ==2)
    {
        ret = robdogCtrl_Sit();
    }

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "VoiceStand Set Failed! ret = %d, cmd = %d", ret, cmd);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 持续运动
int32_t RobDog_Ctrl_Unitree::robdogCtrl_ContinueMove(CtrlMoveData *pMoveData)
{
    double mvX = 0;
    double mvY = 0;
    double mvYaw = 0;

    if ((pMoveData->x != 0) || (pMoveData->y != 0) || (pMoveData->yaw != 0))
    {
        RobdogCtrlGait gait_now;
        gait_now = rdCtrlGait();
        if (gait_now == ROBDOGCTRL_GAIT_FREEBOUND || gait_now == ROBDOGCTRL_GAIT_FREEJUMP)
        {
            mvX = (pMoveData->x == 0) ? 0 : ((pMoveData->x > 0) ? UT_MOVE_X_HIGH_JUMP : UT_MOVE_X_LOW_JUMP);
            mvY = (pMoveData->y == 0) ? 0 : ((pMoveData->y > 0) ? UT_MOVE_Y_HIGH_JUMP : UT_MOVE_Y_LOW_JUMP);
            mvYaw = (pMoveData->yaw == 0) ? 0 : ((pMoveData->yaw > 0) ? UT_MOVE_YAW_LOW_JUMP : UT_MOVE_YAW_HIGH_JUMP);
        }
        else
        {
            mvX = (pMoveData->x == 0) ? 0 : ((pMoveData->x > 0) ? UT_MOVE_X_HIGH : UT_MOVE_X_LOW);
            mvY = (pMoveData->y == 0) ? 0 : ((pMoveData->y > 0) ? UT_MOVE_Y_HIGH : UT_MOVE_Y_LOW);
            mvYaw = (pMoveData->yaw == 0) ? 0 : ((pMoveData->yaw > 0) ? UT_MOVE_YAW_LOW : UT_MOVE_YAW_HIGH);
        }

        robdogCtrl_Move(mvX, mvY, mvYaw);
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 运动接口
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Move(double x, double y, double yaw)
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    RobdogCtrlType ctrlType = ROBDOGCTRL_TYPE_INVALID;

    // 检查是否有其他动作在执行
    ctrlType = rdCtrlType();
    if (ctrlType != ROBDOGCTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    ret = utChangeGaitProc();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "utChangeGaitProc failed! ret = %d", ret);
        return ret;
    }

    // 平滑处理
    last_x = last_x * (1 - smooth) + x * smooth;
    last_y = last_y * (1 - smooth) + y * smooth;
    last_yaw = last_yaw * (1 - smooth) + yaw * smooth;

    if (avoidFlag)
    {
        // 避障模式下的移动控制，默认关闭避障，暂时均使用运控sport_client接口
        RCLCPP_INFO(node_ctrl_->get_logger(), "avoid true, x: %f, y: %f, z: %f", last_x, last_y, last_yaw);
        // ret = ov_client.Move(last_x, last_y, last_yaw);
        ret = sport_client.Move(last_x, last_y, last_yaw);
    }
    else
    {
        //常规模式下，为了稳定增加了点滤波或者踏步
        RCLCPP_INFO(node_ctrl_->get_logger(), "avoid false, x: %f, y: %f, z: %f", last_x, last_y, last_yaw);
        ret = sport_client.Move(last_x, last_y, last_yaw);
    }

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Move Action failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Unitree::robdogCtrl_StopMove()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    if (avoidFlag)
    {
        // 避障模式下的移动控制，默认关闭避障，暂时均使用运控sport_client接口
        RCLCPP_INFO(node_ctrl_->get_logger(), "avoid: true");
        // ret = ov_client.Move(0, 0, 0);
        ret = sport_client.Move(0, 0, 0);
    }
    else
    {
        // 常规模式下，为了稳定增加了点滤波或者踏步
        RCLCPP_INFO(node_ctrl_->get_logger(), "avoid: false");
        ret = sport_client.Move(0, 0, 0);

        // stopmove停止会切换到灵动步态，需切换回原步态
        // ret = sport_client.StopMove();
        // utChangeGaitProc();
    }

    last_x = 0.0;
    last_y = 0.0;
    last_yaw = 0.0;

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "StopMove Action failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 回零
int32_t RobDog_Ctrl_Unitree::robdogCtrl_ResetZero()         	
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    if (avoidFlag)
    {
        // 避障模式下的移动控制，默认关闭避障，暂时均使用运控sport_client接口
        // RCLCPP_INFO(node_ctrl_->get_logger(), "avoid: true");
        // ret = ov_client.Move(0, 0, 0);
        ret = sport_client.Move(0, 0, 0);
    }
    else
    {
        // 常规模式下，为了稳定增加了点滤波或者踏步
        // RCLCPP_INFO(node_ctrl_->get_logger(), "avoid: false");
        ret = sport_client.Move(0, 0, 0);

        // stopmove停止会切换到灵动步态，需切换回原步态
        // ret = sport_client.StopMove();
        // utChangeGaitProc();
    }

    last_x = 0.0;
    last_y = 0.0;
    last_yaw = 0.0;

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "StopMove Action failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 软急停
int32_t RobDog_Ctrl_Unitree::robdogCtrl_EmergencyStop()    	
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    ret = sport_client.Damp();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Damp Action failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 开启避障功能
int32_t RobDog_Ctrl_Unitree::robdogCtrl_AvoidOpen()           
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 关闭避障功能
int32_t RobDog_Ctrl_Unitree::robdogCtrl_AvoidClose()           
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 手动模式
int32_t RobDog_Ctrl_Unitree::robdogCtrl_ManualMode()          	
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 自主模式
int32_t RobDog_Ctrl_Unitree::robdogCtrl_AutoMode()       	    
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 移动模式
int32_t RobDog_Ctrl_Unitree::robdogCtrl_MoveMode()        	    
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 心跳
int32_t RobDog_Ctrl_Unitree::robdogCtrl_HeartBeat()        	
{
    //RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人温度信息
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Temperature()       	
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 0x31010D07
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Position(float x, float y, float radian)   	        
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 0x122
int32_t RobDog_Ctrl_Unitree::robdogCtrl_PositionAngVel()       
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 用户定义, 0x00000160
int32_t RobDog_Ctrl_Unitree::robdogCtrl_UserDefined(int32_t cmd)          	
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人状态信息
int32_t RobDog_Ctrl_Unitree::robdogCtrl_State()             	
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Unitree::utThreadInit()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    int32_t status;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    while(1)
    {
        // 等待通信正常
        if (false == utConnectWait())
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "utThreadInit: Wait Connect Error");
            continue;
        }

        // 关闭宇树避障服务
        if (avoidFlag == true)
        {
            ret = rsc.ServiceSwitch("obstacles_avoid", 0, status);
            if (ret == ROBDOGCTRL_ERROR_SUCCESS)
            {
                avoidFlag = false;
            }

            RCLCPP_INFO(node_ctrl_->get_logger(), "ServiceSwitch return: %d , status: %d", ret, status);
        }

        // 自动翻身设置为不生效
        if (autoRecover == true)
        {
            ret = sport_client.AutoRecoverSet(false);
            if (ret == ROBDOGCTRL_ERROR_SUCCESS)
            {
                autoRecover = false;
            }

            RCLCPP_INFO(node_ctrl_->get_logger(), "AutoRecoverSet return: %d", ret);
        }

        // 全都设置成功，退出
        if ((avoidFlag == false) && (autoRecover == false))
        {
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Unitree::robdogCtrl_Init(void* ctrl_ptr_)
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    if(nullptr == ctrl_ptr_) 
    {
        return ROBDOGCTRL_ERROR_INVALID_PARA;
    }

    node_ctrl_ = (RobdogCtrlNode *)ctrl_ptr_;
    
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    sport_client.SetTimeout(20.0f);
    sport_client.Init();

    // ov_client.SetTimeout(5.0f);
    // ov_client.Init();

    rsc.SetTimeout(10.0f);
    rsc.Init();

    thread ut_threadProc(bind(&RobDog_Ctrl_Unitree::utThreadProc, this));
    ut_threadProc.detach();

    thread ut_threadInit(bind(&RobDog_Ctrl_Unitree::utThreadInit, this));
    ut_threadInit.detach();

    //关闭雷达
    //lidarSwitch.data("ON");
    //pubLidarSwitch.reset(new ChannelPublisher<std_msgs::msg::dds_::String_>("rt/utlidar/switch"));
    //pubLidarSwitch->InitChannel();
    //pubLidarSwitch->Write(lidarSwitch);

    return ROBDOGCTRL_ERROR_SUCCESS;
}
