#ifndef __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_WAVHELPER_H_
#define __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_WAVHELPER_H_
#include <cstdint>
#include <string>
#include <fstream>
#include <rclcpp/rclcpp.hpp>
#include <homi_speech/def.h>
#include <homi_sdk/AudioStream.h>
namespace homi_speech
{
    // WAV文件头结构体
    struct WavHeader {
        char chunkId[4];
        uint32_t chunkSize;
        char format[4];
        char subchunk1Id[4];
        uint32_t subchunk1Size;
        uint16_t audioFormat;
        uint16_t numChannels;
        uint32_t sampleRate;
        uint32_t byteRate;
        uint16_t blockAlign;
        uint16_t bitsPerSample;
        char subchunk2Id[4];
        uint32_t subchunk2Size;
    };
    class WavHelperFromFile
    {
    private:
        WavHeader _header;
        std::ifstream _file;
    public:
        ~WavHelperFromFile()
        {
            close();
        }
        int openFile(const std::string &fileName)
        {
            if(_file.is_open())
            {
                _file.close();
            }
            _file = std::ifstream(fileName.c_str(), std::ios::binary);
            if(!_file.is_open())
            {
                RCLCPP_WARN(rclcpp::get_logger("WavHelper"),"open file(%s) fail",fileName.c_str());
                return -1;
            }
            _file.read(reinterpret_cast<char*>(&_header), sizeof(_header));
            std::streamsize bytesRead = _file.gcount(); 
            if(bytesRead!=sizeof(_header))
            {
                RCLCPP_WARN(rclcpp::get_logger("WavHelper"),"read header fail,bytesRead=%ld sizeof(WavHeader)=%lu",bytesRead,sizeof(_header));
                _file.close();
                return -1;
            }
            return 0;
        }
        homi::app::SampleFormat getFormat()
        {
            switch (_header.bitsPerSample) {
                case 8:
                    return homi::app::SampleFormat::PCM_S8LE;
                case 16:
                    return homi::app::SampleFormat::PCM_S16LE;
                case 24:
                    return homi::app::SampleFormat::PCM_S24LE;
                case 32:
                    return homi::app::SampleFormat::PCM_S32LE;
                default:
                    return homi::app::SampleFormat::End;
            }
        }
        uint32_t getChannels()
        {
            return _header.numChannels;
        }
        uint32_t getRate()
        {
            return _header.sampleRate;
        }
        std::vector<unsigned char> readData(int ops,uint32_t size)
        {
            std::vector<uint8_t> audioData(size);
            if(ops>=0)
            {
                _file.seekg(sizeof(WavHeader)+ops, std::ios::beg);
            }
            _file.read(reinterpret_cast<char*>(audioData.data()), size);
            std::streamsize bytesRead = _file.gcount(); 
            if(bytesRead<0)
            {
                audioData.resize(0);
            }
            audioData.resize(bytesRead);
            return audioData;
        }
        void close()
        {
            if(_file.is_open())
            {
                _file.close();
            }
        }
    };
}

#endif  // __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_WAVHELPER_H_
