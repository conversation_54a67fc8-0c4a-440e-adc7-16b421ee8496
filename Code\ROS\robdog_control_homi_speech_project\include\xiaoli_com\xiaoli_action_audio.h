/********************************机器人离线语音文件******************************* */
// enum HomiTaskStatus {
//     ROBOT_TASK_STATUS_STARTING,              //任务开始
//     ROBOT_TASK_STATUS_EXECUTING,             //任务执行中
//     ROBOT_TASK_STATUS_FAILED,                //任务执行失败
//     ROBOT_TASK_STATUS_COMPLETED              //任务完成
// };

/********************************机器人动作的对应执行时间******************************* */
// 站立、趴下、打招呼、扭身体、跳舞、蹦跶、摇摆、挺胸、伸懒腰、快速摇摆、坐下、拜年、比心
enum RobotActionTime {
    ACTION_STAND_UP       = 4,    // 站立
    ACTION_GET_DOWN       = 4,    // 趴下
    ACTION_GREETING       = 9,    // 打招呼
    ACTION_TWIST_BODY     = 21,    // 扭身体 或者七秒
    ACTION_DANCE          = 15,    // 跳舞
    ACTION_TWIST_ASS      = 15,    // 蹦跶
    ACTION_SHAKE_BODY     = 9,    // 摇摆
    ACTION_CHEST_OUT      = 13,    // 挺胸
    ACTION_STRETCH        = 13,    // 伸懒腰
    ACTION_FAST_SHAKE_BODY = 8,   // 快速摇摆
    ACTION_SIT_DOWN        = 9,   // 坐下
    ACTION_NEW_YEAR_CALL   = 17,   // 拜年
    ACTION_FINGER_HEART    = 14   // 比心
};

/******************************* 机器人的语音和表情 *******************************/

#define VIDEO_PATH_DEFAULT        "/vedio/default"
#define VIDEO_PATH_DANCE          "/vedio/dance"
#define VIDEO_PATH_HELLO          "/vedio/hello"
#define VIDEO_PATH_JUMPING        "/vedio/jumping"
#define VIDEO_PATH_SHAKE          "/vedio/shake"
#define VIDEO_PATH_TOUCH          "/vedio/touch"
#define VIDEO_PATH_TWIST          "/vedio/twist"

#define AUDIO_PATH_DANCE          "/audio/dance"
#define AUDIO_PATH_HELLO          "/audio/hello"
#define AUDIO_PATH_JUMPING        "/audio/jumping"
#define AUDIO_PATH_SHAKE          "/audio/shake"
#define AUDIO_PATH_TOUCH          "/audio/touch"
#define AUDIO_PATH_TWIST          "/audio/twist"
#define AUDIO_PATH_SITDOWN        "/audio/sitDown"
#define AUDIO_PATH_STRETCH        "/audio/stretch"
#define AUDIO_PATH_CHESTOUT       "/audio/chestOut"
#define AUDIO_PATH_NEW_AUDIO      "/audio/new_audio"
#define AUDIO_PATH_FINGERHEART    "/audio/Fingerheart"
#define AUDIO_PATH_FOLLOW         "/audio/follow"
#define AUDIO_PATH_HANDPOS        "/audio/handpos"
#define AUDIO_PATH_HAPPYNEWYEAR   "/audio/happyNewYear"
#define AUDIO_PATH_OBSTACLE       "/audio/Obstacle"
