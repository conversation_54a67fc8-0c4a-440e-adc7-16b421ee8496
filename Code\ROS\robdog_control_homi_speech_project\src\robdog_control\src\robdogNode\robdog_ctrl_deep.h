#pragma once

#include "robdog_ctrl_api.h"

// 发给机器狗的数据包
struct CommandHead {
  uint32_t code;
  uint32_t size;
  uint32_t type;
};
const uint32_t kDataSize = 256;
struct Command{
  CommandHead head;
  uint32_t data[kDataSize];
};

class RobDog_Ctrl_Deep : public RobDog_Ctrl_Dev
{
    public:
        int32_t robdogCtrl_Init(void* ctrl_ptr_);                          // 初始化
        int32_t robdogCtrl_ContinueMove(CtrlMoveData *pMoveData);       // 调整身体高度,0x21010102;前后平移,0x21010130;左右平移,0x21010131;调整偏航角,0x21010135
        int32_t robdogCtrl_Move(double x, double y, double yaw);    // 0x140,指定前后平移的速度(m/s); 0x145,指定左右平移的速度(m/s); 0x141, 指定旋转角速度(rad/s)
        int32_t robdogCtrl_StopMove();                              // 调整身体高度,0x21010102;前后平移,0x21010130;左右平移,0x21010131;调整偏航角,0x21010135
        int32_t robdogCtrl_StandUp();              	               // 起立/趴下,               0x21010202
        int32_t robdogCtrl_GetDown();                	             // 起立/趴下,               0x21010202
        int32_t robdogCtrl_Sit();       	          	               // 坐下,                   0x21010506
        int32_t robdogCtrl_Locomotion(RobdogCtrlMotion motion);         // 做动作
        int32_t robdogCtrl_ChangeGait(RobdogCtrlGait gait);
        int32_t robdogCtrl_ManualMode();          	  	             // 手动模式,               0x21010C02
        int32_t robdogCtrl_AutoMode();       	     	               // 自主模式,               0x21010C03
        int32_t robdogCtrl_ResetZero();           	             	   // 回零,                   0x21010C05
        int32_t robdogCtrl_MoveMode();        	     	               // 移动模式,               0x21010D06
        int32_t robdogCtrl_VoiceStand(int32_t cmd);        	       // 语音指令（起立/趴下）,   0x21010C0A
        int32_t robdogCtrl_AvoidClose();             	             // 关闭避障功能,           0x21012109
        int32_t robdogCtrl_AvoidOpen();                             // 开启避障功能
        int32_t robdogCtrl_EmergencyStop();    	      	           // 软急停,                 0x21020C0E
        int32_t robdogCtrl_HeartBeat();        	      	           // 心跳,                   0x21040001
        int32_t robdogCtrl_Temperature();       	  	               // 机器人温度信息,          0x21040002
        int32_t robdogCtrl_Position(float x, float y,float radian); //                         0x31010D07
        int32_t robdogCtrl_PositionAngVel();          	             //                         0x122
        int32_t robdogCtrl_UserDefined(int32_t cmd);          	     // 用户定义,                  0x00000160
        int32_t robdogCtrl_State();              	  	             // 机器人状态信息,           0x0901

        int32_t deepSendPacket(uint8_t *pkt, size_t packet_size);
        RobdogCtrlNode* node_ctrl_ = nullptr;
};