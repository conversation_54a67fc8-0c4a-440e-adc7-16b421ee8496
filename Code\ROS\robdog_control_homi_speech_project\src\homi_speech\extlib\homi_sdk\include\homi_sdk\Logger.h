#ifndef __SRC_HOMI_INCLUDE_HOMI_LOGGER_H_
#define __SRC_HOMI_INCLUDE_HOMI_LOGGER_H_

#include <string.h>

extern "C" {
typedef struct logger_s logger_t;
int  logger_print(logger_t* logger, int level, const char* fmt, ...) __attribute__((format(printf,3,4)));
}
namespace homi
{
    enum class LogLevel{
        LOG_LEVEL_TRACE,
        LOG_LEVEL_DEBUG,
        LOG_LEVEL_INFO,
        LOG_LEVEL_WARN,
        LOG_LEVEL_ERROR,
        LOG_LEVEL_FATAL
    } ;
    logger_t* loggerGet();
    int loggerLevel2Int(LogLevel level);
}

#define __HOMI_FILENAME__  (strrchr("/" __FILE__, '/') + 1)
#define loggerD(fmt, ...) do{if(homi::loggerGet()) logger_print(homi::loggerGet(), homi::loggerLevel2Int(homi::LogLevel::LOG_LEVEL_DEBUG),fmt " [%s:%d:%s]\n", ## __VA_ARGS__, __HOMI_FILENAME__, __LINE__, __FUNCTION__);}while(0)
#define loggerI(fmt, ...) do{if(homi::loggerGet()) logger_print(homi::loggerGet(), homi::loggerLevel2Int(homi::LogLevel::LOG_LEVEL_INFO), fmt " [%s:%d:%s]\n", ## __VA_ARGS__, __HOMI_FILENAME__, __LINE__, __FUNCTION__);}while(0)
#define loggerW(fmt, ...) do{if(homi::loggerGet()) logger_print(homi::loggerGet(), homi::loggerLevel2Int(homi::LogLevel::LOG_LEVEL_WARN), fmt " [%s:%d:%s]\n", ## __VA_ARGS__, __HOMI_FILENAME__, __LINE__, __FUNCTION__);}while(0)
#define loggerE(fmt, ...) do{if(homi::loggerGet()) logger_print(homi::loggerGet(), homi::loggerLevel2Int(homi::LogLevel::LOG_LEVEL_ERROR),fmt " [%s:%d:%s]\n", ## __VA_ARGS__, __HOMI_FILENAME__, __LINE__, __FUNCTION__);}while(0)
#define loggerF(fmt, ...) do{if(homi::loggerGet()) logger_print(homi::loggerGet(), homi::loggerLevel2Int(homi::LogLevel::LOG_LEVEL_FATAL),fmt " [%s:%d:%s]\n", ## __VA_ARGS__, __HOMI_FILENAME__, __LINE__, __FUNCTION__);}while(0)



#endif  // __SRC_HOMI_INCLUDE_HOMI_LOGGER_H_
