import launch
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, IncludeLaunchDescription, TimerAction
from launch.substitutions import LaunchConfiguration
from launch_ros.substitutions import FindPackageShare
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory



def generate_launch_description():
    config_share_dir = get_package_share_directory('launch_package')
    DeclareLaunchArgument('angle_threshold', default_value='30', description='angle_threshold value'),

    homi_ws_server = Node(
        package="nvidia_control",
        executable="homi_ws_server",
        output='log',  
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
    )

    nvidia_control = TimerAction(
        #delay 1s
        period=1.0,  
        # output='log',  
        actions=[ Node(
            package="nvidia_control",
            executable="nvidia_control",
            parameters=[config_share_dir + '/configs/robot_config.yaml'],
        )]
    )

    follow_rcs = Node(
        package="follow_rcs",
        executable="follow_rcs",
        output='log',  
        parameters=[config_share_dir + '/configs/follow_config.yaml'],
    )
    
    control_catch_node = Node(
        package="follow_strategy",
        executable="control_catch_turtle_v1",
        output='log',  
        name="control_catch_turtle_v1"
    )

    target_twist_estimate_node = Node(
        package="follow_strategy",
        executable="target_twist_estimate",
        output='log',  
        name="target_twist_estimate"
    )

    pixel2pose_node = Node(
        package="pixel2world_pose",
        executable="pixel",
        output='log',
        name="pixel"
    )

    video_config = os.path.join(
        get_package_share_directory("video_gst_nv"),
        "config",
        "param.yaml"
    )
    script_path = os.path.join(
        get_package_share_directory("video_gst_nv"),
        "script",
        "find_camera.sh"
    )
    video_gst_node = Node(
        package="video_gst_nv",
        namespace="video_gst_nv",
        name="video_gst_nv_node",
        executable="video_gst_nv_node",
        parameters=[
            video_config,
            {"script_path": script_path}
        ]

    )
    
    return LaunchDescription([
            homi_ws_server,
            nvidia_control,
            # follow_rcs,
            # control_catch_node,
            # target_twist_estimate_node,
            # pixel2pose_node,
            video_gst_node
    ])
