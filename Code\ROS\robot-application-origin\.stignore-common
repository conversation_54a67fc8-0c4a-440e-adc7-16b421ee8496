# 通用忽略规则
# 适用于所有项目类型的通用文件和目录

# ===== 系统文件 =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# ===== 临时文件 =====
*.tmp
*.temp
*.swp
*.swo
*~
.#*
\#*#

# ===== 日志文件 =====
*.log
logs/
log/

# ===== 备份文件 =====
*.bak
*.backup
*.old
bak/
backup/

# ===== 压缩文件（可选择性忽略） =====
*.zip
*.rar
*.7z
*.tar.gz
*.tar.bz2

# ===== IDE 和编辑器文件 =====

# IntelliJ IDEA
.idea/
*.iml
*.iws
*.ipr

# Eclipse
.project
.classpath
.settings/
.metadata/

# Visual Studio Code
.vscode/
*.code-workspace

# Vim
*.swp
*.swo
Session.vim

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# ===== JavaScript/Node.js 项目 =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===== 通用输出目录 =====
output/
outputs/
temp/
tmp/

# ===== 测试结果 =====
test_results/
test-results/
*_test_results/

# ===== 文档生成 =====
docs/build/

# ===== 大文件和媒体文件 =====

# 视频文件
*.mp4
*.avi
*.mov
*.mkv
*.flv

# 音频文件
*.mp3
*.wav
*.flac

# 图片文件（可选择性忽略，根据需要调整）
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.bmp 