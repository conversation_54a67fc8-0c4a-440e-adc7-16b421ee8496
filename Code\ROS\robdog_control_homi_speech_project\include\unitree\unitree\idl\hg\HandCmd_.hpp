/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: HandCmd_.idl
  Source: HandCmd_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_HG_HANDCMD__HPP
#define DDSCXX_UNITREE_IDL_HG_HANDCMD__HPP

#include "unitree/idl/hg/MotorCmd_.hpp"

#include <cstdint>
#include <array>
#include <vector>

namespace unitree_hg
{
namespace msg
{
namespace dds_
{
class HandCmd_
{
private:
 std::vector<::unitree_hg::msg::dds_::MotorCmd_> motor_cmd_;
 std::array<uint32_t, 4> reserve_ = { };

public:
  HandCmd_() = default;

  explicit HandCmd_(
    const std::vector<::unitree_hg::msg::dds_::MotorCmd_>& motor_cmd,
    const std::array<uint32_t, 4>& reserve) :
    motor_cmd_(motor_cmd),
    reserve_(reserve) { }

  const std::vector<::unitree_hg::msg::dds_::MotorCmd_>& motor_cmd() const { return this->motor_cmd_; }
  std::vector<::unitree_hg::msg::dds_::MotorCmd_>& motor_cmd() { return this->motor_cmd_; }
  void motor_cmd(const std::vector<::unitree_hg::msg::dds_::MotorCmd_>& _val_) { this->motor_cmd_ = _val_; }
  void motor_cmd(std::vector<::unitree_hg::msg::dds_::MotorCmd_>&& _val_) { this->motor_cmd_ = _val_; }
  const std::array<uint32_t, 4>& reserve() const { return this->reserve_; }
  std::array<uint32_t, 4>& reserve() { return this->reserve_; }
  void reserve(const std::array<uint32_t, 4>& _val_) { this->reserve_ = _val_; }
  void reserve(std::array<uint32_t, 4>&& _val_) { this->reserve_ = _val_; }

  bool operator==(const HandCmd_& _other) const
  {
    (void) _other;
    return motor_cmd_ == _other.motor_cmd_ &&
      reserve_ == _other.reserve_;
  }

  bool operator!=(const HandCmd_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_hg::msg::dds_::HandCmd_>::getTypeName()
{
  return "unitree_hg::msg::dds_::HandCmd_";
}

template <> constexpr bool TopicTraits<::unitree_hg::msg::dds_::HandCmd_>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::unitree_hg::msg::dds_::HandCmd_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::HandCmd_>::type_map_blob_sz() { return 752; }
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::HandCmd_>::type_info_blob_sz() { return 148; }
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::HandCmd_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x03,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0xf1,  0xa6,  0x3e,  0x17,  0xe2,  0x28,  0xe6,  0x7d, 
 0x27,  0x6d,  0xfe,  0x71,  0xd6,  0xf1,  0x56,  0x00,  0x52,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x42,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x1e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf1,  0x01,  0x00,  0x00,  0xf1, 
 0x82,  0x7f,  0x55,  0xde,  0x9b,  0x9d,  0xe3,  0x8c,  0x62,  0xba,  0xcd,  0x1e,  0x56,  0xc9,  0x5b,  0xcf, 
 0x19,  0xa4,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0xf1,  0x82, 
 0x7f,  0x55,  0xde,  0x9b,  0x9d,  0xe3,  0x8c,  0x62,  0xba,  0xcd,  0x1e,  0x56,  0xc9,  0x00,  0x00,  0x00, 
 0x83,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x73,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x76,  0x94,  0xf4,  0xa6,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x47,  0xbc,  0xdc,  0xd7,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x45,  0x80,  0xc2,  0x74,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x26,  0xb5,  0x68,  0xe4,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x87,  0x22,  0x16,  0x52,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x9e,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0xf2,  0x5c,  0x45,  0xf9,  0xf8,  0xb1,  0xe1,  0xc2,  0x79,  0xa6,  0x59,  0x52,  0x13,  0x32,  0xb0,  0x00, 
 0x8e,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x28,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x20,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x68,  0x67,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x48,  0x61,  0x6e,  0x64,  0x43, 
 0x6d,  0x64,  0x5f,  0x00,  0x5a,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x2c,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf2,  0x01,  0x00,  0x00,  0xf2,  0xa2,  0xd9,  0xce,  0xd5, 
 0x0a,  0x79,  0x56,  0xe2,  0xa6,  0x07,  0x61,  0x3a,  0x13,  0xf1,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x6d,  0x6f,  0x74,  0x6f,  0x72,  0x5f,  0x63,  0x6d,  0x64,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x04,  0x07,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00, 
 0x00,  0x00,  0xf2,  0xa2,  0xd9,  0xce,  0xd5,  0x0a,  0x79,  0x56,  0xe2,  0xa6,  0x07,  0x61,  0x3a,  0x13, 
 0xf1,  0x00,  0x00,  0x00,  0xe2,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x68,  0x67,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4d, 
 0x6f,  0x74,  0x6f,  0x72,  0x43,  0x6d,  0x64,  0x5f,  0x00,  0x00,  0x00,  0x00,  0xaa,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x02,  0x00,  0x00,  0x00,  0x71,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00,  0x74,  0x61,  0x75,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x6b,  0x70,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x6b,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x40,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0xf2,  0x5c,  0x45,  0xf9,  0xf8,  0xb1,  0xe1,  0xc2,  0x79,  0xa6,  0x59,  0x52, 
 0x13,  0x32,  0xb0,  0xf1,  0xa6,  0x3e,  0x17,  0xe2,  0x28,  0xe6,  0x7d,  0x27,  0x6d,  0xfe,  0x71,  0xd6, 
 0xf1,  0x56,  0xf2,  0xa2,  0xd9,  0xce,  0xd5,  0x0a,  0x79,  0x56,  0xe2,  0xa6,  0x07,  0x61,  0x3a,  0x13, 
 0xf1,  0xf1,  0x82,  0x7f,  0x55,  0xde,  0x9b,  0x9d,  0xe3,  0x8c,  0x62,  0xba,  0xcd,  0x1e,  0x56,  0xc9, 
};
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::HandCmd_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x90,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x40,  0x00,  0x00,  0x00,  0x3c,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0xa6,  0x3e,  0x17,  0xe2,  0x28,  0xe6,  0x7d,  0x27,  0x6d,  0xfe,  0x71, 
 0xd6,  0xf1,  0x56,  0x00,  0x56,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x82,  0x7f,  0x55,  0xde,  0x9b,  0x9d,  0xe3, 
 0x8c,  0x62,  0xba,  0xcd,  0x1e,  0x56,  0xc9,  0x00,  0x87,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40, 
 0x40,  0x00,  0x00,  0x00,  0x3c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x5c,  0x45,  0xf9, 
 0xf8,  0xb1,  0xe1,  0xc2,  0x79,  0xa6,  0x59,  0x52,  0x13,  0x32,  0xb0,  0x00,  0x92,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0xa2,  0xd9,  0xce,  0xd5,  0x0a,  0x79,  0x56,  0xe2,  0xa6,  0x07,  0x61,  0x3a,  0x13,  0xf1,  0x00, 
 0xe6,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_hg::msg::dds_::HandCmd_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_hg::msg::dds_::HandCmd_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_hg::msg::dds_::HandCmd_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_hg::msg::dds_::HandCmd_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_hg::msg::dds_::HandCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.motor_cmd().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.motor_cmd()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_hg::msg::dds_::HandCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::HandCmd_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_hg::msg::dds_::HandCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.motor_cmd().size());
      if (!read(streamer, se_1))
        return false;
      instance.motor_cmd().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.motor_cmd()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_hg::msg::dds_::HandCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::HandCmd_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_hg::msg::dds_::HandCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.motor_cmd().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.motor_cmd()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_hg::msg::dds_::HandCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::HandCmd_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_hg::msg::dds_::HandCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.motor_cmd()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_hg::msg::dds_::HandCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::HandCmd_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_HG_HANDCMD__HPP
