#ifndef __SRC_HOMI_INCLUDE_HOMI_AUDIOSTREAM_H_
#define __SRC_HOMI_INCLUDE_HOMI_AUDIOSTREAM_H_
#include <memory>
#include <vector>
#include <chrono>
namespace homi::app 
{
    enum class SampleFormat
    {
        PCM_U8LE =0,
        PCM_S8LE,
        PCM_U16LE,
        PCM_S16LE,
        PCM_U24LE,
        PCM_S24LE,
        PCM_U32LE,
        PCM_S32LE,
        PCM_U8BE,
        PCM_S8BE,
        PCM_U16BE,
        PCM_S16BE,
        PCM_U24BE,
        PCM_S24BE,
        PCM_U32BE,
        PCM_S32BE,
        PCM_F32,
        End
    };
    enum class StreamErrorCode 
    { 
        Success = 0, 
        Failure = 1,
        EAgain = 2,
        EEOF = 3,
        End
    };
    struct AudioConfig
    {
        SampleFormat format;
        unsigned int sampleRate;
        unsigned int channelCount;
    };
    struct AudioFrame
    {
        AudioConfig config;
        std::chrono::milliseconds ts;
        std::vector<unsigned char> data;
    };
    class IAudioStreamInput
    {
    public:
        virtual StreamErrorCode read(std::shared_ptr<AudioFrame> &InDataPtr,const std::chrono::milliseconds &wait=std::chrono::milliseconds::max()) = 0;
        virtual StreamErrorCode getAudioConfig(AudioConfig &) = 0;
        virtual StreamErrorCode close(const std::chrono::milliseconds &wait=std::chrono::milliseconds::max()) = 0;
        virtual ~IAudioStreamInput() = default;
    };

    class IAudioStreamOutput
    {
    public:
        virtual StreamErrorCode write(std::shared_ptr<AudioFrame> OutDataPtr,const std::chrono::milliseconds &wait=std::chrono::milliseconds::max()) = 0;
        virtual StreamErrorCode getAudioConfig(AudioConfig &config) = 0;
        virtual StreamErrorCode close(bool flush= false,const std::chrono::milliseconds &wait=std::chrono::milliseconds::max()) = 0;
        virtual ~IAudioStreamOutput() = default;
    };
   
}

#endif  // __SRC_HOMI_INCLUDE_HOMI_AUDIOSTREAM_H_
