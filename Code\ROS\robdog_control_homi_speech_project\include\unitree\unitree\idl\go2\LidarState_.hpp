/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: LidarState_.idl
  Source: LidarState_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_LIDARSTATE__HPP
#define DDSCXX_UNITREE_IDL_GO2_LIDARSTATE__HPP

#include <cstdint>
#include <array>
#include <string>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class LidarState_
{
private:
 double stamp_ = 0.0;
 std::string firmware_version_;
 std::string software_version_;
 std::string sdk_version_;
 float sys_rotation_speed_ = 0.0f;
 float com_rotation_speed_ = 0.0f;
 uint8_t error_state_ = 0;
 uint8_t dirty_percentage_ = 0;
 float cloud_frequency_ = 0.0f;
 float cloud_packet_loss_rate_ = 0.0f;
 uint32_t cloud_size_ = 0;
 uint32_t cloud_scan_num_ = 0;
 float imu_frequency_ = 0.0f;
 float imu_packet_loss_rate_ = 0.0f;
 std::array<float, 3> imu_rpy_ = { };
 double serial_recv_stamp_ = 0.0;
 uint32_t serial_buffer_size_ = 0;
 uint32_t serial_buffer_read_ = 0;

public:
  LidarState_() = default;

  explicit LidarState_(
    double stamp,
    const std::string& firmware_version,
    const std::string& software_version,
    const std::string& sdk_version,
    float sys_rotation_speed,
    float com_rotation_speed,
    uint8_t error_state,
    uint8_t dirty_percentage,
    float cloud_frequency,
    float cloud_packet_loss_rate,
    uint32_t cloud_size,
    uint32_t cloud_scan_num,
    float imu_frequency,
    float imu_packet_loss_rate,
    const std::array<float, 3>& imu_rpy,
    double serial_recv_stamp,
    uint32_t serial_buffer_size,
    uint32_t serial_buffer_read) :
    stamp_(stamp),
    firmware_version_(firmware_version),
    software_version_(software_version),
    sdk_version_(sdk_version),
    sys_rotation_speed_(sys_rotation_speed),
    com_rotation_speed_(com_rotation_speed),
    error_state_(error_state),
    dirty_percentage_(dirty_percentage),
    cloud_frequency_(cloud_frequency),
    cloud_packet_loss_rate_(cloud_packet_loss_rate),
    cloud_size_(cloud_size),
    cloud_scan_num_(cloud_scan_num),
    imu_frequency_(imu_frequency),
    imu_packet_loss_rate_(imu_packet_loss_rate),
    imu_rpy_(imu_rpy),
    serial_recv_stamp_(serial_recv_stamp),
    serial_buffer_size_(serial_buffer_size),
    serial_buffer_read_(serial_buffer_read) { }

  double stamp() const { return this->stamp_; }
  double& stamp() { return this->stamp_; }
  void stamp(double _val_) { this->stamp_ = _val_; }
  const std::string& firmware_version() const { return this->firmware_version_; }
  std::string& firmware_version() { return this->firmware_version_; }
  void firmware_version(const std::string& _val_) { this->firmware_version_ = _val_; }
  void firmware_version(std::string&& _val_) { this->firmware_version_ = _val_; }
  const std::string& software_version() const { return this->software_version_; }
  std::string& software_version() { return this->software_version_; }
  void software_version(const std::string& _val_) { this->software_version_ = _val_; }
  void software_version(std::string&& _val_) { this->software_version_ = _val_; }
  const std::string& sdk_version() const { return this->sdk_version_; }
  std::string& sdk_version() { return this->sdk_version_; }
  void sdk_version(const std::string& _val_) { this->sdk_version_ = _val_; }
  void sdk_version(std::string&& _val_) { this->sdk_version_ = _val_; }
  float sys_rotation_speed() const { return this->sys_rotation_speed_; }
  float& sys_rotation_speed() { return this->sys_rotation_speed_; }
  void sys_rotation_speed(float _val_) { this->sys_rotation_speed_ = _val_; }
  float com_rotation_speed() const { return this->com_rotation_speed_; }
  float& com_rotation_speed() { return this->com_rotation_speed_; }
  void com_rotation_speed(float _val_) { this->com_rotation_speed_ = _val_; }
  uint8_t error_state() const { return this->error_state_; }
  uint8_t& error_state() { return this->error_state_; }
  void error_state(uint8_t _val_) { this->error_state_ = _val_; }
  uint8_t dirty_percentage() const { return this->dirty_percentage_; }
  uint8_t& dirty_percentage() { return this->dirty_percentage_; }
  void dirty_percentage(uint8_t _val_) { this->dirty_percentage_ = _val_; }
  float cloud_frequency() const { return this->cloud_frequency_; }
  float& cloud_frequency() { return this->cloud_frequency_; }
  void cloud_frequency(float _val_) { this->cloud_frequency_ = _val_; }
  float cloud_packet_loss_rate() const { return this->cloud_packet_loss_rate_; }
  float& cloud_packet_loss_rate() { return this->cloud_packet_loss_rate_; }
  void cloud_packet_loss_rate(float _val_) { this->cloud_packet_loss_rate_ = _val_; }
  uint32_t cloud_size() const { return this->cloud_size_; }
  uint32_t& cloud_size() { return this->cloud_size_; }
  void cloud_size(uint32_t _val_) { this->cloud_size_ = _val_; }
  uint32_t cloud_scan_num() const { return this->cloud_scan_num_; }
  uint32_t& cloud_scan_num() { return this->cloud_scan_num_; }
  void cloud_scan_num(uint32_t _val_) { this->cloud_scan_num_ = _val_; }
  float imu_frequency() const { return this->imu_frequency_; }
  float& imu_frequency() { return this->imu_frequency_; }
  void imu_frequency(float _val_) { this->imu_frequency_ = _val_; }
  float imu_packet_loss_rate() const { return this->imu_packet_loss_rate_; }
  float& imu_packet_loss_rate() { return this->imu_packet_loss_rate_; }
  void imu_packet_loss_rate(float _val_) { this->imu_packet_loss_rate_ = _val_; }
  const std::array<float, 3>& imu_rpy() const { return this->imu_rpy_; }
  std::array<float, 3>& imu_rpy() { return this->imu_rpy_; }
  void imu_rpy(const std::array<float, 3>& _val_) { this->imu_rpy_ = _val_; }
  void imu_rpy(std::array<float, 3>&& _val_) { this->imu_rpy_ = _val_; }
  double serial_recv_stamp() const { return this->serial_recv_stamp_; }
  double& serial_recv_stamp() { return this->serial_recv_stamp_; }
  void serial_recv_stamp(double _val_) { this->serial_recv_stamp_ = _val_; }
  uint32_t serial_buffer_size() const { return this->serial_buffer_size_; }
  uint32_t& serial_buffer_size() { return this->serial_buffer_size_; }
  void serial_buffer_size(uint32_t _val_) { this->serial_buffer_size_ = _val_; }
  uint32_t serial_buffer_read() const { return this->serial_buffer_read_; }
  uint32_t& serial_buffer_read() { return this->serial_buffer_read_; }
  void serial_buffer_read(uint32_t _val_) { this->serial_buffer_read_ = _val_; }

  bool operator==(const LidarState_& _other) const
  {
    (void) _other;
    return stamp_ == _other.stamp_ &&
      firmware_version_ == _other.firmware_version_ &&
      software_version_ == _other.software_version_ &&
      sdk_version_ == _other.sdk_version_ &&
      sys_rotation_speed_ == _other.sys_rotation_speed_ &&
      com_rotation_speed_ == _other.com_rotation_speed_ &&
      error_state_ == _other.error_state_ &&
      dirty_percentage_ == _other.dirty_percentage_ &&
      cloud_frequency_ == _other.cloud_frequency_ &&
      cloud_packet_loss_rate_ == _other.cloud_packet_loss_rate_ &&
      cloud_size_ == _other.cloud_size_ &&
      cloud_scan_num_ == _other.cloud_scan_num_ &&
      imu_frequency_ == _other.imu_frequency_ &&
      imu_packet_loss_rate_ == _other.imu_packet_loss_rate_ &&
      imu_rpy_ == _other.imu_rpy_ &&
      serial_recv_stamp_ == _other.serial_recv_stamp_ &&
      serial_buffer_size_ == _other.serial_buffer_size_ &&
      serial_buffer_read_ == _other.serial_buffer_read_;
  }

  bool operator!=(const LidarState_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::LidarState_>::getTypeName()
{
  return "unitree_go::msg::dds_::LidarState_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::LidarState_>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::LidarState_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::LidarState_>::type_map_blob_sz() { return 1126; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::LidarState_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::LidarState_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x57,  0x01,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0x09,  0xd6,  0x30,  0xb6,  0x06,  0x40,  0xe7, 
 0xbe,  0x4d,  0xa2,  0xb9,  0x6d,  0x00,  0x8c,  0x00,  0x3f,  0x01,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2f,  0x01,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x96,  0xb8,  0xc7,  0x8d,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0xdf,  0xa1,  0xc7,  0xbd, 
 0x0c,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0xed,  0x06,  0xdc,  0x4c, 
 0x0c,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0x57,  0xea,  0x46,  0xbb, 
 0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xa9,  0x50,  0xc9,  0xf7,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x47,  0xda,  0x34,  0xb9,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xa7,  0xd8,  0x5b,  0x8a,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x46,  0xa2,  0x96,  0x22,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xee,  0x8b,  0xc3,  0x16,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x96,  0x24,  0x1c,  0x8b,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xf0,  0x35,  0xb6,  0xef,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x77,  0x03,  0x9a,  0x1e,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xb9,  0x6b,  0xc1,  0x8e,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x5f,  0x31,  0xe6,  0x4b,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x50,  0xa7,  0x3f,  0x29,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x0f,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x88,  0x89,  0x6b,  0x48,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x0e,  0x29,  0x6f,  0xc3,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x64,  0xba,  0x33,  0xa1,  0x00,  0xdd,  0x02,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0xf2,  0x19,  0xc9,  0x75,  0x02,  0xb1,  0x96,  0x04,  0x36,  0x08,  0x47,  0x2b, 
 0xd0,  0x18,  0x25,  0x00,  0xc5,  0x02,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x2b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4c, 
 0x69,  0x64,  0x61,  0x72,  0x53,  0x74,  0x61,  0x74,  0x65,  0x5f,  0x00,  0x00,  0x8d,  0x02,  0x00,  0x00, 
 0x12,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x73,  0x74,  0x61,  0x6d,  0x70,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0x11,  0x00,  0x00,  0x00,  0x66,  0x69,  0x72,  0x6d, 
 0x77,  0x61,  0x72,  0x65,  0x5f,  0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00, 
 0x1f,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0x11,  0x00,  0x00,  0x00, 
 0x73,  0x6f,  0x66,  0x74,  0x77,  0x61,  0x72,  0x65,  0x5f,  0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e, 
 0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x73,  0x64,  0x6b,  0x5f,  0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x13,  0x00,  0x00,  0x00,  0x73,  0x79,  0x73,  0x5f,  0x72,  0x6f,  0x74,  0x61,  0x74,  0x69,  0x6f,  0x6e, 
 0x5f,  0x73,  0x70,  0x65,  0x65,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x13,  0x00,  0x00,  0x00,  0x63,  0x6f,  0x6d,  0x5f, 
 0x72,  0x6f,  0x74,  0x61,  0x74,  0x69,  0x6f,  0x6e,  0x5f,  0x73,  0x70,  0x65,  0x65,  0x64,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x65,  0x72,  0x72,  0x6f,  0x72,  0x5f,  0x73,  0x74,  0x61,  0x74,  0x65,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x64,  0x69,  0x72,  0x74,  0x79,  0x5f,  0x70,  0x65,  0x72,  0x63,  0x65,  0x6e, 
 0x74,  0x61,  0x67,  0x65,  0x00,  0x00,  0x00,  0x00,  0x1e,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x10,  0x00,  0x00,  0x00,  0x63,  0x6c,  0x6f,  0x75,  0x64,  0x5f,  0x66,  0x72, 
 0x65,  0x71,  0x75,  0x65,  0x6e,  0x63,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x25,  0x00,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x17,  0x00,  0x00,  0x00,  0x63,  0x6c,  0x6f,  0x75, 
 0x64,  0x5f,  0x70,  0x61,  0x63,  0x6b,  0x65,  0x74,  0x5f,  0x6c,  0x6f,  0x73,  0x73,  0x5f,  0x72,  0x61, 
 0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x07,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x63,  0x6c,  0x6f,  0x75,  0x64,  0x5f,  0x73,  0x69, 
 0x7a,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1d,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x07,  0x00,  0x0f,  0x00,  0x00,  0x00,  0x63,  0x6c,  0x6f,  0x75,  0x64,  0x5f,  0x73,  0x63, 
 0x61,  0x6e,  0x5f,  0x6e,  0x75,  0x6d,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x0e,  0x00,  0x00,  0x00,  0x69,  0x6d,  0x75,  0x5f, 
 0x66,  0x72,  0x65,  0x71,  0x75,  0x65,  0x6e,  0x63,  0x79,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00, 
 0x0d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x15,  0x00,  0x00,  0x00,  0x69,  0x6d,  0x75,  0x5f, 
 0x70,  0x61,  0x63,  0x6b,  0x65,  0x74,  0x5f,  0x6c,  0x6f,  0x73,  0x73,  0x5f,  0x72,  0x61,  0x74,  0x65, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x09,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x69,  0x6d,  0x75,  0x5f,  0x72,  0x70,  0x79,  0x00,  0x00,  0x00,  0x00,  0x00,  0x20,  0x00,  0x00,  0x00, 
 0x0f,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00,  0x12,  0x00,  0x00,  0x00,  0x73,  0x65,  0x72,  0x69, 
 0x61,  0x6c,  0x5f,  0x72,  0x65,  0x63,  0x76,  0x5f,  0x73,  0x74,  0x61,  0x6d,  0x70,  0x00,  0x00,  0x00, 
 0x21,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x73,  0x65,  0x72,  0x69,  0x61,  0x6c,  0x5f,  0x62,  0x75,  0x66,  0x66,  0x65,  0x72,  0x5f,  0x73,  0x69, 
 0x7a,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x07,  0x00,  0x13,  0x00,  0x00,  0x00,  0x73,  0x65,  0x72,  0x69,  0x61,  0x6c,  0x5f,  0x62, 
 0x75,  0x66,  0x66,  0x65,  0x72,  0x5f,  0x72,  0x65,  0x61,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x19,  0xc9,  0x75,  0x02,  0xb1,  0x96,  0x04, 
 0x36,  0x08,  0x47,  0x2b,  0xd0,  0x18,  0x25,  0xf1,  0x09,  0xd6,  0x30,  0xb6,  0x06,  0x40,  0xe7,  0xbe, 
 0x4d,  0xa2,  0xb9,  0x6d,  0x00,  0x8c, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::LidarState_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x09,  0xd6,  0x30,  0xb6,  0x06,  0x40,  0xe7,  0xbe,  0x4d,  0xa2,  0xb9, 
 0x6d,  0x00,  0x8c,  0x00,  0x43,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x19,  0xc9,  0x75,  0x02,  0xb1,  0x96,  0x04,  0x36,  0x08,  0x47,  0x2b, 
 0xd0,  0x18,  0x25,  0x00,  0xc9,  0x02,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::LidarState_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::LidarState_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::LidarState_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::LidarState_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::LidarState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.stamp()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.firmware_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.software_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.sdk_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.sys_rotation_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.com_rotation_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.error_state()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.dirty_percentage()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.cloud_frequency()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.cloud_packet_loss_rate()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.cloud_size()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.cloud_scan_num()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.imu_frequency()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.imu_packet_loss_rate()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.imu_rpy()[0], instance.imu_rpy().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.serial_recv_stamp()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.serial_buffer_size()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 17:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.serial_buffer_read()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::LidarState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LidarState_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::LidarState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.stamp()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.firmware_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.software_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.sdk_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.sys_rotation_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.com_rotation_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.error_state()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.dirty_percentage()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.cloud_frequency()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.cloud_packet_loss_rate()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.cloud_size()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.cloud_scan_num()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.imu_frequency()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.imu_packet_loss_rate()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.imu_rpy()[0], instance.imu_rpy().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.serial_recv_stamp()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.serial_buffer_size()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 17:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.serial_buffer_read()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::LidarState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LidarState_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::LidarState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.stamp()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.firmware_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.software_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.sdk_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.sys_rotation_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.com_rotation_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.error_state()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.dirty_percentage()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.cloud_frequency()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.cloud_packet_loss_rate()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.cloud_size()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.cloud_scan_num()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.imu_frequency()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.imu_packet_loss_rate()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.imu_rpy()[0], instance.imu_rpy().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.serial_recv_stamp()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.serial_buffer_size()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 17:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.serial_buffer_read()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::LidarState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LidarState_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::LidarState_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.stamp()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.firmware_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.software_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.sdk_version(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.sys_rotation_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.com_rotation_speed()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.error_state()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.dirty_percentage()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.cloud_frequency()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.cloud_packet_loss_rate()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.cloud_size()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.cloud_scan_num()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.imu_frequency()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.imu_packet_loss_rate()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 14:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.imu_rpy()[0], instance.imu_rpy().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 15:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.serial_recv_stamp()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 16:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.serial_buffer_size()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 17:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.serial_buffer_read()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::LidarState_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LidarState_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_LIDARSTATE__HPP
