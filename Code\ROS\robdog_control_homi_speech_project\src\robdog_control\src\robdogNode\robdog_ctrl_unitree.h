#pragma once

#include <rclcpp/rclcpp.hpp>
#include <cmath>

#include "robdog_ctrl_api.h"

#include <unitree/robot/go2/sport/sport_client.hpp>
#include <unitree/idl/go2/LowState_.hpp>
#include <unitree/common/dds/dds_easy_model.hpp>
#include <unitree/robot/channel/channel_publisher.hpp>
#include <unitree/robot/channel/channel_subscriber.hpp>
#include <unitree/idl/go2/SportModeState_.hpp>
#include <unitree/robot/go2/obstacles_avoid/obstacles_avoid_client.hpp>
#include <unitree/idl/ros2/PointCloud2_.hpp>
#include <unitree/idl/ros2/String_.hpp>
#include <unitree/robot/go2/robot_state/robot_state_client.hpp>

using namespace unitree::common;
using namespace unitree::robot;
using namespace unitree::robot::go2;
using namespace std;

//vx:  取值范围[-2.5~3.8] (m/s)； vy:  取值范围[-1.0~1.0] (m/s)； vyaw:  取值范围[-4~4] (rad/s)。
//pitch:  取值范围  [-0.75~0.75] (rad)
#define UT_MOVE_X_HIGH                 0.8
#define UT_MOVE_X_LOW                (-0.8)
#define UT_MOVE_Y_HIGH                 0.6
#define UT_MOVE_Y_LOW                (-0.6)
#define UT_MOVE_YAW_HIGH               1.2
#define UT_MOVE_YAW_LOW              (-1.2)

#define UT_MOVE_X_HIGH_JUMP            0.6
#define UT_MOVE_X_LOW_JUMP           (-0.6)
#define UT_MOVE_Y_HIGH_JUMP            0.5
#define UT_MOVE_Y_LOW_JUMP           (-0.5)
#define UT_MOVE_YAW_HIGH_JUMP          1
#define UT_MOVE_YAW_LOW_JUMP         (-1)

#define UT_AVOID_MAX_ATTEMPT                          20

#define UT_ERROR_TIMEOUT                              3104

#define UT_SPORT_ERROR_CODE_FREE_WALK                              100
#define UT_SPORT_ERROR_CODE_DAMPING                                1001
#define UT_SPORT_ERROR_CODE_LOCK_STAND                             1002
#define UT_SPORT_ERROR_CODE_SQUAT                                  1004
#define UT_SPORT_ERROR_CODE_SQUAT_2                                2006
#define UT_SPORT_ERROR_CODE_LOCOMOTION                             1006
#define UT_SPORT_ERROR_CODE_SIT                                    1007
#define UT_SPORT_ERROR_CODE_FRONT_JUMP                             1008
#define UT_SPORT_ERROR_CODE_FRONT_POUNCE                           1009
#define UT_SPORT_ERROR_CODE_BALANCE_STAND                          1013
#define UT_SPORT_ERROR_CODE_STATIC_WALK                            1015
#define UT_SPORT_ERROR_CODE_TROT_RUN                               1016
#define UT_SPORT_ERROR_CODE_ECONOMIC_GAIT                          1017
#define UT_SPORT_ERROR_CODE_POSE                                   1091
#define UT_SPORT_ERROR_CODE_RECOVERYSTANDING                       2004
#define UT_SPORT_ERROR_CODE_FREE_AVOID                             2007
#define UT_SPORT_ERROR_CODE_FREE_BOUND                             2008
#define UT_SPORT_ERROR_CODE_FREE_JUMP                              2009
#define UT_SPORT_ERROR_CODE_CLASSIC                                2010
#define UT_SPORT_ERROR_CODE_HAND_STAND                             2011
#define UT_SPORT_ERROR_CODE_FRONT_FLIP                             2012
#define UT_SPORT_ERROR_CODE_BACK_FLIP                              2013
#define UT_SPORT_ERROR_CODE_LEFT_FLIP                              2014
#define UT_SPORT_ERROR_CODE_CROSS_STEP                             2016
#define UT_SPORT_ERROR_CODE_WALK_UPRIGHT                           2017
#define UT_SPORT_ERROR_CODE_PULL                                   2019

typedef enum 
{
    ROBDOGCTRL_TYPE_INVALID = 0,
    ROBDOGCTRL_TYPE_STANDUP,
    ROBDOGCTRL_TYPE_GETDOWN,
    ROBDOGCTRL_TYPE_SIT,
    ROBDOGCTRL_TYPE_CHANGE_GAIT,
    ROBDOGCTRL_TYPE_LOCOMOTION
}RobdogCtrlType;

class RobDog_Ctrl_Unitree : public RobDog_Ctrl_Dev
{
    public:
        int32_t robdogCtrl_Init(void * ctrl_ptr_);                  // 初始化
        int32_t robdogCtrl_ContinueMove(CtrlMoveData *pMoveData);   // 调整身体高度;前后平移;左右平移;调整偏航角
        int32_t robdogCtrl_Move(double x, double y, double yaw);    // 指定前后平移的速度(m/s); 指定左右平移的速度(m/s); 指定旋转角速度(rad/s)
        int32_t robdogCtrl_StopMove();                              // 调整身体高度;前后平移;左右平移;调整偏航角
        int32_t robdogCtrl_StandUp();              	             // 起立
        int32_t robdogCtrl_GetDown();                               // 趴下
        int32_t robdogCtrl_Sit();       	                         // 坐下
        int32_t robdogCtrl_Locomotion(RobdogCtrlMotion motion);         // 做动作
        int32_t robdogCtrl_ChangeGait(RobdogCtrlGait gait);
        int32_t robdogCtrl_ManualMode();          	                 // 手动模式
        int32_t robdogCtrl_AutoMode();       	                     // 自主模式
        int32_t robdogCtrl_ResetZero();         	                 // 回零
        int32_t robdogCtrl_MoveMode();        	                     // 移动模式
        int32_t robdogCtrl_VoiceStand(int32_t cmd);        	     // 语音指令（起立/趴下）
        int32_t robdogCtrl_AvoidClose();                            // 关闭避障功能
        int32_t robdogCtrl_AvoidOpen();                             // 开启避障功能
        int32_t robdogCtrl_EmergencyStop();    	                 // 软急停
        int32_t robdogCtrl_HeartBeat();        	                 // 心跳
        int32_t robdogCtrl_Temperature();       	                 // 机器人温度信息
        int32_t robdogCtrl_Position(float x, float y,float radian); // 
        int32_t robdogCtrl_PositionAngVel();                        // 
        int32_t robdogCtrl_UserDefined(int32_t cmd);                // 用户定义
        int32_t robdogCtrl_State();              	                 // 机器人状态信息

        SportClient sport_client;
        // ObstaclesAvoidClient ov_client;
        RobotStateClient rsc;
        std_msgs::msg::dds_::String_ lidarSwitch;
        ChannelPublisherPtr<std_msgs::msg::dds_::String_> pubLidarSwitch;

        RobdogCtrlType rdCtrlType();
        void wrCtrlType(RobdogCtrlType type);
        RobdogCtrlGait rdCtrlGait();
        void wrCtrlGait(RobdogCtrlGait gait);
        RobdogCtrlMotion rdCtrlMotion();
        void wrCtrlMotion(RobdogCtrlMotion motion);

        bool utConnectWait();
        bool utStandUpWait();
        bool utGaitCheck();
        // int32_t utAvoidUpdate();
        // int32_t utAvoidSet(bool enable);
        int32_t utStandUpProc();
        int32_t utGetDownProc();       
        int32_t utSitProc();
        int32_t utLocomotionProc();
        int32_t utChangeGaitProc();
        int32_t utThreadProc();
        int32_t utThreadInit();

        RobdogCtrlNode* node_ctrl_ = nullptr;
};