/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:04:29
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-02 21:02:13
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_cfg.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "taskMgr/task_info_mgr.h"
#include "robdogNode/robdog_ctrl_node.h"


void contextMsgCallback(void* parent) {
    if(!parent){
        return;
    }
    TaskInfoMgr* pThis = (TaskInfoMgr*)parent;
    pThis->executeTaskMsg();
}

TaskInfoMgr::TaskInfoMgr() {
    setNotifyMsgCallback(contextMsgCallback, this, 5);
    cur_task_info_ = std::make_shared<TaskInfo>();
    cur_task_info_->setTaskStatus(ROBOT_TASK_STATUS_COMPLETED);
}
TaskInfoMgr::~TaskInfoMgr() {
}

void TaskInfoMgr::init(RobdogCtrlNode* ctrl_ptr_) {
    if(nullptr == ctrl_ptr_) {
        return;
    }
    ctrl_node_ = ctrl_ptr_;
}


void TaskInfoMgr::executeTaskMsg() {
    if(!cur_task_info_) {
        return;
    }
    if(ROBOT_TASK_STATUS_COMPLETED != cur_task_info_->getTaskStatus()) {
        //当前执行的任务未完成，不执行新的任务
        return;
    }
    auto taskinfo = popContext();
    if (taskinfo) {
       cur_task_info_ = std::move(*taskinfo); 
    }
    
}

void TaskInfoMgr::cancelTask() {
    if(!cur_task_info_) {
        return;
    }
    if(cur_task_info_->getReExecute()) {
        cur_task_info_->setTaskStatus(ROBOT_TASK_STATUS_STARTING);
        auto taskinfo = std::move(cur_task_info_);
        //重新加到列表，并且执行的优先级最高，pushContext第二个参数为true（后进先出）
        pushContext(taskinfo, true);
    }

}


void TaskInfoMgr::updateTaskStatus(const string& uuid, HomiTaskStatus status) {
    if(!cur_task_info_) {
        return;
    }
    if(uuid != cur_task_info_->getUUid()) {
        //当前执行的任务状态不匹配直接忽略
        return;
    }
    cur_task_info_->setTaskStatus(status);
}
