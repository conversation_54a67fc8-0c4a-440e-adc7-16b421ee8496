#include "public/tools.h"
#include "robdogCenter/robdog_center_mgr.h"
#include "robotState/RobotState.h" 
#include "robotMgr/robot_info_mgr.h" 
#include <rclcpp/rclcpp.hpp>
#include <netinet/in.h>
#include <stdio.h>
#include <unistd.h>
#include <uuid/uuid.h>
#include <geometry_msgs/msg/twist.hpp>
#include <std_msgs/msg/string.hpp>
#include <cmath> 
#include <filesystem>
#include <fstream>
#include <iostream>
#include <jsoncpp/json/json.h>
#include <random>
#include <string>
#include <vector>
#include <atomic>
#include <chrono>
#include <thread>
#include "xiaoli_com/xiaoli_pub_def.h"
#include <homi_com/homi_utils.hpp>
#include <future>
#include <unordered_set>
#include <sensor_msgs/msg/nav_sat_fix.hpp>
#include <cmath>
#include "libWebSocket.h"
#include "trip.h"
using namespace std;
using namespace WS;

extern int nConnectIndex_;
std::vector<std::pair<float, float>> parseCoordinates(const std::string& input) {
    std::vector<std::pair<float, float>> coordinates;
    std::stringstream ss(input);
    std::string item;

    while (std::getline(ss, item, ';')) {
        std::stringstream ssItem(item);
        std::string latStr, lonStr;
        if (std::getline(ssItem, latStr, ',') && std::getline(ssItem, lonStr)) {
            float lat = std::stof(latStr);
            float lon = std::stof(lonStr);
            coordinates.emplace_back(lon, lat); // 注意顺序：经度在前，纬度在后
        }
    }

    return coordinates;
}
void renamePositionName(Json::Value& jsonArray) {
    if (jsonArray.isArray()) {
        for (Json::Value::ArrayIndex i = 0; i < jsonArray.size(); ++i) {
            Json::Value& obj = jsonArray[i];
            if (obj.isObject() && obj.isMember("latitude")) {
                obj["lat"] = obj["latitude"];
                obj.removeMember("latitude");
            }
            if (obj.isObject() && obj.isMember("longitude")) {
                obj["lon"] = obj["longitude"];
                obj.removeMember("longitude");
            }
        }
    }
}

std::string exec(const char* cmd) {
    char buffer[128];
    std::string result = "";
    FILE* pipe = popen(cmd, "r");
    if (!pipe) {
        throw std::runtime_error("popen() failed!");
    }
    try {
        while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
            result += buffer;
        }
    } catch (...) {
        pclose(pipe);
        throw;
    }
    pclose(pipe);
    return result;
}

Json::Value convertToJSON(const std::vector<std::pair<float, float>>& coordinates) {
    Json::Value jsonArray(Json::arrayValue);
    for (const auto& coord : coordinates) {
        Json::Value coordJson;
        // coordJson["lon"] = coord.first;
        coordJson["lat"] = coord.first;
        coordJson["lon"] = coord.second;
        jsonArray.append(coordJson);
    }
    return jsonArray;
}


Json::Value mergeJsonArrays(const Json::Value& array1, const Json::Value& array2) {
    Json::Value mergedArray(Json::arrayValue);
    if (array1.isArray()) {
        for (const auto& item : array1) {
            mergedArray.append(item);
        }
    }
    if (array2.isArray()) {
        for (const auto& item : array2) {
            mergedArray.append(item);
        }
    }
    return mergedArray;
}


void RobdogCenter::navTripStatusCallback(const std_msgs::msg::String::SharedPtr msg){
    if (getTripStatus()!=GOING) return;
    RCLCPP_INFO(rclcpp::get_logger("tripTask"), "Received msg form Avoid algorithm: %s", msg->data.c_str());
    std::string strMsg = msg->data;
    Json::Value value;
    Json::Reader reader;
    reader.parse(strMsg, value);
    switch (value["code"].asInt())
    {
    case ERROR_CAMERA_DATA:{
        RCLCPP_WARN(rclcpp::get_logger("tripTask"), "NO CAMAERA DATA form Avoid algorithm: code=%d", value["code"].asInt());
        setTripErrorCode(CHECK_RESULT_FAIL10);
        handleTripAbnormal(CHECK_RESULT_FAIL10);
        break;
    }
    case ERROR_ELEVATION_MAP:{
        RCLCPP_WARN(rclcpp::get_logger("tripTask"), "NO ELEVATION MAP form Avoid algorithm: code=%d", value["code"].asInt());
        setTripErrorCode(CHECK_RESULT_FAIL10);
        handleTripAbnormal(CHECK_RESULT_FAIL10);
        break;
    }
    case ERROR_OBSTACLE_AVOIDANCE_TIMEOUT:{
        RCLCPP_WARN(rclcpp::get_logger("tripTask"), "OBSTACLE AVOIDANCE TIMEOUT Avoid algorithm: code=%d", value["code"].asInt());
        setTripErrorCode(CHECK_RESULT_FAIL9);
        handleTripAbnormal(CHECK_RESULT_FAIL9);
        break;
    }     
    default:
        break;
    }   
}

Json::Value RobdogCenter::getGaoDeNavigation(float originLatitude, float originLongitude, float destinationLatitude, float destinationLongitude){

    const int MAX_RETRIES = 3;

    std::ostringstream oss;
    std::string command = "curl -s \"";
    std::string key="2448c1137ca178909153ddef36576838\"";

    std::string apiURL="https://restapi.amap.com/v3/direction/walking?origin=";
    // =${ORIGIN_LONGITUDE},${ORIGIN_LATITUDE}&destination=${DESTINATION_LONGITUDE},${DESTINATION_LATITUDE}&key=${KEY}"

    oss << std::fixed << std::setprecision(15);
    oss  << command << apiURL  << originLongitude << "," << originLatitude << "&destination=" << destinationLongitude << "," << destinationLatitude << "&key=" << key;
    std::string cmd = oss.str();
    Json::Value all_json_array(Json::arrayValue);
    Json::Value value;
    for (int retry = 0; retry < MAX_RETRIES; ++retry) {
        std::string result = exec(cmd.c_str());
        RCLCPP_INFO(rclcpp::get_logger("tripTask"), "actionPlanningMove2:cmd.c_str()=%s,result.c_str()=%s", cmd.c_str(),result.c_str());
        Json::Reader reader;
        if (!reader.parse(result, value)) {
            RCLCPP_INFO(rclcpp::get_logger("tripTask"), "ERROR: Failed to parse JSON from response");
            return all_json_array; 
        }
        if (value.isMember("route")) {
            break; 
        } else {
            RCLCPP_INFO(rclcpp::get_logger("tripTask"), "ERROR: No 'route' field in response. Retrying...");
        }
    }
    
    if (value.isMember("route")) {
        if(!value["route"]["paths"].isNull()) {
            Json::Value paths = value["route"]["paths"];
            if (!paths[0].isNull()) {
                Json::Value path = paths[0];
                if (!path["steps"].isNull()) {
                    Json::Value steps = path["steps"];
                    for (unsigned int i = 0; i < steps.size(); i++) {
                        Json::Value step = steps[i];
                        if (!step["polyline"].isNull()) {
                            std::vector<std::pair<float, float>> coordinates = parseCoordinates(step["polyline"].asString());
                            Json::Value jsonArray = convertToJSON(coordinates);
                            all_json_array = mergeJsonArrays(all_json_array, jsonArray);
                        }
                    }
                    setNavigationPath(all_json_array);
                    setDuration(2*atoi(path["duration"].asString().c_str()));//The walking speed of AutoNavi is 1m/s
                    setDistance(atoi(path["distance"].asString().c_str()));
                }
            }
        }
    }
    return  all_json_array;

}

int  RobdogCenter::handleNavigationCheckStatus() {
    if (getDistance()<=MINDIS)
        return CHECK_RESULT_FAIL1;
    if(getDistance()>=MAXDIS)
        return CHECK_RESULT_FAIL4;
    if(getRtkStatus()<RTKBASESTATUS)
        return CHECK_RESULT_FAIL2;
    if(RobotInfoMgr::getInstance().getBatteryLevel()<=BATTERYLOWLEVEL)
        return CHECK_RESULT_FAIL3;
    // if(RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_GETDOWN||RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_STATE)
    //     return CHECK_RESULT_FAIL6;
    return CHECK_RESULT_OK;
}

void RobdogCenter::handleNavigationRequest(const Json::Value &inValue) {

    Json::Value jBody = inValue["body"];
    setEventId(inValue["eventId"].asString());
    // int type = jBody["type"].asInt();
    setUserPhone(jBody["userPhone"].asString());

    float originLatitude = getLatitude();
    float originLongitude = getLongitude();
    Json::Value body;
    body["tripId"] = jBody["tripId"];

    Json::Value response = inValue;
    response["event"] = "navigation_response";
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";

        
    Json::Value all_json_array = getGaoDeNavigation(originLatitude, originLongitude, jBody["destination"]["lat"].asFloat(),jBody["destination"]["lon"].asFloat());

    body["navigationPath"] = all_json_array;
    body["checkResult"] = handleNavigationCheckStatus();
    if (body.isMember("navigationPath") && body["navigationPath"].isArray() && body["navigationPath"].empty())
        body["checkResult"] = 12;
    int battery = static_cast<int>(RobotInfoMgr::getInstance().getBatteryLevel());
    body["distance"] = getDistance();     //行程距离
    body["duration"] = getDuration();     //行程时间
    body["power"] = battery;
    body["estimatedCostPower"] = static_cast<int>(getDistance()*100/2000);
    body["maxDistance"] = MAXDIS;

    response["body"] = body;
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "handleNavigationRequest : %s", jsonString.c_str());
    sendRequestData(jsonString);

}
void RobdogCenter::handleTracetripNaviRequest(const Json::Value &inValue) {
    Json::Value jBody = inValue["body"];
    Json::Value response;
    response["event"] = "trace_trip_response";
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";
    response["eventId"]=inValue["eventId"].asString();
    response["deviceId"]=inValue["deviceId"].asString();
    response["domain"]= "DEVICE_TRIP";
    Json::Value body;
    setNavigationPath(jBody["navigationPath"]);
    body["tripId"] = jBody["tripId"];
    setTripId(jBody["tripId"].asInt64());
    body["distance"] = jBody["estimatedDistance"].asInt();     //行程距离
    setDistance(jBody["estimatedDistance"].asInt());
    body["duration"] =2*static_cast<int>(jBody["estimatedDistance"].asInt()/TRIP_SPEED) ;     //行程时间
    setDuration(body["duration"].asInt());
    body["power"] = static_cast<int>(RobotInfoMgr::getInstance().getBatteryLevel());
    body["estimatedCostPower"] = static_cast<int>(getDistance()*100/2000);  //预估耗电
    body["maxDistance"] = MAXDIS;
    body["checkResult"] = handleNavigationCheckStatus();
    response["body"] = body;
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "handleNavigationRequest : %s", jsonString.c_str());
    sendRequestData(jsonString);
}

void RobdogCenter::handleStripPathReport(const Json::Value &inValue)
{
    static int status_times = 0;

    int status = inValue["status"].asInt();
    RCLCPP_INFO(rclcpp::get_logger("tripTask"), "inValue[status].asInt() is  : %d", status);

    if(status == FINISH)
    {
        status_times++;
    }
    if(status != FINISH )
    {
        status_times = 0;
    }

    if(status_times >=FINISH_TIMES)
    {
        setTripStatus(STOP);
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
        tripDynamicReport();
        setTripDistance(0);
        setTripDuration(0);
        setDistance(0);
        setDuration(0);
    }

    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_TRIP";
    response["event"] = "trip_path_report";
    response["eventId"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";

    Json::Value body;
    body["tripId"] = (Json::Int64)getTripId();
    body["status"] =  (Json::Int64)getTripStatus();

    Json::Value currentPoint;
    currentPoint["lat"]=getLatitude();
    currentPoint["lon"]=getLongitude();
    body["currentPoint"] = currentPoint;

    Json::Value path;
    if (!inValue["path"].isNull()){
        RCLCPP_INFO(rclcpp::get_logger("tripTask"), "inValue[path] is  NOT NULL");
        path = inValue["path"];
        renamePositionName(path);
        body["path"] = path;
    }
    else{
        RCLCPP_INFO(rclcpp::get_logger("tripTask"), "inValue[path] is  NULL");
        body["path"] = Json::Value(Json::arrayValue);
    }
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("tripTask"), "handleStripPathReport : %s", jsonString.c_str());
    sendRequestData(jsonString);
    int remainDis=static_cast<int>(calculateRemainTotalDistance(response));
    RCLCPP_INFO(rclcpp::get_logger("tripTask"), "remainDis is Int : %d", remainDis);
    auto tripDis=getDistance()-remainDis;
    RCLCPP_INFO(rclcpp::get_logger("tripTask"), "tripDis is Int : %d", tripDis);
    tripDis=tripDis>0?tripDis:0;
    setTripDistance(tripDis);
}

void RobdogCenter::handleTripSimpleQuery(const Json::Value &inValue)
{
    Json::Value response = inValue;
    // response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["event"] = "trip_simple_response";
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";

    Json::Value body;
    body["tripId"] = (Json::Int64)getTripId();
    body["traceTaskId"]=(Json::Int64)getTripId();
    body["status"] = (Json::Int64)getTripStatus();

    Json::Value currentPoint;
    currentPoint["lat"]=getLatitude();
    currentPoint["lon"]=getLongitude();
    body["currentPoint"] = currentPoint;

    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleTripSimpleQuery cmd : %s", jsonString.c_str());
    sendRequestData(jsonString);

}


void RobdogCenter::handleCoordReportRequest(const Json::Value &inValue){
    if(inValue["actionType"] == "start")
    {
        setCoordReportStatus(1);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleCoordReportRequest start");
    }
    else if(inValue["actionType"] == "stop")
    {
        setCoordReportStatus(0);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleCoordReportRequest stop");
    }
    else
    {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Undefined actionType: %s", inValue["actionType"].asString().c_str());
    }
}
void RobdogCenter:: odom_callback(const nav_msgs::msg::Odometry::SharedPtr msg) {
    if (getTripStatus()!=GOING)  return;
    latest_odom_ = *msg;
    has_new_data_ = true;
    RCLCPP_INFO_THROTTLE(node_->get_logger(),*node_->get_clock(),1000,"odom callback has new data");
}

void RobdogCenter::check_pos_timeout() {
    if (getTripStatus()!=GOING) {
        prev_trip_status_=STOP;
        return;
    } 
    if (!has_new_data_) return;
    if (getTripStatus()==GOING && prev_trip_status_ != GOING) {
        last_move_time_ = Clock::now();
        RCLCPP_INFO(rclcpp::get_logger("tripTask"), "Start the timer for move monitor");
    }
    prev_trip_status_ =GOING;
    const double dx = latest_odom_.pose.pose.position.x - ref_position_.x;
    const double dy = latest_odom_.pose.pose.position.y - ref_position_.y;
    const double dz = latest_odom_.pose.pose.position.z - ref_position_.z;
    const bool is_moved = (sqrt(dx*dx + dy*dy + dz*dz) > POS_EPSILON);
    if (is_moved) {
        ref_position_ = latest_odom_.pose.pose.position;
        last_move_time_ = Clock::now(); 
        RCLCPP_INFO_THROTTLE(node_->get_logger(),*node_->get_clock(),1000,"[Movement] Position updated");
    }
    const auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(Clock::now() - last_move_time_).count();
    RCLCPP_INFO_THROTTLE(node_->get_logger(),*node_->get_clock(),1000,"[Timeout] Elapsed: %ld/10 seconds", elapsed);
    if (elapsed >= NO_MOVE_TIMEOUT) {
        RCLCPP_WARN(node_->get_logger(), 
                    "Timeout triggered! Last move time: %lld, Current time: %lld",
                    std::chrono::duration_cast<std::chrono::seconds>(last_move_time_.time_since_epoch()).count(),
                    std::chrono::duration_cast<std::chrono::seconds>(Clock::now().time_since_epoch()).count());
        setTripErrorCode(CHECK_RESULT_FAIL5);
        handleTripAbnormal(CHECK_RESULT_FAIL5);
    }
    has_new_data_ = false;
}

void RobdogCenter::actionTripAbnormal(int checkResultFail){

    setTripErrorCode(checkResultFail);
    std::shared_ptr<std_msgs::msg::String> msg = std::make_shared<std_msgs::msg::String>();
    Json::Value body;
    auto errorMap=TripErrors::get_error_map();
    if (errorMap.find(checkResultFail) != errorMap.end()) {
        const ErrorMapping& error = errorMap[checkResultFail];
        body["alarmCode"] = error.errorTripCode;
        body["alarmName"] = error.errorMessage;
        body["alarmDesc"] = error.errorMessage;
        RCLCPP_WARN(node_->get_logger(), "ERROR_TRIP Code:%d,Error Message: %s ", error.errorTripCode, error.errorMessage.c_str());
    } else {
        RCLCPP_WARN(node_->get_logger(),"Error: Unknown CHECK_RESULT_FAIL value.");
    }
    body["alarmLevel"] = ALARMLEVEL_2;
    body["alarmType"] = "算法";
    body["launcherModel"] = "deviceTrip";
    Json::Value data;
    body["data"]["tripId"] = (Json::Int64)getTripId();
    Json::FastWriter writer;
    std::string jsonString = writer.write(body);
    msg->data = jsonString;
    devAlarmReportCallback(msg);
}

//保存当前点位信息
void RobdogCenter::actionRobdogPose(const Json::Value &params)
{
    setRtkStatus(params["status"].asInt());
    if(params["status"].asInt()<RTKBASESTATUS){
        setLatitude(0.0);
        setLongitude(0.0);
    }else{
        setLatitude(params["latitude"].asFloat());
        setLongitude(params["longitude"].asFloat());
    }
    RCLCPP_INFO_THROTTLE(node_->get_logger(),*node_->get_clock(),1000,"latitude:%f, longitude:%f, status:%d", getLatitude(), getLongitude(),getRtkStatus());
}



void RobdogCenter::timerRobotCoordReport() {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_TRIP";
    response["event"] = "coord_report";
    response["eventId"] = "coord_report_" + to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";

    Json::Value body;
    body["lon"] = getLongitude();
    body["lat"] = getLatitude();
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "timerRobotCoordReport: %s", jsonString.c_str());
    sendRequestData(jsonString);
}

double RobdogCenter:: haversine(double lat1, double lon1, double lat2, double lon2) {
    const double R = 6371000; 
    lat1 = lat1 * M_PI / 180.0;
    lon1 = lon1 * M_PI / 180.0;
    lat2 = lat2 * M_PI / 180.0;
    lon2 = lon2 * M_PI / 180.0;
    double dlat = lat2 - lat1;
    double dlon = lon2 - lon1;
    double a = std::sin(dlat / 2) * std::sin(dlat / 2) +
               std::cos(lat1) * std::cos(lat2) *
               std::sin(dlon / 2) * std::sin(dlon / 2);
    double c = 2 * std::atan2(std::sqrt(a), std::sqrt(1 - a));
    return R * c;
}
double RobdogCenter:: calculateRemainTotalDistance(const Json::Value& jsonData) {
    try {
        if (!jsonData.isMember("body") || !jsonData["body"].isMember("path")) {
            throw std::runtime_error("JSON does not contain required 'body' or 'path' fields.");
        }
        auto path = jsonData["body"]["path"];
        if (path.empty() || path.size() <= 1) {
            return 0.0;
        }
        if (!jsonData["body"].isMember("currentPoint") ||
            !jsonData["body"]["currentPoint"].isMember("lat") ||
            !jsonData["body"]["currentPoint"].isMember("lon")) {
            throw std::runtime_error("JSON does not contain valid 'currentPoint' with 'lat' and 'lon'.");
        }
        double lastLat = jsonData["body"]["currentPoint"]["lat"].asDouble();
        double lastLon = jsonData["body"]["currentPoint"]["lon"].asDouble();
        double totalDistance = 0.0;
        if (path.size() == 1) {
            if (!path[0].isMember("lat") || !path[0].isMember("lon")) {
                throw std::runtime_error("Path point does not contain valid 'lat' and 'lon'.");
            }
            double currentLat = path[0]["lat"].asDouble();
            double currentLon = path[0]["lon"].asDouble();
            return haversine(lastLat, lastLon, currentLat, currentLon);
        }
        for (const auto& point : path) {
            if (!point.isMember("lat") || !point.isMember("lon")) {
                throw std::runtime_error("Path point does not contain valid 'lat' and 'lon'.");
            }
            double currentLat = point["lat"].asDouble();
            double currentLon = point["lon"].asDouble();
            totalDistance += haversine(lastLat, lastLon, currentLat, currentLon);
            lastLat = currentLat;
            lastLon = currentLon;
        }
        RCLCPP_INFO(rclcpp::get_logger("tripTask"), "Trip remain distance is : %f", totalDistance);
        return totalDistance;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 0.0;
    }
}
int  RobdogCenter::checkDynamic()
{
    if(getRtkStatus()<RTKBASESTATUS)
        return CHECK_RESULT_FAIL2;
    if(RobotInfoMgr::getInstance().getBatteryLevel()<=BATTERYLOWLEVEL)
        return CHECK_RESULT_FAIL3;
    if(RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_GETDOWN)
        return CHECK_RESULT_FAIL6;
    if(getTripAvoidStatus()!=0)
        return getTripAvoidStatus();
    auto robotTemperature=RobotInfoMgr::getInstance().getRobotTemperature();
    for (double temp : robotTemperature) {
        if (temp > JOINT_TMP_THRED)   
            return CHECK_RESULT_FAIL7;
    }
    if(RobotInfoMgr::getInstance().getRobotCPUTemperature()>CPU_TMP_THRED)
        return CHECK_RESULT_FAIL7;
    return CHECK_RESULT_OK;
}
void RobdogCenter::tripDynamicReport(){
        int tripStatus = getTripStatus();
        Json::Value response;
        response["deviceId"] = RobotState::getInstance().getDeviceId();
        response["domain"] = "DEVICE_TRIP";
        response["event"] = "trip_dynamic_report";
        response["eventId"] = "trip_dynamic_report_" + to_string(base::homiUtils::getCurrentTimeStamp());
        response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
        response["response"] = "false";
        Json::Value body;
        body["tripId"]=(Json::Int64)getTripId();
        // int checkStatus = checkDynamic();
        body["checkResult"]=getTripErrorCode();
        body["status"]=tripStatus;
        auto reDis=(getDistance()-getTripDistance())<0?getDistance():getDistance()-getTripDistance();
        RCLCPP_INFO(rclcpp::get_logger("tripTask"), "all dis is %d, has dis is %d ,rem dis is %d",getDistance() ,getTripDistance(),getDistance()-getTripDistance());
        body["distance"]=reDis;     //剩余距离，单位：米
        body["duration"]=static_cast<int>((reDis)/0.5); // 剩余时间，单位：秒
        body["power"]=static_cast<int>(RobotInfoMgr::getInstance().getBatteryLevel());    //剩余电量
        int usedPower=static_cast<int>(getTripStartBattery() - static_cast<int>(RobotInfoMgr::getInstance().getBatteryLevel()));
        if (usedPower==0)
            body["estimatedCostPower"]=(reDis)*(100)/1500;// 剩余距离预计耗电量
        else
            body["estimatedCostPower"]=((reDis)*(usedPower)/getTripDistance())>80?58:((reDis)*(usedPower)/getTripDistance());// 剩余距离预计耗电量
        body["maxDistance"]=1500;  // 新增最大限制距离(单位：米)返回，本体配置
        body["pastDistance"]=getTripDistance();// 已走过的距离，单位：米
        auto now = std::chrono::system_clock::now();
        std::chrono::duration<double> elapsed_seconds = now - tripStartTime_;
        body["pastDuration"]=elapsed_seconds.count();
        body["usedPower"]=getTripStartBattery() - static_cast<int>(RobotInfoMgr::getInstance().getBatteryLevel());
        response["body"] = body;
        Json::FastWriter writer;
        std::string jsonString = writer.write(response);
        RCLCPP_INFO(rclcpp::get_logger("tripTask"), "tripTimerCallback cmd : %s", jsonString.c_str());
        sendRequestData(jsonString);
}
void RobdogCenter::traceRecordDynamicRep(){

    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_TRIP";
    response["event"] = "create_trace_dynamic";
    response["eventId"] = "create_trace_dynamic" + to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";
    Json::Value body;
    body["traceTaskId"]=(Json::Int64)getTripId();
    body["checkResult"]=getTripErrorCode();
    body["status"] = static_cast<Json::Int>(getTripStatus()==TRACE_GOING?GOING:getTripStatus());
    body["maxDistance"] = MAXDIS;
    response["body"] = body;
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("Trace record task"), "Trace record dynamic response cmd : %s", jsonString.c_str());
    sendRequestData(jsonString);
}
void RobdogCenter::traceRecordPathRep(){
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_TRIP";
    response["event"] = "trace_path_report";
    response["eventId"] = "trace_path_report" + to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";
    Json::Value body;
    body["traceTaskId"]=(Json::Int64)getTripId();
    body["currentPoint"]["lat"]=rtkMsg.latitude;
    body["currentPoint"]["lon"]=rtkMsg.longitude;
    response["body"]=body;
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("Trace record task"), "Trace record dynamic response cmd : %s", jsonString.c_str());
    sendRequestData(jsonString);
}
void RobdogCenter::tripTimerCallback() {
    int tripStatus = getTripStatus();
    if(tripStatus == GOING ||  tripStatus == PAUSE ){
        tripDynamicReport();
    }
    if(tripStatus==TRACE_GOING ){
        traceRecordDynamicRep();
        traceRecordPathRep();
    }
    if(getCoordReportStatus() == 1)
        timerRobotCoordReport();
}

void RobdogCenter::trip_abnormal_monitor() {
    if (getTripStatus()!=GOING&&getTripStatus()!=TRACE_GOING ) return;
    if(getRtkStatus()<RTKBASESTATUS){
        setTripErrorCode(CHECK_RESULT_FAIL2);
        handleTripAbnormal(CHECK_RESULT_FAIL2);
        return;
    }
    if(RobotInfoMgr::getInstance().getBatteryLevel()<=BATTERYLOWLEVEL){
        setTripErrorCode(CHECK_RESULT_FAIL3);
        handleTripAbnormal(CHECK_RESULT_FAIL3);
        return;
    }
    if(RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_GETDOWN)
    {
        setTripErrorCode(CHECK_RESULT_FAIL6);
        handleTripAbnormal(CHECK_RESULT_FAIL6);
        return;
    }
    if(getTripAvoidStatus()!=0){
        setTripErrorCode(getTripAvoidStatus());
        handleTripAbnormal(CHECK_RESULT_FAIL6);
        return;
    }
    auto robotTemperature=RobotInfoMgr::getInstance().getRobotTemperature();
    for (double temp : robotTemperature) {
        if (temp > JOINT_TMP_THRED){
            setTripErrorCode(CHECK_RESULT_FAIL7);
            handleTripAbnormal(CHECK_RESULT_FAIL7);
            return;
        }   
    }
    if(RobotInfoMgr::getInstance().getRobotCPUTemperature()>CPU_TMP_THRED){
        setTripErrorCode(CHECK_RESULT_FAIL7);
        handleTripAbnormal(CHECK_RESULT_FAIL7);
        return;
    }
    return;
}


void RobdogCenter::handleTripReady(const Json::Value &inValue) {
    Json::Value value;
    value["client_type"] = CLIENT_LAUNCHER;
    value["target_client"] = CLIENT_NVIDIA;
    value["action"] = "tripStart";

    Json::Value params;
    setTripId(inValue["body"]["actionArgument"]["tripId"].asInt64());
    params["navigationPath"]=getNavigationPath();
    value["params"] = params;
    WS_Send(value.toStyledString().c_str(), nConnectIndex_);
    RCLCPP_INFO(rclcpp::get_logger("handleTripStart"), "send msg: %s", value.toStyledString().c_str());


    Json::Value jBody = inValue["body"];
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_TRIP";
    response["event"] = "robot_action";
    response["eventId"] = "robot_action";
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";

    Json::Value body;
    Json::Value actionArguement;
    actionArguement["tripId"] = (Json::Int64)getTripId();
    body["actionType"] = "tripReady";
    body["actionArguement"] = actionArguement;
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleTripReady request cmd : %s", jsonString.c_str());

    sendRequestData(jsonString);

    setTripStatus(GOING);
    RobotState::getInstance().setCurrentState(RobotStateEnum::OUTDOOR_TRIP); 
    setTripDistance(0);
    setTripStartBattery(static_cast<int>(RobotInfoMgr::getInstance().getBatteryLevel()));
}

void RobdogCenter::handleTripAbnormal(int errorCode) {
    RCLCPP_INFO(rclcpp::get_logger("handleTripAbnormal"), "handleTripAbnormal error is %d",errorCode);
    if (getTripStatus()==GOING){
        setTripStatus(ABNORMAL);
        Json::Value value;
        value["client_type"] = CLIENT_LAUNCHER;
        value["target_client"] = CLIENT_NVIDIA;
        if (errorCode==CHECK_RESULT_FAIL8)
            value["action"] = "tripPause";
        else
            value["action"] = "tripStop";
        Json::Value params;
        params["tripId"]=(Json::Int64)getTripId();
        value["params"] = params;
        WS_Send(value.toStyledString().c_str(), nConnectIndex_);
        RCLCPP_INFO(rclcpp::get_logger("handleTripAbnormal"), "send msg: %s", value.toStyledString().c_str());
        RCLCPP_INFO(rclcpp::get_logger("handleTripAbnormal"), "Last: tripDynamicReport after recieve trip abnormal");
        tripDynamicReport();
        Json::Value tmpvalue;
        tmpvalue["status"]=1;
        RCLCPP_INFO(rclcpp::get_logger("handleTripAbnormal"), "Last: handleStripPathReport after recieve trip abnormal");
        for (int i = 0; i < 5; ++i) {
            handleStripPathReport(tmpvalue);  
        }
        actionTripAbnormal(errorCode);
        setTripErrorCode(CHECK_RESULT_OK);
    }
    else if (getTripStatus()==TRACE_GOING)
    {
        setTripStatus(ABNORMAL);
        RCLCPP_INFO(rclcpp::get_logger("handle Trace Abnormal"), "Last: Trace DynamicReport after recieve trip abnormal");
        traceRecordDynamicRep();
        setTripErrorCode(CHECK_RESULT_OK);
    }
    RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
}


void RobdogCenter::handleTripStart(const Json::Value &inValue)
{
    int tripReadyStatus = checkTripReady();
    if (tripReadyStatus == 1){
        setTripErrorCode(CHECK_RESULT_OK);
        handleTripReady(inValue);
        homi_speech_interface::msg::RobdogAction msg;
        msg.actiontype = "tripStart";               //Set up the autonomy mode（MPC Mode）
        msg.actionargument = "on";
        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
        tripStartTime_=std::chrono::system_clock::now();
        if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN) {
            homi_speech_interface::msg::RobdogAction msg;
            msg.actiontype = "motorSkill";
            msg.actionargument = "standUp";
            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
            RCLCPP_INFO(rclcpp::get_logger("Trip Start"), "The dog is not standing before trip.Stand up");
        }
    }
}

void RobdogCenter::handleTripPause()
{
    Json::Value value;
    value["client_type"] = CLIENT_LAUNCHER;
    value["target_client"] = CLIENT_NVIDIA;
    value["action"] = "tripPause";

    Json::Value params;
    params["tripId"]=(Json::Int64)getTripId();
    value["params"] = params;
    WS_Send(value.toStyledString().c_str(), nConnectIndex_);
    RCLCPP_INFO(rclcpp::get_logger("handleTripPause"), "send msg: %s", value.toStyledString().c_str());
    setTripStatus(PAUSE);
    RobotState::getInstance().setCurrentState(RobotStateEnum::OUTDOOR_TRIP); 
    // TODO:异常处理
}

void RobdogCenter::handleTripCancel()
{
    Json::Value value;
    value["client_type"] = CLIENT_LAUNCHER;
    value["target_client"] = CLIENT_NVIDIA;
    value["action"] = "tripStop";

    Json::Value params;
    params["tripId"]=(Json::Int64)getTripId();
    value["params"] = params;
    WS_Send(value.toStyledString().c_str(), nConnectIndex_);
    RCLCPP_INFO(rclcpp::get_logger("handleTripCancel"), "send msg: %s", value.toStyledString().c_str());
    setTripStatus(CANCLE);
    RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
    RCLCPP_INFO(rclcpp::get_logger("handleTripCancel"), "Last: tripDynamicReport after recieve trip cancle");
    tripDynamicReport();
    Json::Value tmpvalue;
    tmpvalue["status"]=1;
    RCLCPP_INFO(rclcpp::get_logger("handleTripCancel"), "Last: handleStripPathReport after recieve trip cancle");
    for (int i = 0; i < 5; ++i) {
        handleStripPathReport(tmpvalue);  
    }
}
void RobdogCenter::netMonitorCallback(const std_msgs::msg::String::SharedPtr msg) {
    if (getTripStatus()!=GOING) return;
    Json::Value root;
    Json::Reader reader;
    reader.parse(msg->data, root);
    if (!root.isMember("netstatus")) {
      RCLCPP_WARN(rclcpp::get_logger("netMonitorCallback"), "缺少netstatus字段");
      return;
    }
    const int netstatus = root["netstatus"].asInt();
    std::lock_guard<std::mutex> lock(net_state_mutex_);

    switch (net_state_ ) {
      case NetState::IDLE:
        if (netstatus == 0) {
          RCLCPP_INFO(rclcpp::get_logger("netMonitorCallback"), "检测到网络断开，启动1秒抗抖动计时");
          net_state_  = NetState::WAITING_1SEC;
          timer_1sec_ = node_->create_wall_timer(1000ms, [this]() { handle1SecTimeout(); });}
        break;
      case NetState::WAITING_1SEC:
        if (netstatus == 1) {
          RCLCPP_INFO(rclcpp::get_logger("netMonitorCallback"), "网络在1秒内恢复，忽略抖动");
          timer_1sec_->cancel();
          net_state_  = NetState::IDLE;
        }
        break;
      case NetState::WAITING_9SEC:
        if (netstatus == 1) {
            RCLCPP_INFO(rclcpp::get_logger("netMonitorCallback"), "网络在9秒内恢复，发送通知");
            timer_9sec_->cancel();
            Json::Value params;
            Json::Value value;
            value["client_type"] = CLIENT_LAUNCHER;
            value["target_client"] = CLIENT_NVIDIA;
            value["action"] = "tripStart";
            params["navigationPath"]=getNavigationPath();
            value["params"] = params;
            WS_Send(value.toStyledString().c_str(), nConnectIndex_);
            RCLCPP_INFO(rclcpp::get_logger("handleTripStart after 9 seconds"), "send msg: %s", value.toStyledString().c_str());
            net_state_  = NetState::IDLE;
        }
        break;
    }
}

void RobdogCenter::handle1SecTimeout() {
  std::lock_guard<std::mutex> lock(net_state_mutex_);
  if (net_state_ != NetState::WAITING_1SEC) return;
  RCLCPP_WARN(rclcpp::get_logger("netMonitorCallback"), "确认网络持续断开，暂停任务");
  handleTripPause();
  net_state_ = NetState::WAITING_9SEC;
  timer_9sec_ = node_->create_wall_timer(
    9000ms,
    [this]() { this->handle9SecTimeout(); });
}

void RobdogCenter::handle9SecTimeout() {
  std::lock_guard<std::mutex> lock(net_state_mutex_);
  if (net_state_ != NetState::WAITING_9SEC) return;
  RCLCPP_ERROR(node_->get_logger(), "网络未恢复，取消任务");
  handleTripCancel();
  net_state_ = NetState::IDLE;
}

void RobdogCenter::handleTraceTrip(const Json::Value &inValue)
{
    Json::Value response;
    response["deviceId"] = inValue["deviceId"].asString();
    response["domain"] = inValue["domain"].asString();
    response["event"] = inValue["event"].asString();
    response["eventId"] = inValue["eventId"].asString();
    response["seq"] = inValue["seq"].asString(); 
    response["response"] = "false"; 
    Json::Value body;
    body["traceTaskId"] = inValue["body"]["traceTaskId"].asString();  
    body["actionType"] = inValue["body"]["actionType"].asString();
    if (inValue["body"]["actionType"].asString()=="start"||
        inValue["body"]["actionType"].asString()=="restart"){
        body["resultCode"] = CHECK_TRACE_RET_OK;
        body["resultMsg"] = "";  
        if(getRtkStatus()<RTKBASESTATUS)                //信任rtk为1的情况
            body["resultCode"] = CHECK_RESULT_FAIL2;
        if(RobotInfoMgr::getInstance().getBatteryLevel()<=BATTERYLOWLEVEL)
            body["resultCode"] = CHECK_RESULT_FAIL3;
    }else{                                              //end直接返回正常收到
        body["resultCode"] = 0;  
        body["resultMsg"] = ""; 
        setTripStatus(CANCLE); 
    }
    response["body"] = body;
    if(body["resultCode"].asInt64() == CHECK_TRACE_RET_OK&&inValue["body"]["actionType"].asString()!="end"){
        setTripId(inValue["body"]["traceTaskId"].asInt64());
        setTripStatus(TRACE_GOING);
    }
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "handle traceTrip:create trace action response: %s", jsonString.c_str());
    sendRequestData(jsonString);
}