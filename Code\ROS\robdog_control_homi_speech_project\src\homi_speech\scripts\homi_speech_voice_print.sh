#!/bin/bash

# 脚本名称（假设脚本名为 upload_script.sh）
SCRIPT_NAME=$(basename "$0")

# 清理函数：用于删除临时文件
cleanup() {
    if [ -n "$temp_file" ] && [ -f "$temp_file" ]; then
        echo "Deleting temporary file: $temp_file"
        rm "$temp_file"
        if [ $? -ne 0 ]; then
            echo "Failed to delete temporary file."
        fi
    fi
    if [ -n "$wav_file" ] && [ -f "$wav_file" ]; then
        echo "Deleting upload file: $wav_file"
        rm "$wav_file"
        if [ $? -ne 0 ]; then
            echo "Failed to delete upload file."
        fi
    fi
}

# 注册清理函数，在脚本退出时调用
trap cleanup EXIT

# 获取当前脚本的进程ID
CURRENT_PID=$$

# 检查是否有同名的脚本在运行（排除当前进程）
PIDS=$(ps -ef | grep "/bin/bash.*$SCRIPT_NAME" | grep -v "$CURRENT_PID" | grep -v grep | awk '{print $2}')

if [ -n "$PIDS" ]; then
    echo "Killing existing instances of $SCRIPT_NAME with PIDs: $PIDS"
    # 杀死所有找到的同名但非当前进程的脚本实例
    kill $PIDS
    if [ $? -ne 0 ]; then
        echo "Failed to kill some instances."
        exit 1
    fi
fi

# 检查参数数量是否正确
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <filename> <url>"
    exit 1
fi

# 获取参数
url=$1
filename=$2


# 创建临时文件名
temp_file="${filename}_$(date +%s).tmp"
wav_file="${temp_file}.wav"
# 拷贝原文件为临时文件
cp "$filename" "$temp_file" &&
ffmpeg -f s16le -ar 16000 -ac 1 -i ${temp_file} ${wav_file}
if [ $? -ne 0 ]; then
    echo "Failed to copy and change file."
    exit 1
fi

echo "Temporary file created: $temp_file"
echo "upload file created: $wav_file"

# 使用curl上传临时文件
response=$(curl --silent --write-out "%{http_code}" -X PUT --data-binary "@$wav_file" "$url")

# 检查上传结果
if [[ "$response" == 2* ]]; then
    # HTTP状态码以2开头表示成功
    echo "File uploaded successfully."
else
    echo "Failed to upload the file. HTTP response code: $response"
    exit 1
fi

exit 0