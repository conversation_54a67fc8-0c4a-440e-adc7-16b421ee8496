/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: LowCmd_.idl
  Source: LowCmd_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_LOWCMD__HPP
#define DDSCXX_UNITREE_IDL_GO2_LOWCMD__HPP

#include "unitree/idl/go2/BmsCmd_.hpp"

#include "unitree/idl/go2/MotorCmd_.hpp"

#include <cstdint>
#include <array>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class LowCmd_
{
private:
 std::array<uint8_t, 2> head_ = { };
 uint8_t level_flag_ = 0;
 uint8_t frame_reserve_ = 0;
 std::array<uint32_t, 2> sn_ = { };
 std::array<uint32_t, 2> version_ = { };
 uint16_t bandwidth_ = 0;
 std::array<::unitree_go::msg::dds_::MotorCmd_, 20> motor_cmd_ = { };
 ::unitree_go::msg::dds_::BmsCmd_ bms_cmd_;
 std::array<uint8_t, 40> wireless_remote_ = { };
 std::array<uint8_t, 12> led_ = { };
 std::array<uint8_t, 2> fan_ = { };
 uint8_t gpio_ = 0;
 uint32_t reserve_ = 0;
 uint32_t crc_ = 0;

public:
  LowCmd_() = default;

  explicit LowCmd_(
    const std::array<uint8_t, 2>& head,
    uint8_t level_flag,
    uint8_t frame_reserve,
    const std::array<uint32_t, 2>& sn,
    const std::array<uint32_t, 2>& version,
    uint16_t bandwidth,
    const std::array<::unitree_go::msg::dds_::MotorCmd_, 20>& motor_cmd,
    const ::unitree_go::msg::dds_::BmsCmd_& bms_cmd,
    const std::array<uint8_t, 40>& wireless_remote,
    const std::array<uint8_t, 12>& led,
    const std::array<uint8_t, 2>& fan,
    uint8_t gpio,
    uint32_t reserve,
    uint32_t crc) :
    head_(head),
    level_flag_(level_flag),
    frame_reserve_(frame_reserve),
    sn_(sn),
    version_(version),
    bandwidth_(bandwidth),
    motor_cmd_(motor_cmd),
    bms_cmd_(bms_cmd),
    wireless_remote_(wireless_remote),
    led_(led),
    fan_(fan),
    gpio_(gpio),
    reserve_(reserve),
    crc_(crc) { }

  const std::array<uint8_t, 2>& head() const { return this->head_; }
  std::array<uint8_t, 2>& head() { return this->head_; }
  void head(const std::array<uint8_t, 2>& _val_) { this->head_ = _val_; }
  void head(std::array<uint8_t, 2>&& _val_) { this->head_ = _val_; }
  uint8_t level_flag() const { return this->level_flag_; }
  uint8_t& level_flag() { return this->level_flag_; }
  void level_flag(uint8_t _val_) { this->level_flag_ = _val_; }
  uint8_t frame_reserve() const { return this->frame_reserve_; }
  uint8_t& frame_reserve() { return this->frame_reserve_; }
  void frame_reserve(uint8_t _val_) { this->frame_reserve_ = _val_; }
  const std::array<uint32_t, 2>& sn() const { return this->sn_; }
  std::array<uint32_t, 2>& sn() { return this->sn_; }
  void sn(const std::array<uint32_t, 2>& _val_) { this->sn_ = _val_; }
  void sn(std::array<uint32_t, 2>&& _val_) { this->sn_ = _val_; }
  const std::array<uint32_t, 2>& version() const { return this->version_; }
  std::array<uint32_t, 2>& version() { return this->version_; }
  void version(const std::array<uint32_t, 2>& _val_) { this->version_ = _val_; }
  void version(std::array<uint32_t, 2>&& _val_) { this->version_ = _val_; }
  uint16_t bandwidth() const { return this->bandwidth_; }
  uint16_t& bandwidth() { return this->bandwidth_; }
  void bandwidth(uint16_t _val_) { this->bandwidth_ = _val_; }
  const std::array<::unitree_go::msg::dds_::MotorCmd_, 20>& motor_cmd() const { return this->motor_cmd_; }
  std::array<::unitree_go::msg::dds_::MotorCmd_, 20>& motor_cmd() { return this->motor_cmd_; }
  void motor_cmd(const std::array<::unitree_go::msg::dds_::MotorCmd_, 20>& _val_) { this->motor_cmd_ = _val_; }
  void motor_cmd(std::array<::unitree_go::msg::dds_::MotorCmd_, 20>&& _val_) { this->motor_cmd_ = _val_; }
  const ::unitree_go::msg::dds_::BmsCmd_& bms_cmd() const { return this->bms_cmd_; }
  ::unitree_go::msg::dds_::BmsCmd_& bms_cmd() { return this->bms_cmd_; }
  void bms_cmd(const ::unitree_go::msg::dds_::BmsCmd_& _val_) { this->bms_cmd_ = _val_; }
  void bms_cmd(::unitree_go::msg::dds_::BmsCmd_&& _val_) { this->bms_cmd_ = _val_; }
  const std::array<uint8_t, 40>& wireless_remote() const { return this->wireless_remote_; }
  std::array<uint8_t, 40>& wireless_remote() { return this->wireless_remote_; }
  void wireless_remote(const std::array<uint8_t, 40>& _val_) { this->wireless_remote_ = _val_; }
  void wireless_remote(std::array<uint8_t, 40>&& _val_) { this->wireless_remote_ = _val_; }
  const std::array<uint8_t, 12>& led() const { return this->led_; }
  std::array<uint8_t, 12>& led() { return this->led_; }
  void led(const std::array<uint8_t, 12>& _val_) { this->led_ = _val_; }
  void led(std::array<uint8_t, 12>&& _val_) { this->led_ = _val_; }
  const std::array<uint8_t, 2>& fan() const { return this->fan_; }
  std::array<uint8_t, 2>& fan() { return this->fan_; }
  void fan(const std::array<uint8_t, 2>& _val_) { this->fan_ = _val_; }
  void fan(std::array<uint8_t, 2>&& _val_) { this->fan_ = _val_; }
  uint8_t gpio() const { return this->gpio_; }
  uint8_t& gpio() { return this->gpio_; }
  void gpio(uint8_t _val_) { this->gpio_ = _val_; }
  uint32_t reserve() const { return this->reserve_; }
  uint32_t& reserve() { return this->reserve_; }
  void reserve(uint32_t _val_) { this->reserve_ = _val_; }
  uint32_t crc() const { return this->crc_; }
  uint32_t& crc() { return this->crc_; }
  void crc(uint32_t _val_) { this->crc_ = _val_; }

  bool operator==(const LowCmd_& _other) const
  {
    (void) _other;
    return head_ == _other.head_ &&
      level_flag_ == _other.level_flag_ &&
      frame_reserve_ == _other.frame_reserve_ &&
      sn_ == _other.sn_ &&
      version_ == _other.version_ &&
      bandwidth_ == _other.bandwidth_ &&
      motor_cmd_ == _other.motor_cmd_ &&
      bms_cmd_ == _other.bms_cmd_ &&
      wireless_remote_ == _other.wireless_remote_ &&
      led_ == _other.led_ &&
      fan_ == _other.fan_ &&
      gpio_ == _other.gpio_ &&
      reserve_ == _other.reserve_ &&
      crc_ == _other.crc_;
  }

  bool operator!=(const LowCmd_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::LowCmd_>::getTypeName()
{
  return "unitree_go::msg::dds_::LowCmd_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::LowCmd_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::LowCmd_>::type_map_blob_sz() { return 1710; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::LowCmd_>::type_info_blob_sz() { return 196; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::LowCmd_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x72,  0x02,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0xf1,  0x83,  0x28,  0x76,  0xce,  0xec,  0x97,  0x37, 
 0xd6,  0x9f,  0x6e,  0xd6,  0x2a,  0x3f,  0x63,  0x00,  0x63,  0x01,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x53,  0x01,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x96,  0xe8,  0x9a,  0x29,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xa2,  0x18,  0x71,  0x24,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xf0,  0xd2,  0xa4,  0x1c,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x07,  0xaf,  0xbe,  0x94,  0xcd,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x2a,  0xf7, 
 0x2f,  0x10,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x36, 
 0x67,  0x6c,  0x2b,  0x00,  0x24,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf1, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x14,  0xf1,  0xc8,  0xe5,  0x83,  0x5b,  0x0f,  0x0c, 
 0x17,  0x65,  0xbc,  0x23,  0x1d,  0xfa,  0x02,  0xff,  0x5b,  0xcf,  0x19,  0xa4,  0x19,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x3e,  0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0,  0x7e,  0x52, 
 0xcf,  0x51,  0xed,  0x4a,  0x23,  0x11,  0x50,  0x97,  0xc7,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x28,  0x02,  0xa8,  0x2f,  0x18,  0xc9,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0c,  0x02,  0x0b,  0x98, 
 0xf7,  0xbc,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x02,  0x50,  0xbd,  0x8c,  0x21,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x95,  0x58,  0x71,  0xc9,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xf5,  0xad,  0x59,  0xc5,  0xf1, 
 0xc8,  0xe5,  0x83,  0x5b,  0x0f,  0x0c,  0x17,  0x65,  0xbc,  0x23,  0x1d,  0xfa,  0x02,  0xff,  0x00,  0x00, 
 0x8e,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x7e,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x76,  0x94,  0xf4,  0xa6,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x47,  0xbc,  0xdc,  0xd7,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x45,  0x80,  0xc2,  0x74,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x26,  0xb5,  0x68,  0xe4,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x87,  0x22,  0x16,  0x52,  0x00,  0x16,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x07,  0x9c,  0x3b, 
 0x62,  0x94,  0xf1,  0x3e,  0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0,  0x7e,  0x52,  0xcf,  0x51,  0xed,  0x4a, 
 0x23,  0x00,  0x00,  0x00,  0x3e,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x2e,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x32,  0x62,  0xd4,  0x8d,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x03,  0x02,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x00,  0xce,  0x03,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0xf2,  0x52,  0xcc,  0x8e,  0xb0,  0xd4,  0x27,  0xd6,  0x92,  0xaa,  0x2b,  0x20,  0xa9,  0x05,  0x40,  0x00, 
 0x26,  0x02,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x27,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x1f,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4c,  0x6f,  0x77,  0x43,  0x6d, 
 0x64,  0x5f,  0x00,  0x00,  0xf2,  0x01,  0x00,  0x00,  0x0e,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x02,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x68,  0x65,  0x61,  0x64,  0x00,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x6c,  0x65,  0x76,  0x65,  0x6c,  0x5f,  0x66,  0x6c,  0x61,  0x67,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x1c,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0e,  0x00,  0x00,  0x00, 
 0x66,  0x72,  0x61,  0x6d,  0x65,  0x5f,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00, 
 0x1d,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x73,  0x6e,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x76,  0x65,  0x72,  0x73,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x18,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x06,  0x00,  0x0a,  0x00,  0x00,  0x00,  0x62,  0x61,  0x6e,  0x64, 
 0x77,  0x69,  0x64,  0x74,  0x68,  0x00,  0x00,  0x00,  0x30,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf2,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x14,  0xf2,  0x42,  0x1f, 
 0xa5,  0xb2,  0xdb,  0x2d,  0xef,  0xf5,  0xc0,  0x3f,  0xff,  0x16,  0x30,  0x3b,  0x0a,  0x00,  0x00,  0x00, 
 0x6d,  0x6f,  0x74,  0x6f,  0x72,  0x5f,  0x63,  0x6d,  0x64,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x31,  0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a,  0xf3,  0xd3, 
 0xf1,  0x6b,  0x34,  0x22,  0x33,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x62,  0x6d,  0x73,  0x5f, 
 0x63,  0x6d,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2a,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x28,  0x02,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x77,  0x69,  0x72,  0x65,  0x6c,  0x65,  0x73,  0x73,  0x5f,  0x72,  0x65,  0x6d, 
 0x6f,  0x74,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1e,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0c,  0x02,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x6c,  0x65,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1e,  0x00,  0x00,  0x00, 
 0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x02,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x66,  0x61,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x13,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x67,  0x70,  0x69,  0x6f,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x07,  0x00,  0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x63,  0x72,  0x63,  0x00,  0x00,  0x00,  0xf2,  0x42,  0x1f,  0xa5,  0xb2,  0xdb, 
 0x2d,  0xef,  0xf5,  0xc0,  0x3f,  0xff,  0x16,  0x30,  0x3b,  0x00,  0x00,  0x00,  0xee,  0x00,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4d,  0x6f,  0x74,  0x6f,  0x72,  0x43,  0x6d,  0x64,  0x5f, 
 0x00,  0x00,  0x00,  0x00,  0xb6,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65, 
 0x00,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x71,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x12,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x74,  0x61,  0x75,  0x00,  0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x6b,  0x70,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x6b,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x06,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x07,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0xf2,  0x31, 
 0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a,  0xf3,  0xd3,  0xf1,  0x6b,  0x34,  0x22,  0x33,  0x00,  0x00,  0x00, 
 0x76,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x27,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x1f,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x42,  0x6d,  0x73,  0x43,  0x6d, 
 0x64,  0x5f,  0x00,  0x00,  0x42,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x04,  0x00,  0x00,  0x00,  0x6f,  0x66,  0x66,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x03,  0x02,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x5e,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0xf2,  0x52,  0xcc,  0x8e,  0xb0,  0xd4,  0x27,  0xd6,  0x92,  0xaa,  0x2b,  0x20, 
 0xa9,  0x05,  0x40,  0xf1,  0x83,  0x28,  0x76,  0xce,  0xec,  0x97,  0x37,  0xd6,  0x9f,  0x6e,  0xd6,  0x2a, 
 0x3f,  0x63,  0xf2,  0x42,  0x1f,  0xa5,  0xb2,  0xdb,  0x2d,  0xef,  0xf5,  0xc0,  0x3f,  0xff,  0x16,  0x30, 
 0x3b,  0xf1,  0xc8,  0xe5,  0x83,  0x5b,  0x0f,  0x0c,  0x17,  0x65,  0xbc,  0x23,  0x1d,  0xfa,  0x02,  0xff, 
 0xf2,  0x31,  0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a,  0xf3,  0xd3,  0xf1,  0x6b,  0x34,  0x22,  0x33,  0xf1, 
 0x3e,  0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0,  0x7e,  0x52,  0xcf,  0x51,  0xed,  0x4a,  0x23, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::LowCmd_>::type_info_blob() {
  static const uint8_t blob[] = {
 0xc0,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x83,  0x28,  0x76,  0xce,  0xec,  0x97,  0x37,  0xd6,  0x9f,  0x6e,  0xd6, 
 0x2a,  0x3f,  0x63,  0x00,  0x67,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0xc8,  0xe5,  0x83,  0x5b,  0x0f,  0x0c,  0x17, 
 0x65,  0xbc,  0x23,  0x1d,  0xfa,  0x02,  0xff,  0x00,  0x92,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x3e,  0x8e,  0xb6,  0x7c,  0x25,  0xfd,  0xe0,  0x7e,  0x52,  0xcf,  0x51,  0xed,  0x4a,  0x23,  0x00, 
 0x42,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x52,  0xcc,  0x8e,  0xb0,  0xd4,  0x27,  0xd6,  0x92,  0xaa,  0x2b,  0x20, 
 0xa9,  0x05,  0x40,  0x00,  0x2a,  0x02,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x42,  0x1f,  0xa5,  0xb2,  0xdb,  0x2d,  0xef, 
 0xf5,  0xc0,  0x3f,  0xff,  0x16,  0x30,  0x3b,  0x00,  0xf2,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x31,  0xa8,  0x59,  0x24,  0x05,  0x6b,  0x7a,  0xf3,  0xd3,  0xf1,  0x6b,  0x34,  0x22,  0x33,  0x00, 
 0x7a,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::LowCmd_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::LowCmd_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::LowCmd_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::LowCmd_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::LowCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.head()[0], instance.head().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.level_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.frame_reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.sn()[0], instance.sn().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.bandwidth()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_cmd()) {  //array depth 1
      if (!write(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.bms_cmd(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.led()[0], instance.led().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.fan()[0], instance.fan().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.gpio()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::LowCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LowCmd_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::LowCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.head()[0], instance.head().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.level_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.frame_reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.sn()[0], instance.sn().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.bandwidth()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (auto & a_1:instance.motor_cmd()) {  //array depth 1
      if (!read(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.bms_cmd(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.led()[0], instance.led().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.fan()[0], instance.fan().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.gpio()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::LowCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LowCmd_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::LowCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.head()[0], instance.head().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.level_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.frame_reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.sn()[0], instance.sn().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.bandwidth()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_cmd()) {  //array depth 1
      if (!move(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.bms_cmd(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.led()[0], instance.led().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.fan()[0], instance.fan().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.gpio()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::LowCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LowCmd_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::LowCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.head()[0], instance.head().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.level_flag()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.frame_reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.sn()[0], instance.sn().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.version()[0], instance.version().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 5:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.bandwidth()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 6:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_cmd()) {  //array depth 1
      if (!max(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 7:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.bms_cmd(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 8:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.wireless_remote()[0], instance.wireless_remote().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 9:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.led()[0], instance.led().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 10:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.fan()[0], instance.fan().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 11:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.gpio()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 12:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.reserve()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 13:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::LowCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::LowCmd_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_LOWCMD__HPP
