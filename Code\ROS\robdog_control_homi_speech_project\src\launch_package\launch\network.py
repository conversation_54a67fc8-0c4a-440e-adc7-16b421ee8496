import launch
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, IncludeLaunchDescription, TimerAction
from launch.substitutions import LaunchConfiguration
from launch_ros.substitutions import FindPackageShare
from launch_ros.actions import Node
import os
from ament_index_python.packages import get_package_share_directory
from launch.launch_description_sources import PythonLaunchDescriptionSource



def generate_launch_description():
    config_share_dir = get_package_share_directory('launch_package')


    network = Node(
        package="network",
        executable="network_node",
        #name='andlink_node',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
        # output='log',
        # respawn=True
    )

    return LaunchDescription([
    #     andlink,
    #     ble,
    #   homi_speech_yaml,
    network
    #   robdog_control
        # a2dp
    ])
