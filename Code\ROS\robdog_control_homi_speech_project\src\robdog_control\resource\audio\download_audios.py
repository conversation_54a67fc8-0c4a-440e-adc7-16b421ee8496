import requests
import json
import os

# JSON 数据
json_data={
    "data": {
        "audios": [
            {
                "text": "哎呀，这个问题太复杂啦，让我好好想一想",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164231515131904.wav"
            },
            {
                "text": "这样真的好舒服啊",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164237701730304.wav"
            },
            {
                "text": "我是小小舞蹈家，看我的绚丽的舞步",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164241443049472.wav"
            },
            {
                "text": "大家跟我的节奏扭起来",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164244739772416.wav"
            },
            {
                "text": "全身摆动起来，左边，右边！",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164248015523840.wav"
            },
            {
                "text": "这个问题太复杂了，等我学会了再告诉你吧",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164252021084160.wav"
            },
            {
                "text": "当前网络状态不佳，请检查网络是否正常",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164256051810304.wav"
            },
            {
                "text": "好的",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164258811662336.wav"
            },
            {
                "text": "大家好，我是移动爱家机器狗虎跑",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164262330683392.wav"
            },
            {
                "text": "大家好！我是移动爱家机器狗虎跑，很高兴在这里与大家见面",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164267992993792.wav"
            },
            {
                "text": "你是我的好朋友，所以让你摸模我的头吧",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164272246018048.wav"
            },
            {
                "text": "为什么你摸我的头？是因为我做了什么好事吗？",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164277409206272.wav"
            },
            {
                "text": "我也喜欢被摸后背，下次记得哦",
                "audio": "https://hz-wx-robot01-acloud-0001.obs.cidc-rp-12.joint.cmecloud.cn/robot/cloudbrain/ability/tmp/tts/2024/10/1/153164280617848832.wav"
            }
        ]
}

}
# 解析 JSON 数据
audios = json_data["data"]["audios"]

# 创建保存目录
output_dir = "/home/<USER>/code/deeprobots_application_ros1/resource/audio/new_audio"
# os.makedirs(output_dir, exist_ok=True)

# 下载所有音频文件
for audio in audios:
    url = audio["audio"]
    filename = os.path.basename(url)
    filepath = os.path.join(output_dir, filename)

# 检查文件是否已存在
    if not os.path.exists(filepath):
        response = requests.get(url)
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                f.write(response.content)
            print(f"Downloaded: {filename}")
        else:
            print(f"Failed to download: {filename}")
    else:
        print(f"{filename} already exists.")

print("All files downloaded.")