/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: Response_.idl
  Source: Response_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_RESPONSE__HPP
#define DDSCXX_RESPONSE__HPP

#include "ResponseHeader_.hpp"

#include <cstdint>
#include <vector>
#include <string>

namespace unitree_api
{
namespace msg
{
namespace dds_
{
class Response_
{
private:
 ::unitree_api::msg::dds_::ResponseHeader_ header_;
 std::string data_;
 std::vector<uint8_t> binary_;

public:
  Response_() = default;

  explicit Response_(
    const ::unitree_api::msg::dds_::ResponseHeader_& header,
    const std::string& data,
    const std::vector<uint8_t>& binary) :
    header_(header),
    data_(data),
    binary_(binary) { }

  const ::unitree_api::msg::dds_::ResponseHeader_& header() const { return this->header_; }
  ::unitree_api::msg::dds_::ResponseHeader_& header() { return this->header_; }
  void header(const ::unitree_api::msg::dds_::ResponseHeader_& _val_) { this->header_ = _val_; }
  void header(::unitree_api::msg::dds_::ResponseHeader_&& _val_) { this->header_ = _val_; }
  const std::string& data() const { return this->data_; }
  std::string& data() { return this->data_; }
  void data(const std::string& _val_) { this->data_ = _val_; }
  void data(std::string&& _val_) { this->data_ = _val_; }
  const std::vector<uint8_t>& binary() const { return this->binary_; }
  std::vector<uint8_t>& binary() { return this->binary_; }
  void binary(const std::vector<uint8_t>& _val_) { this->binary_ = _val_; }
  void binary(std::vector<uint8_t>&& _val_) { this->binary_ = _val_; }

  bool operator==(const Response_& _other) const
  {
    (void) _other;
    return header_ == _other.header_ &&
      data_ == _other.data_ &&
      binary_ == _other.binary_;
  }

  bool operator!=(const Response_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_api::msg::dds_::Response_>::getTypeName()
{
  return "unitree_api::msg::dds_::Response_";
}

template <> constexpr bool TopicTraits<::unitree_api::msg::dds_::Response_>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::unitree_api::msg::dds_::Response_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::Response_>::type_map_blob_sz() { return 1068; }
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::Response_>::type_info_blob_sz() { return 244; }
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::Response_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x53,  0x01,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0xf1,  0x48,  0xc9,  0x97,  0xd3,  0x79,  0xc3,  0xe1, 
 0x8c,  0x53,  0x0b,  0x5f,  0x8f,  0xe9,  0x46,  0x00,  0x58,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x48,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x9c,  0x43,  0xe5,  0x09,  0xd9, 
 0x26,  0x2e,  0x25,  0xe7,  0x3c,  0x21,  0xef,  0xbf,  0xee,  0x09,  0x9f,  0xb9,  0x95,  0x00,  0x00,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0x8d,  0x77,  0x7f,  0x38, 
 0x10,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf3,  0x01,  0x00,  0x00,  0x02, 
 0x9d,  0x71,  0x83,  0xf1,  0xf1,  0x9c,  0x43,  0xe5,  0x09,  0xd9,  0x26,  0x2e,  0x25,  0xe7,  0x3c,  0x21, 
 0xef,  0xbf,  0xee,  0x00,  0x51,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x41,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x8f,  0x78,  0x20,  0x91,  0xd8,  0x0b,  0x89,  0x0d,  0xad, 
 0xf3,  0xe7,  0xd6,  0x60,  0xe6,  0xff,  0x48,  0x3d,  0x1f,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x5b,  0x67,  0x75,  0x71,  0x30,  0x34,  0xdf,  0x53,  0x23, 
 0x66,  0x5f,  0xf9,  0x73,  0x40,  0x9a,  0xcb,  0x44,  0x54,  0xf1,  0x8f,  0x78,  0x20,  0x91,  0xd8,  0x0b, 
 0x89,  0x0d,  0xad,  0xf3,  0xe7,  0xd6,  0x60,  0xe6,  0x33,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0xb8,  0x0b,  0xb7,  0x74,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0xd3,  0x3a,  0xff,  0x5d,  0xf1, 
 0x5b,  0x67,  0x75,  0x71,  0x30,  0x34,  0xdf,  0x53,  0x23,  0x66,  0x5f,  0xf9,  0x73,  0x40,  0x00,  0x00, 
 0x23,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x13,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x04,  0xc1,  0x33,  0x67,  0x94,  0x00,  0x4f,  0x02,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0xf2,  0xd1,  0xc3,  0x2c,  0xc0,  0x22,  0x14,  0x90,  0x1a,  0x46,  0x4a,  0x6c,  0x51,  0x32,  0x5d,  0x00, 
 0x9d,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x2a,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x61,  0x70,  0x69,  0x3a, 
 0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x52,  0x65,  0x73,  0x70, 
 0x6f,  0x6e,  0x73,  0x65,  0x5f,  0x00,  0x00,  0x00,  0x65,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x25,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x0f,  0x90,  0xcc,  0xa0,  0xee, 
 0x3b,  0x25,  0x7f,  0xf8,  0x91,  0x0c,  0x07,  0xeb,  0x3e,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x68,  0x65,  0x61,  0x64,  0x65,  0x72,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0x05,  0x00,  0x00,  0x00,  0x64,  0x61,  0x74,  0x61, 
 0x00,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf3, 
 0x01,  0x00,  0x00,  0x02,  0x07,  0x00,  0x00,  0x00,  0x62,  0x69,  0x6e,  0x61,  0x72,  0x79,  0x00,  0x00, 
 0x00,  0xf2,  0x0f,  0x90,  0xcc,  0xa0,  0xee,  0x3b,  0x25,  0x7f,  0xf8,  0x91,  0x0c,  0x07,  0xeb,  0x3e, 
 0x95,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x30,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x28,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x61,  0x70,  0x69,  0x3a, 
 0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x52,  0x65,  0x73,  0x70, 
 0x6f,  0x6e,  0x73,  0x65,  0x48,  0x65,  0x61,  0x64,  0x65,  0x72,  0x5f,  0x00,  0x59,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x27,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0xf8, 
 0x8b,  0x48,  0x81,  0xc6,  0xcf,  0x5a,  0x29,  0xdc,  0xd6,  0x89,  0x3c,  0xdd,  0x22,  0x00,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x69,  0x64,  0x65,  0x6e,  0x74,  0x69,  0x74,  0x79,  0x00,  0x00,  0x00,  0x00, 
 0x25,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x1a,  0x4c,  0x7d,  0xe4,  0xfc, 
 0x64,  0xee,  0x04,  0x5d,  0x30,  0x0c,  0xff,  0x55,  0xda,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x73,  0x74,  0x61,  0x74,  0x75,  0x73,  0x00,  0x00,  0x00,  0xf2,  0xf8,  0x8b,  0x48,  0x81,  0xc6,  0xcf, 
 0x5a,  0x29,  0xdc,  0xd6,  0x89,  0x3c,  0xdd,  0x22,  0x75,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x31,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x29,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x61,  0x70,  0x69,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64, 
 0x73,  0x5f,  0x3a,  0x3a,  0x52,  0x65,  0x71,  0x75,  0x65,  0x73,  0x74,  0x49,  0x64,  0x65,  0x6e,  0x74, 
 0x69,  0x74,  0x79,  0x5f,  0x00,  0x00,  0x00,  0x00,  0x35,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x11,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x69,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x15,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x05,  0x00,  0x07,  0x00,  0x00,  0x00,  0x61,  0x70,  0x69,  0x5f,  0x69,  0x64,  0x00,  0x00, 
 0x00,  0xf2,  0x1a,  0x4c,  0x7d,  0xe4,  0xfc,  0x64,  0xee,  0x04,  0x5d,  0x30,  0x0c,  0xff,  0x55,  0xda, 
 0x57,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x30,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x28,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x61,  0x70,  0x69,  0x3a, 
 0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x52,  0x65,  0x73,  0x70, 
 0x6f,  0x6e,  0x73,  0x65,  0x53,  0x74,  0x61,  0x74,  0x75,  0x73,  0x5f,  0x00,  0x1b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x63,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00,  0x7c,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0xf2,  0xd1,  0xc3,  0x2c,  0xc0,  0x22,  0x14,  0x90,  0x1a,  0x46,  0x4a,  0x6c, 
 0x51,  0x32,  0x5d,  0xf1,  0x48,  0xc9,  0x97,  0xd3,  0x79,  0xc3,  0xe1,  0x8c,  0x53,  0x0b,  0x5f,  0x8f, 
 0xe9,  0x46,  0xf2,  0x0f,  0x90,  0xcc,  0xa0,  0xee,  0x3b,  0x25,  0x7f,  0xf8,  0x91,  0x0c,  0x07,  0xeb, 
 0x3e,  0xf1,  0x9c,  0x43,  0xe5,  0x09,  0xd9,  0x26,  0x2e,  0x25,  0xe7,  0x3c,  0x21,  0xef,  0xbf,  0xee, 
 0xf2,  0xf8,  0x8b,  0x48,  0x81,  0xc6,  0xcf,  0x5a,  0x29,  0xdc,  0xd6,  0x89,  0x3c,  0xdd,  0x22,  0xf1, 
 0x8f,  0x78,  0x20,  0x91,  0xd8,  0x0b,  0x89,  0x0d,  0xad,  0xf3,  0xe7,  0xd6,  0x60,  0xe6,  0xf2,  0x1a, 
 0x4c,  0x7d,  0xe4,  0xfc,  0x64,  0xee,  0x04,  0x5d,  0x30,  0x0c,  0xff,  0x55,  0xda,  0xf1,  0x5b,  0x67, 
 0x75,  0x71,  0x30,  0x34,  0xdf,  0x53,  0x23,  0x66,  0x5f,  0xf9,  0x73,  0x40, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::Response_>::type_info_blob() {
  static const uint8_t blob[] = {
 0xf0,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x70,  0x00,  0x00,  0x00,  0x6c,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x48,  0xc9,  0x97,  0xd3,  0x79,  0xc3,  0xe1,  0x8c,  0x53,  0x0b,  0x5f, 
 0x8f,  0xe9,  0x46,  0x00,  0x5c,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x4c,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x9c,  0x43,  0xe5,  0x09,  0xd9,  0x26,  0x2e, 
 0x25,  0xe7,  0x3c,  0x21,  0xef,  0xbf,  0xee,  0x00,  0x55,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x8f,  0x78,  0x20,  0x91,  0xd8,  0x0b,  0x89,  0x0d,  0xad,  0xf3,  0xe7,  0xd6,  0x60,  0xe6,  0x00, 
 0x37,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x5b,  0x67,  0x75,  0x71,  0x30,  0x34,  0xdf, 
 0x53,  0x23,  0x66,  0x5f,  0xf9,  0x73,  0x40,  0x00,  0x27,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40, 
 0x70,  0x00,  0x00,  0x00,  0x6c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xd1,  0xc3,  0x2c, 
 0xc0,  0x22,  0x14,  0x90,  0x1a,  0x46,  0x4a,  0x6c,  0x51,  0x32,  0x5d,  0x00,  0xa1,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x4c,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x0f,  0x90,  0xcc,  0xa0,  0xee,  0x3b,  0x25,  0x7f,  0xf8,  0x91,  0x0c,  0x07,  0xeb,  0x3e,  0x00, 
 0x99,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xf8,  0x8b,  0x48,  0x81,  0xc6,  0xcf,  0x5a, 
 0x29,  0xdc,  0xd6,  0x89,  0x3c,  0xdd,  0x22,  0x00,  0x79,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x1a,  0x4c,  0x7d,  0xe4,  0xfc,  0x64,  0xee,  0x04,  0x5d,  0x30,  0x0c,  0xff,  0x55,  0xda,  0x00, 
 0x5b,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_api::msg::dds_::Response_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_api::msg::dds_::Response_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_api::msg::dds_::Response_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_api::msg::dds_::Response_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_api::msg::dds_::Response_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.data(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.binary().size());
      if (!write(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !write(streamer, instance.binary()[0], se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_api::msg::dds_::Response_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::Response_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_api::msg::dds_::Response_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.data(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.binary().size());
      if (!read(streamer, se_1))
        return false;
      instance.binary().resize(se_1);
      if (se_1 > 0 &&
          !read(streamer, instance.binary()[0], se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_api::msg::dds_::Response_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::Response_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_api::msg::dds_::Response_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.data(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.binary().size());
      if (!move(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !move(streamer, uint8_t(), se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_api::msg::dds_::Response_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::Response_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_api::msg::dds_::Response_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.header(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.data(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !max(streamer, uint8_t(), se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_api::msg::dds_::Response_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::Response_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_RESPONSE__HPP
