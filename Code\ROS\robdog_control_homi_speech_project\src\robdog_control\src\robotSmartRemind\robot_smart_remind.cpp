
/****************************** 智能播报相关 **********************************/
#include "libWebSocket.h"
#include "robot_smart_remind.h" 

#include "robotState/RobotState.h"
#include "robotInfoCfg/read_map_point_cfg.h"
#include "robotMgr/robot_info_mgr.h"

#include <rclcpp/rclcpp.hpp>
#include <netinet/in.h>
#include <stdio.h>
#include <unistd.h>

#include <geometry_msgs/msg/twist.hpp>
#include <std_msgs/msg/string.hpp>
#include <cmath> 
#include <filesystem>
#include <fstream>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/assistant_take_photo.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>
#include <iostream>
#include <jsoncpp/json/json.h>
#include <random>
#include <string>
#include <tf2/transform_datatypes.h> // ROS 2 中的 tf2
#include <thread>
#include <vector>
#include <atomic>
#include <chrono>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <iostream>
#include <thread>
#include "xiaoli_com/xiaoli_pub_def.h"
#include <homi_com/homi_utils.hpp>
#include "robdogNode/robdog_ctrl_node.h"

#include "public/tools.h" // 为了播放表情
#include <sys/stat.h>
#include <dirent.h>
using namespace std;
using namespace WS;

RobdogSmartRemindCtrl::RobdogSmartRemindCtrl() {

}

RobdogSmartRemindCtrl::~RobdogSmartRemindCtrl() {
    
}

void RobdogSmartRemindCtrl::init(RobdogCtrlNode* node) {
    remind_ctrl_ = node;
    lastMoveMessageTime = remind_ctrl_->now();

    // ******************************************** 订阅和发布器 ******************************************** 

    // 智能播报服务的客户端【上传播报文本】
    brocast_client = remind_ctrl_->create_client<homi_speech_interface::srv::AssistantSpeechText>(
        "/homi_speech/helper_assistant_speech_text_service");

    wake_pub_ = remind_ctrl_->create_publisher<homi_speech_interface::msg::Wakeup>("/audio_recorder/wakeup_event", 10);

    // 请求语音助手打断当前正在播放的内容
    brocast_abort_client = remind_ctrl_->create_client<homi_speech_interface::srv::AssistantAbort>(
        "/homi_speech/helper_assistant_abort_service");
    
    // 关闭和开启语音助手
    set_wake_client = remind_ctrl_->create_client<homi_speech_interface::srv::SetWakeEvent>(
        "/audio_node/set_wake_event_service");

    timer_brocast = remind_ctrl_->create_wall_timer(
        std::chrono::seconds(40),
        std::bind(&RobdogSmartRemindCtrl::SendBrocastCallback, this));

    // 语音助手下发的是否播报被打断指令
    brocast_sub = remind_ctrl_->create_subscription<homi_speech_interface::msg::AssistantEvent>(
        "/homi_speech/speech_assistant_status_topic", 100,
        std::bind(&RobdogSmartRemindCtrl::BrocastIfAbortCallBack, this, std::placeholders::_1));
    /****************************** 定时器 **********************************/
    // 发布状态信息话题
    // status_pub_ = remind_ctrl_->create_publisher<homi_speech_interface::msg::ProprietySet>(
    //     "/deep_udp_ctrl/status_ctrl", 1);

    // // 定时器检查状态
    // timerDog = remind_ctrl_->create_wall_timer(
    //     std::chrono::milliseconds(100),
    //     std::bind(&RobdogSmartRemindCtrl::checkStatusWatchdog, this));   

    // robPoseStatusTimer_ = ctrl_->create_wall_timer(
    //     std::chrono::seconds(1),
    //     std::bind(&RobdogHandPosCtrl::timerRobotPoseCallback, this));

}

// 从string转到json
Json::Value parseJson_1(const std::string& jsonString) {
    Json::CharReaderBuilder readerBuilder;
    Json::Value data;
    std::string errs;
    std::istringstream stream(jsonString);
    if (!Json::parseFromStream(readerBuilder, stream, &data, &errs)) {
        std::cerr << "JSON 解析错误: " << errs ;
    }
    return data;
}

void RobdogSmartRemindCtrl::brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response)
{
    // 使用response的get()获取
    auto response_value = response.get();
    std::string sectionId  = response_value->section_id;
    RobotState::getInstance().setSectionId(sectionId);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received sectionId from server: %s", sectionId.c_str());
}

// --------------------------------- 和定时器有关的操作 ------------------------------------------
void RobdogSmartRemindCtrl::setTotalCount(int count) {
    total_count_ = count;
    send_count_ = 0; // 重置已发送次数
}

void RobdogSmartRemindCtrl::triggerTimerCallback() {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Timer Start");
    timer_->reset();
    timerCallback();
}

void RobdogSmartRemindCtrl::timerCallback() {
    if (send_count_ < total_count_) {
        // publishVelocity(current_twist_msg_);
        RobdogCenter::getInstance().publishVelocity(std::make_shared<geometry_msgs::msg::Twist>(current_twist_msg_));

        ++send_count_; // 增加了定时器次数
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Pub times: %d, Total Count_: %d", send_count_, total_count_);
    } else {
        // 完成发送后停止定时器
        // timer_.stop();
        timer_->cancel();
        send_count_ = 0;
    }
}

// --------------------------------- 和定点移动有关的操作 ------------------------------------------
 void RobdogSmartRemindCtrl::moveToTargetAndBrocast(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
    // 如果已在指定地点，则直接开始播报
    if (RobdogCenter::getInstance().isAtTarget(msg)) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start broadcasting!!");
        timer_brocast->cancel();
        brocast_send_count_ = 0;
        timer_brocast->reset();
        SendBrocastCallback();
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start MovetoTarget!!");
        // 创建一个用于发送给感知主机的msg（用于多点导航）
        auto msg_to_nvidia =  std::make_shared<homi_speech_interface::msg::SIGCEvent>();
        std::string data_string = msg->event;
        Json::Value inputRoot = parseJson_1(data_string);
        Json::Value outputRoot;

        // 添加 points 数组
        Json::Value pointsArray(Json::arrayValue);
        Json::Value point;
        
        // 这里获取 "remindLocation" 中的数据，并转换到 points 数组
        Json::Value remindLocation = inputRoot["body"]["remindLocation"];
        point["x"] = remindLocation["xCoordinate"].asDouble();
        point["y"] = remindLocation["yCoordinate"].asDouble();
        point["angle"] = remindLocation["angle"].asDouble();
        point["option"] = "even_low_speed";  // 根据需求填写
        point["uid"] = "1";  // 这里可以根据实际情况调整
        
        pointsArray.append(point);
        outputRoot["points"] = pointsArray;

        // 把json转为string
        Json::StreamWriterBuilder writer;
        std::string jsonString = Json::writeString(writer, outputRoot);
        msg_to_nvidia->event = jsonString;  
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The message of moveToTarget is: %s", jsonString.c_str());

        RobdogCenter::getInstance().moveToTarget(msg_to_nvidia);
        // 创建一个定时器来检查目标状态
        robMoveStatusTimer_brocast = remind_ctrl_->create_wall_timer(std::chrono::seconds(1), std::bind(&RobdogSmartRemindCtrl::checkTargetStatus_brocast, this));
    }
}

void RobdogSmartRemindCtrl::checkTargetStatus_brocast() {
    // 如果到达目标
    if (at_target_) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Arrive target point and start broadcasting!!");
        timer_brocast->cancel();
        brocast_send_count_ = 0;
        timer_brocast->reset();
        SendBrocastCallback();

        at_target_ = false; // 一次导航任务完成
        // 停止定时器
        robMoveStatusTimer_brocast->cancel(); 
    }
    // 如果导航任务取消了就关闭检查的定时器
    else if(moveCancel_){
        robMoveStatusTimer_brocast->cancel(); 
    }
}
// --------------------------------- 播报是否被唤醒词打断 ------------------------------------------
// 接受语音助手上报的信息（是否终止）
void RobdogSmartRemindCtrl::BrocastIfAbortCallBack(const homi_speech_interface::msg::AssistantEvent::SharedPtr msg) {
    std::string sectionId_past = RobotState::getInstance().getSectionId(); // 语音助手之前上报的sectionId
    std::string sectionId_cur = msg->section_id;
    std::string aborting = msg->description;
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "aborting: %s, sectionId_past: %s, sectionId_cur: %s",
    // aborting.c_str(), sectionId_past.c_str(), sectionId_cur.c_str());
    // if(sectionId_past == sectionId_cur)
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "6666666666666666666666666666666666666666");
    if (sectionId_past == sectionId_cur && aborting == "UserAbort") {   // 被语音词唤醒
        RobotBroadcastStatusToPlat(0); // 被打断
        timer_brocast->cancel();
        brocast_send_count_ = 0;
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "interrupted by the wake word!!!");
    }
}

// 向平台上传智能播报状态，被唤醒词打断了需上报
void RobdogSmartRemindCtrl::RobotBroadcastStatusToPlat(int status) {
    // 构建状态报告的 JSON
    Json::Value response;

    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "ROBOT_BUSINESS_DEVICE";        // 域
    response["event"] = "broadcast_report";              // 事件类型
    response["eventId"] = "111111111";                   // 事件 ID
    response["seq"] = std::to_string(base::homiUtils::getCurrentTimeStamp()); // 时间戳作为序列号

    // 将状态写入 JSON
    response["body"]["status"] = status; // 状态字段：0-打断，1-正常运行
    long id = RobotState::getInstance().getRemindId();
    response["body"]["remindId"] = Json::Int64(id); // 设备 ID【直接读取】

    // 将 JSON 转换为字符串
    Json::StreamWriterBuilder writerBuilder;
    std::string jsonString = Json::writeString(writerBuilder, response);

    // 打印信息
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Send res to plat, res is %s", jsonString.c_str());

    // 调用服务并处理响应
    RobdogCenter::getInstance().sendRequestData(jsonString);  
}

// --------------------------------- 播报内容的在线播放 ------------------------------------------
// 定时发送播报文本
void RobdogSmartRemindCtrl::SendBrocastCallback() {
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Brocast times: %d, Total Count_: %d", brocast_send_count_, brocast_total_count_);
    if (brocast_send_count_ < brocast_total_count_) { // 人为定义发送次数
        sendStringToBrocast(brocast_text); // 向语音助手发送播报文本
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start Brocasting: %s.", brocast_text);
        ++brocast_send_count_; // 增加了定时器次数
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start Brocasting: %s, Pub times: %d, Total Count_: %d", brocast_text, brocast_send_count_, brocast_total_count_);
    } else {
        // 完成发送后停止定时器
        timer_brocast->cancel();
        brocast_send_count_ = 0;
    }
}

// 调用服务向语音助手发送智能播报文本（收到的sectionId要保存起来）
void RobdogSmartRemindCtrl::sendStringToBrocast(const std::string &message) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Sending to brocast: %s", message.c_str()); // 播报文本
   
    // 创建请求消息
    auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();
    request->msg = message;

    // 调用服务并处理响应
    if (!brocast_client->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service not available after waiting");
        return;
    }

    auto ret = brocast_client->wait_for_service(std::chrono::seconds(1));
    if(ret==false)
    {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
    }
    auto result = brocast_client->async_send_request(request, std::bind(&RobdogSmartRemindCtrl::brocast_srv_callback, this, std::placeholders::_1));   

}

// *********************************************************************************************************************
// -------------------------- 播报的创建和立即执行 ---------------------------------------
void RobdogSmartRemindCtrl::CreatAndUpdate(const Json::Value &jBody){
    //RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "entityType == 10001");
    std::string data_string = jBody["data"].asString();
    Json::Value data = parseJson_1(data_string);

    long id = data["id"].asInt64();
    RobotState::getInstance().setRemindId(id);
    std::string deviceId = data["deviceId"].asString();
    std::string title = data["title"].asString();
    // int remindType = data["remindType"].asInt();
    bool enabled = data["enabled"].asBool();

    // std::vector<std::string> weekDays;

    // for (const auto& day : data["time"]["weekDays"]) {
    //     weekDays.push_back(day.asString());
    // }

    // int repeatType = data["time"]["repeatType"].asInt();
    // int dayOfMonth = data["time"]["dayOfMonth"].asInt();
    // int month = data["time"]["month"].asInt();
    // int year = data["time"]["year"].asInt();

    std::vector<std::string> contents;
    std::string text_single = ""; // 提醒文本
    std::string text = ""; // 所有提醒文本
    for (const auto& content : data["contents"]) {
        text_single = content["text"].asString();
        contents.push_back(text_single);
        text += text_single;
        text += "          ";
    }

    std::string remindLocationUid = data["remindLocation"]["uid"].asString();
    std::string remindLocationName = data["remindLocation"]["name"].asString();
    // int familyMemberId = data["familyMember"]["familyMemberId"].asInt();
    std::string nickname = data["familyMember"]["nickname"].asString();
    bool running = data["running"].asBool();

    if (!enabled && jBody["changeType"].asInt() != 1) { // changeType为3的时候需要打断播报
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "!enabled && jBody[changeType].asInt() != 1");
        timer_brocast->cancel();
        brocast_send_count_ = 0;
        // homi_speech_interface::srv::AssistantAbort::Request resMsg;
        // brocast_abort_client->async_send_request(std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>(resMsg));
        auto reqMsg = std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>();
        auto ret = brocast_abort_client->wait_for_service(std::chrono::seconds(1));
        if(ret==false)
        {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
        }
        auto result = brocast_abort_client->async_send_request(reqMsg);   
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "cut the brocast!!");

        // 此处要取消定点移动
        RobdogCenter::getInstance().handleCancelMovement();
    }

    if (running) {
        brocast_text = text;
        int secondOfDay = 0;
        int endSecondOfDay = 0;
        Json::Value time_broc = data["time"];
        if (!time_broc["secondOfDay"].isNull()){  
            secondOfDay = time_broc["secondOfDay"].asInt();
        }
        if (!time_broc["endSecondOfDay"].isNull()) {
            endSecondOfDay = time_broc["endSecondOfDay"].asInt();
            if (endSecondOfDay != 0 && endSecondOfDay > secondOfDay) { // 如果是时间段的提醒
                brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40;}
            else{
                brocast_total_count_ = 1;// 说明是单点的提醒 (endSecondOfDay等于0也可能是单点提醒)
            }
        } else {
            // endSecondOfDay 字段不存在，默认处理为单点提醒
            brocast_total_count_ = 1;
        }
        long long mapId = 0;
        std::string current_mapid = "";
        if (!data["remindLocation"]["mapId"].isNull()) {
            mapId = data["remindLocation"]["mapId"].asInt64();
            current_mapid = std::to_string(mapId);
        }
        if(data["remindLocation"]["xCoordinate"].isNull() && data["remindLocation"]["yCoordinate"].isNull() && data["remindLocation"]["angle"].isNull()){
            // 原地播报
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start On-site broadcasting!!");
            brocast_text = text;
            timer_brocast->cancel();
            brocast_send_count_ = 0;
            timer_brocast->reset();
            SendBrocastCallback();
        }
        // 判断mapId是否一致，不一致则不前往也不播报
        else if(!current_mapid.empty() && current_mapid != RobotState::getInstance().getMapId()){
            return;
        }
        else{
            // 新造一个类似于平台的msg
            // homi_speech_interface::msg::SIGCEvent::SharedPtr broadcast_msg;
            auto broadcast_msg = std::make_shared<homi_speech_interface::msg::SIGCEvent>();
            Json::Value value_brocast;
            value_brocast["body"] = jBody;
            Json::StreamWriterBuilder writer;
            std::string jsonString = Json::writeString(writer, value_brocast);
            broadcast_msg->event = jsonString;  
            // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The message will be broadcasted, %s", broadcast_msg->event.c_str());             
            moveToTargetAndBrocast(broadcast_msg); // 放到定点移动模块去执行      
        }
    }
}

void RobdogSmartRemindCtrl::RemindOnTime(const Json::Value &jBody) {
    // 处理定时提醒的逻辑（播报立即执行）
    long id = jBody["id"].asInt64();     // 数据 ID // 存起来
    RobotState::getInstance().setRemindId(id);
    std::string deviceId = jBody["deviceId"].asString(); // 设备 ID
    std::string title = jBody["title"].asString();       // 标题
    int remindType = jBody["remindType"].asInt(); // 1-吃药提醒 2-日程提醒
    bool enabled = jBody["enabled"].asBool(); // 启用状态

    // std::vector<std::string> weekDays;
    // for (const auto &day : jBody["time"]["weekDays"]) {
    //     weekDays.push_back(day.asString()); // 取值集合"Mon","Tue","Wed","Thu","Fri","Sat","Sun"
    // }

    // int repeatType = jBody["time"]["repeatType"].asInt(); // 重复类型：1:每周; 2:单次
    // int dayOfMonth = jBody["time"]["dayOfMonth"].asInt();    // 一个月中的第几天
    // int month = jBody["time"]["month"].asInt(); // 月份
    // int year = jBody["time"]["year"].asInt();   // 年份

    // 播报的点位信息：
    std::string uid = jBody["remindLocation"]["uid"].asString(); // 点位唯一id
    std::string name = jBody["remindLocation"]["name"].asString(); // 位置名称

    std::vector<std::string> contents;
    std::string text_single = ""; // 提醒文本
    std::string text = ""; // 所有提醒文本
    for (const auto &content : jBody["contents"]) {
        std::string contentType = content["contentType"].asString(); // 1-播报内容  2-天气预报
        text_single = content["text"].asString();     // 提醒文本
        // int location = content["location"].asInt(); // 所在城市
        std::string locationStr = content["locationStr"].asString(); // 所在城市，天气提醒必填(冗余)

        // 可以根据需要对内容进行处理或存储
        contents.push_back(text_single); // 示例：将文本内容存入 vector
        // 要把文本拼成一个长文本播放
        // text += "         "; 
        text += text_single;
        text += "          ";
    }

    std::string remindLocationUid = jBody["remindLocation"]["uid"].asString(); // 提醒位置 uid
    std::string remindLocationName = jBody["remindLocation"]["name"].asString(); // 提醒位置名称

    // int familyMemberId = jBody["familyMember"]["familyMemberId"].asInt(); // 关联家庭成员 id
    std::string nickname = jBody["familyMember"]["nickname"].asString(); // 家庭成员昵称

    if (!enabled) {
        // RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "No broadcasting!");
        // RobotBroadcastStatusToPlat(0);
        timer_brocast->cancel();
        brocast_send_count_ = 0;
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "APP Stop Broadcast!");
        
        // homi_speech_interface::srv::AssistantAbort::Request resMsg;
        // brocast_abort_client->async_send_request(std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>(resMsg));
        auto reqMsg= std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>();
        auto ret = brocast_abort_client->wait_for_service(std::chrono::seconds(1));
        if(ret==false)
        {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
        }
        auto result = brocast_abort_client->async_send_request(reqMsg);   
        
        // 取消定点移动
        RobdogCenter::getInstance().handleCancelMovement();
        
    } else {
        brocast_text = text;
        int secondOfDay = 0;
        int endSecondOfDay = 0;
        if (!jBody["time"]["secondOfDay"].isNull()){  
            secondOfDay = jBody["time"]["secondOfDay"].asInt();
        }
        if (!jBody["time"]["endSecondOfDay"].isNull()) {
            endSecondOfDay = jBody["time"]["endSecondOfDay"].asInt();
            if (endSecondOfDay != 0 && endSecondOfDay > secondOfDay) { // 如果是时间段的提醒
                brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40;}
            else{
                brocast_total_count_ = 1;// 说明是单点的提醒 (endSecondOfDay等于0也可能是单点提醒)
            }
        } else {
            // endSecondOfDay 字段不存在，默认处理为单点提醒
            brocast_total_count_ = 1;
        }
        long long mapId = 0;
        std::string current_mapid = "";
        if (!jBody["remindLocation"]["mapId"].isNull()) {
            mapId = jBody["remindLocation"]["mapId"].asInt64();
            current_mapid = std::to_string(mapId);
        }
        if(jBody["remindLocation"]["xCoordinate"].isNull() && jBody["remindLocation"]["yCoordinate"].isNull() && jBody["remindLocation"]["angle"].isNull()){
            // 原地播报
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start On-site broadcasting!!");
            brocast_text = text;
            timer_brocast->cancel();
            brocast_send_count_ = 0;
            timer_brocast->reset();
            SendBrocastCallback();
        }
        // 判断mapId是否一致，不一致则不前往也不播报
        else if(!current_mapid.empty() && current_mapid != RobotState::getInstance().getMapId()){
            return;
        }
        else{ // 默认同时有x、y、angle
            // 新造一个类似于平台的msg
            // homi_speech_interface::msg::SIGCEvent::SharedPtr broadcast_msg;
            auto broadcast_msg = std::make_shared<homi_speech_interface::msg::SIGCEvent>();
            Json::Value value_brocast;
            value_brocast["body"] = jBody;
            Json::StreamWriterBuilder writer;
            std::string jsonString = Json::writeString(writer, value_brocast);
            broadcast_msg->event = jsonString;  
            // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The message will be broadcasted, %s", broadcast_msg->event.c_str());             
            moveToTargetAndBrocast(broadcast_msg); // 放到定点移动模块去执行      
        }
    }
}
