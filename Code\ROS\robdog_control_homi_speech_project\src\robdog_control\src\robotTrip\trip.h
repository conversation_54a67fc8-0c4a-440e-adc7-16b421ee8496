#pragma once
#include <string>
#include <std_msgs/msg/bool.hpp>
#include "../robdogNode/robdog_ctrl_node.h"
#include <nav_msgs/msg/odometry.hpp>  
#include <chrono>  
#include <mutex>
#define MINDIS  3 //最短距离
#define MAXDIS  2500
#define RTKBASESTATUS      1
#define BATTERYLOWLEVEL    22
#define CHECK_RESULT_OK    1
#define CHECK_TRACE_RET_OK 0
#define CHECK_RESULT_FAIL1 2   //目的地与当前设备重合
#define CHECK_RESULT_FAIL2 3  //无法获取当前位置
#define CHECK_RESULT_FAIL3 4  //电量不足
#define CHECK_RESULT_FAIL4 5  //超出距离限制
#define CHECK_RESULT_FAIL5 6  //长时间位置未更新
#define CHECK_RESULT_FAIL6 7  //机器狗姿态异常
#define CHECK_RESULT_FAIL7 8  //机器狗温度过高   
#define CHECK_RESULT_FAIL8 9  //网络断开
#define CHECK_RESULT_FAIL9 10 //无法绕开障碍物
#define CHECK_RESULT_FAIL10 11//避障功能异常
#define CHECK_RESULT_FAIL11 12//无法通过高德API获取路径


#define ERROR_TRIP_POSITION_NOT_UPDATED       2010020001  // 长时间位置未更新
#define ERROR_TRIP_POSITION_MISMATCH          2010020002  // 设备当前位置与导航时不一致
#define ERROR_TRIP_BATTERY_INSUFFICIENT       2010020003  // 电量不足
#define ERROR_TRIP_NOT_STANDING               2010020004  // 机器狗姿态异常
#define ERROR_TRIP_RTK_SIGNAL_DISCONNECTED    2010020005  // 无法获取当前位置
#define ERROR_TRIP_JOINT_TEMPERATURE_OVERHEAT 2010020006  // 机器狗温度过高
#define ERROR_TRIP_NET_DISCONNECT             2010020007  // 网络断开
#define ERROR_TRIP_AVOID_OBSTACLE_FAILED      2010020008  // 无法绕开障碍物
#define ERROR_TRIP_AVOID_OBSTACLE_FUNCTION_ABNORM      2010020009  // 避障功能异常


#define ERROR_CAMERA_DATA 7101                      //出行无相机数据    
#define ERROR_ELEVATION_MAP 7102                    //无深程图
#define ERROR_OBSTACLE_AVOIDANCE_TIMEOUT 7103       //避障超时


#define    STATUS2_EPSILON  0.000001  // 厘米级阈值（约0.11米）
#define    STATUS1_EPSILON  0.00001   // 分米级阈值（约1.11米）

#define POS_EPSILON    0.01    // 位置变化阈值（单位：米）
#define JOINT_TMP_THRED 110
#define CPU_TMP_THRED 67
#define NO_MOVE_TIMEOUT 15.0
#define TRIP_SPEED  0.5
// 创建映射关系结构体
struct ErrorMapping {
    int errorTripCode;
    std::string errorMessage;
};

namespace TripErrors {
    inline const std::map<int, ErrorMapping>& get_error_map() {
        static const std::map<int, ErrorMapping> errorMappings = {
            {CHECK_RESULT_FAIL1, {ERROR_TRIP_POSITION_MISMATCH, "目的地与当前设备重合"}},
            {CHECK_RESULT_FAIL2, {ERROR_TRIP_RTK_SIGNAL_DISCONNECTED, "无法获取当前位置"}},
            {CHECK_RESULT_FAIL3, {ERROR_TRIP_BATTERY_INSUFFICIENT, "电量不足"}},
            {CHECK_RESULT_FAIL4, {ERROR_TRIP_POSITION_MISMATCH, "超出距离限制"}},  
            {CHECK_RESULT_FAIL5, {ERROR_TRIP_POSITION_NOT_UPDATED, "长时间位置未更新"}},
            {CHECK_RESULT_FAIL6, {ERROR_TRIP_NOT_STANDING, "机器狗姿态异常"}},
            {CHECK_RESULT_FAIL7, {ERROR_TRIP_JOINT_TEMPERATURE_OVERHEAT, "机器狗温度过高"}},
            {CHECK_RESULT_FAIL8, {ERROR_TRIP_NET_DISCONNECT, "网络断开"}},
            {CHECK_RESULT_FAIL9, {ERROR_TRIP_AVOID_OBSTACLE_FAILED, "无法绕开障碍物(避障超时)"}},
            {CHECK_RESULT_FAIL10, {ERROR_TRIP_AVOID_OBSTACLE_FUNCTION_ABNORM, "避障功能异常"}}
        };
        return errorMappings;
    }
};
