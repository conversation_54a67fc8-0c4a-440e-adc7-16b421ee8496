/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:05:05
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-01 10:26:34
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_config.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#pragma once
#include <fstream>
#include <tuple>
#include <unordered_map>
#include <map>
#include <chrono>
#include <string>
#include <jsoncpp/json/json.h>
#include <std_msgs/msg/string.hpp>
#include <rclcpp/rclcpp.hpp>

#include <homi_speech_interface/msg/sigc_event.hpp>
#include <homi_speech_interface/msg/robdog_action.hpp>
#include <homi_speech_interface/srv/sigc_data.hpp>
#include <homi_speech_interface/srv/net_ctrl.hpp>
#include <homi_speech_interface/msg/propriety_set.hpp>
#include <homi_speech_interface/msg/proper_to_app.hpp>
#include <homi_speech_interface/msg/continue_move.hpp>
#include <homi_speech_interface/msg/assistant_event.hpp>
#include <homi_speech_interface/msg/wakeup.hpp>
#include <homi_speech_interface/srv/assistant_speech_text.hpp>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/assistant_take_photo.hpp>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>
#include <homi_speech_interface/srv/set_diy_word.hpp>
#include <homi_speech_interface/msg/live_stream_task.hpp>

#include <homi_com/singleton.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>

#include <unitree/robot/go2/sport/sport_client.hpp>
#include <unitree/idl/go2/LowState_.hpp>
#include <unitree/common/dds/dds_easy_model.hpp>
#include <unitree/robot/channel/channel_publisher.hpp>
#include <unitree/robot/channel/channel_subscriber.hpp>
#include <unitree/idl/go2/SportModeState_.hpp>
#include <unitree/robot/go2/obstacles_avoid/obstacles_avoid_client.hpp>
#include <unitree/robot/b2/motion_switcher/motion_switcher_client.hpp>

#include <unitree/common/time/time_tool.hpp>
#include <unitree/idl/ros2/PointCloud2_.hpp>

using namespace unitree::common;
using namespace unitree::robot;
using namespace unitree::robot::go2;

#define BATTERY_LEVEL_1 10
#define BATTERY_LEVEL_2 20
#define BATTERY_LEVEL_3 30

#define TEMP_THREADHOLD 120.0
#define TEMP_THREADHOLD_UNITREE 76
#define CPU_TEMP_THREADHOLD 70.0
#define TEMP_TOOLOW_THREADHOLD 0.0
#define TEMP_LOW_THREADHOLD 10.0
#define HW_MOTION_CPU_OVERHEATING 1020040101
#define HW_MOTION_JOINT 102001 //HW 1 + MOTION 02 + JOINT 001
#define BATTERY_ALARM_BASE 10400101 // HW 1+ Battery 04 + battery1 001 + battery level 01
// limit alarm report freq
using namespace std;

class RobdogCtrlNode;

// 云深处狗子上报的数据类
class DeepRobotState {
public:
    int32_t robot_basic_state = 0;
    int32_t robot_gait_state = 0;
    std::array<double, 3> rpy = {0.0};            // Roll, Pitch, Yaw
    std::array<double, 3> rpy_vel = {0.0};        // Angular velocities
    std::array<double, 3> xyz_acc = {0.0};        // Acceleration in XYZ
    std::array<double, 3> pos_world = {0.0};      // Position in world coordinates
    std::array<double, 3> vel_world = {0.0};      // Velocity in world coordinates
    std::array<double, 3> vel_body = {0.0};       // Velocity in body coordinates
    uint32_t touch_down_and_stair_trot = 0;
    bool is_charging = false;
    uint32_t error_state = 0;
    int32_t robot_motion_state = 0;
    double battery_level = 0.0;
    int32_t task_state = 0;
    bool is_robot_need_move = false;
    bool zero_position_flag = false;
    std::array<double, 2> ultrasound = {0.0};     
    DeepRobotState(const char* data, RobdogCtrlNode* ctrl_ptr_);
};

class DeepRobotStateReceived {
public:
    int code;
    int size;
    int cons_code;
    DeepRobotState robot_state;
    DeepRobotStateReceived(const char* data, RobdogCtrlNode* ctrl_ptr_);
};


class RobotInfoMgr: public base::singleton<RobotInfoMgr>  {
public:
    RobotInfoMgr();
    ~RobotInfoMgr();
    void init(RobdogCtrlNode* ctrl_ptr_);
    void to_json(const DeepRobotState& state, Json::Value& value);
    void brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response);
    int checkActionState(const std::string& actionType, const std::string& actionArgument);
    bool utStandCheck();
    bool utStandDownCheck();
    bool utSitCheck();
    bool utLocomotionCheck();
    bool utMoveCheck();
    void utSetHighState(const void *message);
    void utSetLowState(const void *message);
    void utGetHighState(unitree_go::msg::dds_::SportModeState_ &sport_state_info);
    void utGetLowState(unitree_go::msg::dds_::LowState_ &low_state_info);
    std::string get_unitree_temperature_status(const double threshold, const double temp);
    std::string get_temperature_status();
    std::string get_cpu_temperature_status();

public:
    // 自动生成get和set函数
    //机器人实时状态（RobotBasicState RobotGaitState RobotMotionState这三个参数组合成）
	PropertyBuilderByName(HomiRobotStatus, RobotStatus, ROBDOG_STATUS_STATE);   
    using VelArray = std::array<double, 3>;
    std::array<double, 3> init_value = {0.0, 0.0, 0.0};
    PropertyBuilderByName(VelArray, RPY, init_value); //{0.0});   
    PropertyBuilderByName(VelArray, RPYVel, init_value); //{0.0});   
    PropertyBuilderByName(VelArray, XYZAcc, init_value); //{0.0});   
    PropertyBuilderByName(VelArray, PosWorld, init_value); //{0.0});   
    PropertyBuilderByName(VelArray, VelWorld, init_value); //{0.0});   
    PropertyBuilderByName(VelArray, VelBody, init_value); //{0.0});   
    PropertyBuilderByName(unsigned, TouchDownAndStairTrot, 0);   
    PropertyBuilderByName(bool, IsCharging, false);   
    PropertyBuilderByName(unsigned, ErrorState, 0);  
    PropertyBuilderByName(bool, ZeroPositionFlag, false);   
    PropertyBuilderByName(double, BatteryLevel, 90.0);   
    PropertyBuilderByName(unsigned, TaskState, 0);   
    PropertyBuilderByName(bool, IsRobotNeedMove, false);   
    using VelArray2 = std::array<double, 2>;
    std::array<double, 2> init_value2 = {0.0, 0.0};
    PropertyBuilderByName(VelArray2, Ultrasound, init_value2); //{0.0});
    PropertyBuilderByName(std::string, TempStatus, "Normal"); //{0.0});
    PropertyBuilderByName(std::string, CPUTempStatus, "Normal"); //{0.0});

    PropertyBuilderByName(HomiUtRobotStatus, UtRobotStatus, UT_ROBDOG_STATUS_STATE);   
private:
    //这三个参数作为云深处的数据，不对外提供，转成自定义的宏定义的状态类型去处理
    PropertyBuilderByName(int, RobotBasicState, 17);   
    PropertyBuilderByName(int, RobotGaitState, 0);   
    PropertyBuilderByName(unsigned, RobotMotionState, 0);  
    PropertyBuilderByName(double, RobotCPUTemperature, 20.0); // 新增CPU温度
    using VelArrayJointNum = std::array<double, MAX_JOINT_NUM>;
    std::array<double, MAX_JOINT_NUM> temp_value = {20.0, 20.0, 20.0, 20.0, 20.0, 20.0, 20.0, 20.0, 20.0, 20.0, 20.0, 20.0};
    PropertyBuilderByName(VelArrayJointNum, RobotTemperature, temp_value); // 关节温度
    // alarm publisher
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr robotInfoWarnPub_ = nullptr;
    // alarm publish state
    bool CPUTempPublished = false;
    std::array<bool, MAX_JOINT_NUM> JointTempPublished = {false};
    std::array<uint32_t, MAX_JOINT_NUM> MotorErrorPublished = {0};
    bool alarmBattery10Percent = false;
    bool alarmBattery20Percent = false;
    bool alarmBattery30Percent = false;
private:
    void sendrobdogcall();
    void receiveAndProcessData();
    void sendrobdogcall_temperature();
    void receiveAndProcessData_temperature();

    // ************************  接收到服务器数据后的异步回调
    void sendStringToBrocast(const std::string& message);
    void RobotBroadcastStatusToPlat(std::string device, double Temp);

    void batteryLevelAlarmHandle(double BatteryLevel);
    void CPUOverheatingAlarmHandle(uint32_t temp);
    void jointOverheatingAlarmHandle(double temp, int num);
    void utJointOverheatAction(double temp, int num);
    void utMotorAlarmHandle(std::array<uint32_t, 2> &reserve, int num);
    void utHighStateHandler(const void *message);
    void utLowStateHandler(const void *message);
    void utCloudHandler(const void *message);
    void utStateThreadCall();
    void utStateSet();
    void utHighStateDump(); 
    void utLowStateDump(); 
    void utStateDump();

    //服务器
    rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedPtr brocast_client;

public:
    RobdogCtrlNode* node_ctrl_ = nullptr;
    double robot_temperature = 0.0;

    ChannelSubscriberPtr<unitree_go::msg::dds_::SportModeState_> suber_sport;
    ChannelSubscriberPtr<unitree_go::msg::dds_::LowState_> suber_low;
    ChannelSubscriberPtr<sensor_msgs::msg::dds_::PointCloud2_> suber_cloud;
};
