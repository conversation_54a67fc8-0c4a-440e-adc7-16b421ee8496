cmake_minimum_required(VERSION 3.8)
project(launch_package)

if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)

install(DIRECTORY launch DESTINATION share/${PROJECT_NAME})
install(FILES configs/robot_config.yaml DESTINATION share/${PROJECT_NAME}/configs)
install(FILES configs/follow_config.yaml DESTINATION share/${PROJECT_NAME}/configs)
install(FILES configs/handle_network.yaml DESTINATION share/${PROJECT_NAME}/configs)
install(FILES configs/robot_info.yaml DESTINATION share/${PROJECT_NAME}/configs)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  set(ament_cmake_copyright_FOUND TRUE)
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
