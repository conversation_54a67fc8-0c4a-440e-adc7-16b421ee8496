#ifndef __SRC_HOMI_INCLUDE_HOMI_ABILITY_H_
#define __SRC_HOMI_INCLUDE_HOMI_ABILITY_H_
#include <string>
#include <functional>
namespace homi
{
    int registerAbilityPhoneCall(const std::function<void(const std::string &phoneNumber)> &func);

    int registerAbilityVoicePlay(const std::function<void(const std::string &url)> &func);

    int registerAbilityVoicePlay(const std::function<void(const std::string &url,const std::string &text,const std::string &source)> &func);

    int registerAbilityAudioMeidaPlay(const std::function<void(const std::string &raw)> &func);
/* 
    infoJson 和 nextJson的json格式
{
    "url": "https://xxxx",
    "type": "类型",
    "codec": "音频格式",
    "id": "内容id",
    "len": 153,
    "name": "内容名称",
    "author":"xxx",
    "pic": "图片"
}
*/
    int registerAbilityAudioMeidaPlay(const std::function<void(const std::string &subEvent,const std::string &playListUrl,const std::string &infoJson,const std::string &nextJson,const bool infinity)> &func);

    int requestAudioMediaPlayNext(const std::string &currentId,const std::string &instruction="next");
}


#endif  // __SRC_HOMI_INCLUDE_HOMI_ABILITY_H_

