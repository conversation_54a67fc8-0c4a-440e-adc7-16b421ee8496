/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: Go2FrontVideoData_.idl
  Source: Go2FrontVideoData_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_GO2FRONTVIDEODATA__HPP
#define DDSCXX_UNITREE_IDL_GO2_GO2FRONTVIDEODATA__HPP

#include <cstdint>
#include <vector>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class Go2FrontVideoData_
{
private:
 uint64_t time_frame_ = 0;
 std::vector<uint8_t> video720p_;
 std::vector<uint8_t> video360p_;
 std::vector<uint8_t> video180p_;

public:
  Go2FrontVideoData_() = default;

  explicit Go2FrontVideoData_(
    uint64_t time_frame,
    const std::vector<uint8_t>& video720p,
    const std::vector<uint8_t>& video360p,
    const std::vector<uint8_t>& video180p) :
    time_frame_(time_frame),
    video720p_(video720p),
    video360p_(video360p),
    video180p_(video180p) { }

  uint64_t time_frame() const { return this->time_frame_; }
  uint64_t& time_frame() { return this->time_frame_; }
  void time_frame(uint64_t _val_) { this->time_frame_ = _val_; }
  const std::vector<uint8_t>& video720p() const { return this->video720p_; }
  std::vector<uint8_t>& video720p() { return this->video720p_; }
  void video720p(const std::vector<uint8_t>& _val_) { this->video720p_ = _val_; }
  void video720p(std::vector<uint8_t>&& _val_) { this->video720p_ = _val_; }
  const std::vector<uint8_t>& video360p() const { return this->video360p_; }
  std::vector<uint8_t>& video360p() { return this->video360p_; }
  void video360p(const std::vector<uint8_t>& _val_) { this->video360p_ = _val_; }
  void video360p(std::vector<uint8_t>&& _val_) { this->video360p_ = _val_; }
  const std::vector<uint8_t>& video180p() const { return this->video180p_; }
  std::vector<uint8_t>& video180p() { return this->video180p_; }
  void video180p(const std::vector<uint8_t>& _val_) { this->video180p_ = _val_; }
  void video180p(std::vector<uint8_t>&& _val_) { this->video180p_ = _val_; }

  bool operator==(const Go2FrontVideoData_& _other) const
  {
    (void) _other;
    return time_frame_ == _other.time_frame_ &&
      video720p_ == _other.video720p_ &&
      video360p_ == _other.video360p_ &&
      video180p_ == _other.video180p_;
  }

  bool operator!=(const Go2FrontVideoData_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::Go2FrontVideoData_>::getTypeName()
{
  return "unitree_go::msg::dds_::Go2FrontVideoData_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::Go2FrontVideoData_>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::Go2FrontVideoData_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::Go2FrontVideoData_>::type_map_blob_sz() { return 386; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::Go2FrontVideoData_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::Go2FrontVideoData_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x78,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0x75,  0x67,  0x61,  0x59,  0x75,  0x39,  0xc1, 
 0x45,  0x05,  0x1d,  0x51,  0xbe,  0x71,  0xb1,  0x00,  0x60,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x50,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x08,  0x2d,  0x24,  0x8b,  0x17,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf3,  0x01,  0x00,  0x00,  0x02, 
 0xd5,  0x57,  0x60,  0x01,  0x10,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf3, 
 0x01,  0x00,  0x00,  0x02,  0x1d,  0x38,  0x7d,  0x67,  0x10,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x80,  0xf3,  0x01,  0x00,  0x00,  0x02,  0x04,  0xb8,  0xc7,  0x48,  0xdc,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0xf2,  0xc8,  0x74,  0x27,  0xb8,  0x30,  0xbd,  0x3d,  0x37,  0xbe,  0x7b,  0xec, 
 0x40,  0x49,  0xe1,  0x00,  0xc4,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x32,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x2a,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x47, 
 0x6f,  0x32,  0x46,  0x72,  0x6f,  0x6e,  0x74,  0x56,  0x69,  0x64,  0x65,  0x6f,  0x44,  0x61,  0x74,  0x61, 
 0x5f,  0x00,  0x00,  0x00,  0x84,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x08,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x74,  0x69,  0x6d,  0x65, 
 0x5f,  0x66,  0x72,  0x61,  0x6d,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf3,  0x01,  0x00,  0x00,  0x02,  0x0a,  0x00,  0x00,  0x00, 
 0x76,  0x69,  0x64,  0x65,  0x6f,  0x37,  0x32,  0x30,  0x70,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf3,  0x01,  0x00,  0x00,  0x02,  0x0a,  0x00,  0x00,  0x00, 
 0x76,  0x69,  0x64,  0x65,  0x6f,  0x33,  0x36,  0x30,  0x70,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf3,  0x01,  0x00,  0x00,  0x02,  0x0a,  0x00,  0x00,  0x00, 
 0x76,  0x69,  0x64,  0x65,  0x6f,  0x31,  0x38,  0x30,  0x70,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0xf2,  0xc8,  0x74,  0x27,  0xb8,  0x30,  0xbd,  0x3d,  0x37,  0xbe,  0x7b,  0xec, 
 0x40,  0x49,  0xe1,  0xf1,  0x75,  0x67,  0x61,  0x59,  0x75,  0x39,  0xc1,  0x45,  0x05,  0x1d,  0x51,  0xbe, 
 0x71,  0xb1, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::Go2FrontVideoData_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x75,  0x67,  0x61,  0x59,  0x75,  0x39,  0xc1,  0x45,  0x05,  0x1d,  0x51, 
 0xbe,  0x71,  0xb1,  0x00,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0xc8,  0x74,  0x27,  0xb8,  0x30,  0xbd,  0x3d,  0x37,  0xbe,  0x7b,  0xec, 
 0x40,  0x49,  0xe1,  0x00,  0xc8,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::Go2FrontVideoData_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::Go2FrontVideoData_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::Go2FrontVideoData_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::Go2FrontVideoData_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::Go2FrontVideoData_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.time_frame()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.video720p().size());
      if (!write(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !write(streamer, instance.video720p()[0], se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.video360p().size());
      if (!write(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !write(streamer, instance.video360p()[0], se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.video180p().size());
      if (!write(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !write(streamer, instance.video180p()[0], se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::Go2FrontVideoData_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::Go2FrontVideoData_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::Go2FrontVideoData_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.time_frame()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.video720p().size());
      if (!read(streamer, se_1))
        return false;
      instance.video720p().resize(se_1);
      if (se_1 > 0 &&
          !read(streamer, instance.video720p()[0], se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.video360p().size());
      if (!read(streamer, se_1))
        return false;
      instance.video360p().resize(se_1);
      if (se_1 > 0 &&
          !read(streamer, instance.video360p()[0], se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.video180p().size());
      if (!read(streamer, se_1))
        return false;
      instance.video180p().resize(se_1);
      if (se_1 > 0 &&
          !read(streamer, instance.video180p()[0], se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::Go2FrontVideoData_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::Go2FrontVideoData_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::Go2FrontVideoData_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.time_frame()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.video720p().size());
      if (!move(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !move(streamer, uint8_t(), se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.video360p().size());
      if (!move(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !move(streamer, uint8_t(), se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.video180p().size());
      if (!move(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !move(streamer, uint8_t(), se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::Go2FrontVideoData_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::Go2FrontVideoData_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::Go2FrontVideoData_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.time_frame()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !max(streamer, uint8_t(), se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !max(streamer, uint8_t(), se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, true))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      if (se_1 > 0 &&
          !max(streamer, uint8_t(), se_1))
        return false;
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::Go2FrontVideoData_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::Go2FrontVideoData_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_GO2FRONTVIDEODATA__HPP
