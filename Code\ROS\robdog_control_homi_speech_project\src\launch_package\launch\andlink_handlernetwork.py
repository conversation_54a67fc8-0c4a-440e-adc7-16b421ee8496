import launch
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, IncludeLaunchDescription, TimerAction
from launch.substitutions import LaunchConfiguration
from launch_ros.substitutions import FindPackageShare
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory



def generate_launch_description():
    config_share_dir = get_package_share_directory('launch_package')

    handlernetwork = Node(
        package="handlernetwork",
        executable="command_server",
        name='handlernetwork_node',
        output='log',
        parameters=[config_share_dir + '/configs/handle_network.yaml'],
    )
    
    
    # andlink = Node(
    #     package="andlink",
    #     executable="andlinkscript",
    #     #name='andlink_node',  
    #     output='log',  
    #     respawn=True
    # )


    
    return LaunchDescription([
        handlernetwork
        # andlink
    ])
