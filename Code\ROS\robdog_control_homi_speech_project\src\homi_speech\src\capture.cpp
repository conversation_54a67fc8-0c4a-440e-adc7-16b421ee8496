#include <rclcpp/rclcpp.hpp>
#include <homi_speech_interface/msg/pcm_stream.hpp>
#include <homi_speech/def.h>
#include <homi_speech/AlsaHelper.h>
#include <homi_speech/json.hpp> 
#include <string>
#include <thread>
#include <atomic>
#include <chrono>
std::atomic<bool> _needrestart;
int main(int argc, char *argv[])
{
    setlocale(LC_ALL, "");
    rclcpp::init(argc, argv);
    auto capture_node = rclcpp::Node::make_shared("capture",rclcpp::NodeOptions().automatically_declare_parameters_from_overrides(true));
    RCLCPP_INFO(rclcpp::get_logger("capture"),"capture start up");
    // std::string alsaCaptureDeviceName;
    std::string usbShPath;
    std::string usbName;
    std::string pcm_stream_topic;
    int channelNumb = 1;
    _needrestart = false;
    if(!capture_node->get_parameter(PARAM_ALSA_CAPTURE_SH_NAME,usbShPath)
        ||!capture_node->get_parameter(PARAM_ALSA_CAPTURE_SH_PARAM,usbName)
        ||!capture_node->get_parameter(PARAM_ALSACAPTURE_CHANNEL,channelNumb)
        ||!capture_node->get_parameter(PARAM_PCM_STREAM_TOPIC,pcm_stream_topic))
    {
        RCLCPP_ERROR(rclcpp::get_logger("capture"),"read param error ,exit");
        return -1;
    }
    long long lcount = 0;
    auto timer = capture_node->create_wall_timer(std::chrono::seconds(5),[&lcount](){
        lcount++;
        RCLCPP_INFO(rclcpp::get_logger("capture"),"idle ,run count =%lld s ...",(lcount*5));
        if(_needrestart)
        {
            RCLCPP_WARN(rclcpp::get_logger("capture"),"restart ");
            exit(-1);
        }
        _needrestart = true;
    });
    auto pushStream = capture_node->create_publisher<homi_speech_interface::msg::PCMStream>(pcm_stream_topic,50);
    bool stop=false;
    auto mythread = std::thread([&stop,&pushStream,usbShPath,usbName,channelNumb](){
        homi_speech::AlsaHelperCapture capture;
        int cardId=-1;
        std::string alsaCaptureDeviceName="default";
        try
        {
            auto usbjson = nlohmann::json::parse(usbName);
            auto usb_name = usbjson["name"];
            for(auto e:usb_name)
            {
                auto nameTmp = e.get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger("capture"),"usbname=%s",nameTmp.c_str());
                if(nameTmp=="default")
                {
                    cardId = 0;
                    break;
                }
                cardId = homi_speech::getCardId(usbShPath,nameTmp);
                if(cardId>=0)
                {
                    alsaCaptureDeviceName = "plughw:" + std::to_string(cardId) + ",0";
                    break;
                }
            }
        }
        catch(const std::exception& e)
        {
            RCLCPP_ERROR(rclcpp::get_logger("capture"),"%s",e.what());
            exit(-1);
        }
        if(cardId<0)
        {
            RCLCPP_ERROR(rclcpp::get_logger("capture"),"cardId<0");
            exit(-1);
        }
        RCLCPP_INFO(rclcpp::get_logger("capture"),"alsaCaptureDeviceName=%s channelNumb=%d",alsaCaptureDeviceName.c_str(),channelNumb);
        RCLCPP_INFO(rclcpp::get_logger("capture"),"open1");
        capture.openAndConfig(alsaCaptureDeviceName,"S16_LE",channelNumb,16000);
        std::this_thread::sleep_for(std::chrono::milliseconds(250));
        capture.close();
        std::this_thread::sleep_for(std::chrono::milliseconds(250));
        capture.openAndConfig(alsaCaptureDeviceName,"S16_LE",channelNumb,16000);
        RCLCPP_INFO(rclcpp::get_logger("capture"),"open2");
        auto periodSize = capture.getPeriodSize();
        auto periodBytes = capture.getPeriodBytes();
        unsigned int count=0;
        RCLCPP_INFO(rclcpp::get_logger("capture"),"periodSize=%lu periodBytes=%u",periodSize,periodBytes);
        while(!stop)
        {
            try
            {
                count++;
                auto now = std::chrono::system_clock::now();
                auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
                _needrestart = false;
                auto captureDataPtr = std::make_shared<std::vector<unsigned char>>(periodBytes);
                auto readSize = capture.readi(captureDataPtr->data(),periodSize);               
                readSize = periodSize *2;
                std::vector<unsigned char> channelData(readSize);
                unsigned char *uiBuffer = captureDataPtr->data();
                auto buffer = channelData.data();
                for(int i=0;i<(int)periodSize;i++)
                {
                    buffer[2*i] = uiBuffer[2*i*channelNumb];
                    buffer[2*i+1] = uiBuffer[2*i*channelNumb+1];
                }
                
                
                homi_speech_interface::msg::PCMStream msg;
                msg.ts = ts;
                msg.data = std::vector<unsigned char>(buffer,buffer+readSize);
                pushStream->publish(msg);

                if(count%(50*5)==0)
                {
                    RCLCPP_INFO(rclcpp::get_logger("capture"),"update capture %d alsaCaptureDeviceName=%s",readSize,alsaCaptureDeviceName.c_str());
                }
            }
            catch(...)
            {
                RCLCPP_WARN(rclcpp::get_logger("capture"),"capture reset");
                capture.close();
                capture.openAndConfig(alsaCaptureDeviceName,"S16_LE",channelNumb,16000);
                RCLCPP_INFO(rclcpp::get_logger("capture"),"alsaCaptureDeviceName=%s",alsaCaptureDeviceName.c_str());
                periodSize = capture.getPeriodSize();
                periodBytes = capture.getPeriodBytes();                
            }
        }
    });
    rclcpp::spin(capture_node);
    stop = true;
    if(mythread.joinable())
    {
        mythread.join();
    }
    RCLCPP_INFO(rclcpp::get_logger("capture"),"capture end");
    return 0;
}