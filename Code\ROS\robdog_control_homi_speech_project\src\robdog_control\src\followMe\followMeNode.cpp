#include "followMeNode.h" 
#include <cstdlib> 
#include <chrono>  

using namespace std::chrono_literals; 

FollowNode::FollowNode(const rclcpp::NodeOptions & options)
    : Node("follow_node", options) { 
    RCLCPP_INFO(this->get_logger(), "Initializing FollowNode...");

    std::string follow_me_status = "/task_status";
    audio_command_subscription_ = this->create_subscription<std_msgs::msg::String>(follow_me_status,10, 
                                std::bind(&FollowNode::follow_me_status_callback, this, std::placeholders::_1));
    RCLCPP_INFO(this->get_logger(), "Subscribed to follow_me command topic: %s", follow_me_status.c_str());

    target_position_sub_=this->create_subscription<geometry_msgs::msg::PoseStamped>("/target_calibration_req/pose", 100,
        std::bind(&FollowNode::target_position_callback, this, std::placeholders::_1));

    RCLCPP_INFO(this->get_logger(), "Subscribed to target position command topic: /target_calibration_req/pose");

    client_assistant_abort_ = this->create_client<homi_speech_interface::srv::AssistantAbort>(
        "/homi_speech/helper_assistant_abort_service");
    client_set_wake_event_ = this->create_client<homi_speech_interface::srv::SetWakeEvent>(
        "/audio_node/set_wake_event_service");
    platClint_ = this->create_client<homi_speech_interface::srv::SIGCData>(
        "/homi_speech/sigc_data_service");

    RCLCPP_INFO(this->get_logger(), "Service clients created.");

    if (!client_assistant_abort_->wait_for_service(2s)) {
        RCLCPP_WARN(this->get_logger(), "Service '/homi_speech/helper_assistant_abort_service' not available after 2s.");
    }
    if (!client_set_wake_event_->wait_for_service(2s)) {
        RCLCPP_WARN(this->get_logger(), "Service '/audio_node/set_wake_event_service' not available after 2s.");
    }
    if (!platClint_->wait_for_service(2s)) {
        RCLCPP_WARN(this->get_logger(), "Service '/homi_speech/sigc_data_service' not available after 2s.");
    }

    watchdogTimer_ = this->create_wall_timer(std::chrono::milliseconds(500), 
        std::bind(&FollowNode::watchDogforsubTargetCalibrationReq, this));

    pubCatchPos_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("catch_turtle/posestamped", 10);
    pubTargetPos_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("/target_turtle/posestamped", 10);
    pullUpProcessPub_=this->create_publisher<std_msgs::msg::String>("/follow_me_control", 10);
    velPub_ = this->create_publisher<geometry_msgs::msg::Twist>("/catch_turtle/ctrl_instruct", 10); 
    mode_publisher_=this->create_publisher<std_msgs::msg::UInt8>("/operation_mode", rclcpp::SystemDefaultsQoS().reliable().transient_local());

    subPubRbtSever_ = this->create_subscription<std_msgs::msg::String>("/follow_me/from_control", 10, 
        std::bind(&FollowNode::subRbtServerInfo, this, std::placeholders::_1));
    tripsigSub_ = this->create_subscription<std_msgs::msg::String>("/navigation_control", 10, 
        std::bind(&FollowNode::tripsigHandle, this, std::placeholders::_1));
    navPositionSub_ = this->create_subscription<std_msgs::msg::String>("/navigation_position", 
        10, std::bind(&FollowNode::navCallback, this, std::placeholders::_1));

    RCLCPP_INFO(this->get_logger(), "FollowNode initialized successfully.");
}
void FollowNode::sendDataToPlatform(const std::string &data) {
    auto request_sigc_data = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();
    request_sigc_data->data = data;  
    if (!platClint_->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "SERVICE assistant NOT available");
        return;
    }
    auto response_callback = 
        [this](rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedFuture future) {
            try {
                auto response = future.get();
                if (response->error_code!=SERVICE_SUCCESS) {
                    RCLCPP_WARN(this->get_logger(), "service call failed: %d", response->error_code);
                }
            } catch (const std::exception& e) {
                RCLCPP_ERROR(this->get_logger(), "service call exception: %s", e.what());
            }
        };
    auto future = platClint_->async_send_request(request_sigc_data, response_callback);
}
void FollowNode::sendWarningandNotifyAPP(int errorCode, const std::string& errorSource, std::string& errorMsg) {
    auto body = asembleWarningBody(errorCode, errorSource, errorMsg);
    sendWarnigtoPlat(body);
    nofifyAPPFollowStatus(errorCode);
    bContinue_ = false;
    stopFollow();
}

Json::Value FollowNode::asembleWarningBody(int code,const std::string &alarmName,std::string &alarmDesc)
{
    Json::Value body;
    body["alarmCode"] = code;
    body["alarmName"] = alarmName;
    body["alarmLevel"] = 1;
    body["alarmType"] = "主动跟随告警";
    Json::Value notifyWay1(Json::arrayValue);
    notifyWay1.append(1);
    notifyWay1.append(3);
    Json::Value notifyWay2(Json::arrayValue);
    notifyWay2.append(1);
    notifyWay2.append(2);
    body["notifystrategy"] = Json::Value(Json::arrayValue);
    body["alarmDesc"] = alarmDesc;
    body["launcherModel"] = "FollowMe";
    return body;
}

void FollowNode::follow_me_status_callback(const std_msgs::msg::String::SharedPtr msg) {
    std::string followStatus = msg->data;
    RCLCPP_INFO(this->get_logger(), "Received follow me status: '%s'", followStatus.c_str());
    Json::Reader reader;
    Json::Value json_msg;
    if (!reader.parse(msg->data, json_msg))
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to parse JSON message");
        return;
    }
    if (!bStartServer_)
        return;    
    if (current_mode_ ==OperationMode::TRIP_MODE)
        return;     
    int code = json_msg["code"].asInt();
    std::string message = json_msg["msg"]["msg"].asString();
    switch (code)
    {
        case FollowNodeStarted:
        {
            RCLCPP_INFO(this->get_logger(), "alreadyStarted_=%d", alreadyStarted_);
            RCLCPP_INFO(this->get_logger(), "The algorithm is loaded successfully, and the following target is being detected.code=%d", code);
            break;
        }
        case FollowNodeStopped:
            break;
        case FollowFunctionInitialized:
            break;
        case FollowFunctionPaused:
            break;
        case TargetDetected:
        {
            RCLCPP_INFO(this->get_logger(), "Detect the target.code=%d", code);
            play_audio_async(getResourcePath("audio/follow/Locked_you.wav"));
            break;
        }
        case InvalidMessageFormat:
            break;
        case NoUWBData:
        {
            std::string errorMsg = json_msg["msg"]["error"].asString();
            RCLCPP_WARN(this->get_logger(), "No UWB Data.code=%d", code);
            play_audio_async(getResourcePath("audio/follow/NoUWBChangeVison.wav"));
            break;
        }
        case NoCameraData:
        {
            RCLCPP_WARN(this->get_logger(), "No Camera Data.code=%d", code);
            std::string errorMsg = json_msg["msg"]["error"].asString();
            play_audio_async(getResourcePath("audio/follow/NoCamera.wav"));
            auto body=asembleWarningBody(code,"NoUWBData",errorMsg);
            sendWarnigtoPlat(body);
            nofifyAPPFollowStatus(code);
            bContinue_=false;
            stopFollow();
            break;
        }
        case TargetLostDuringTracking:
        {
            std::string errorMsg = json_msg["msg"]["error"].asString();
            {
                std::lock_guard<std::mutex> lock(msg_cnt_mutex_);
                msg_cnt_++;
            }
            if (msg_cnt_ % 90 == 0) {
                play_audio_async(getResourcePath("audio/follow/LostTarget.wav"));
            }
            bContinue_=false;
            auto twist_msg = std::make_unique<geometry_msgs::msg::Twist>();
            twist_msg->linear.x = 0.0;  
            twist_msg->linear.y = 0.0;
            twist_msg->linear.z = 0.0;
            twist_msg->angular.z = (lastPose.pose.position.y > 0) ? -0.5 : 0.5;
            twist_msg->angular.x = 0.0;
            twist_msg->angular.y = 0.0;
            velPub_->publish(std::move(twist_msg));
            RCLCPP_INFO_THROTTLE(this->get_logger(),*this->get_clock(),2000, "Target lost,rotate with speed 0.5");
            auto elapsed_time = std::chrono::duration_cast<std::chrono::seconds>(Clock::now() - lastTargetCaliReqTime).count();
            RCLCPP_INFO_THROTTLE(this->get_logger(),*this->get_clock(),2000, "this->now()=%lld, lastTargetCaliReqTime=%lld,elapsed_time=%lld",Clock::now(),lastTargetCaliReqTime, elapsed_time);
            if (elapsed_time > FOLLOW_TARGET_LOSE_TIMEOUT) {
                bContinue_ = false; 
                RCLCPP_WARN(this->get_logger(),"Lost Target  for more than 15 seconds. Stop follow.");
                play_audio_async(getResourcePath("audio/follow/LostFinally.wav"));
                auto body=asembleWarningBody(code,"TargetLostDuringTracking",errorMsg);
                sendWarnigtoPlat(body);
                nofifyAPPFollowStatus(code);
                stopFollow();
            }
            break;
        }
        case ObstacleAvoidanceError_Elevation_Map:
        case ObstacleAvoidanceError_TIMEOUT:
        case ObstacleAvoidanceError_DeepCam:
        {
            std::string errorMsg = json_msg["msg"]["error"].asString();
            RCLCPP_WARN(this->get_logger(), "The obstacle avoidance fails.code=%d", code);
            bContinue_ = false; 
            auto body=asembleWarningBody(ObstacleAvoidanceError,"ObstacleAvoidanceError",errorMsg);
            sendWarnigtoPlat(body);
            nofifyAPPFollowStatus(code);
            play_audio_async(getResourcePath("audio/follow/ObstacleAvoidance.wav"));
            stopFollow();
            break;
        }
        case NoFollowTargetDetectedFirstTime:
        {
            RCLCPP_WARN(this->get_logger(), "No Follow Target Detected first time.code=%d", code);
            play_audio_async(getResourcePath("audio/follow/NotargetFront.wav"));  
            break;
        }
        case NoFollowTargetDetected:
        {
            RCLCPP_WARN(this->get_logger(), "No Follow Target Detected=%d", code);
            std::string errorMsg = json_msg["msg"]["error"].asString();
            play_audio_async(getResourcePath("audio/follow/NotargetFrontFinally.wav"));  
            bContinue_=false;
            auto body=asembleWarningBody(code,"NoFollowTargetDetected",errorMsg);
            sendWarnigtoPlat(body);
            nofifyAPPFollowStatus(code);
            stopFollow();   
            break;
        }
        case SUMMON_FUNCTION_STARTED:
        {
            RCLCPP_INFO(this->get_logger(), "The Fixed-Point Summoning mission is opened.code=%d", code);
            play_audio_async(getResourcePath("audio/follow/SummonStart.wav"));
            break;
        }
        case SUMMON_FUNCTION_REACHED:
        {
            RCLCPP_INFO(this->get_logger(), "The Fixed-Point Summoning mission ends code=%d", code);
            play_audio_async(getResourcePath("audio/follow/SummonEND.wav"));  
            bContinue_=false;
            stopFollow();   
            break;
        }
        case SUMMON_WITHOUT_UWB:
        {
            RCLCPP_WARN(this->get_logger(), "The Fixed-Point Summoning mission ends as NO UWB code=%d", code);
            play_audio_async(getResourcePath("audio/follow/SummonNoUWB.wav"));  
            bContinue_=false;
            stopFollow();   
            break;
        }
        default:
            break;

    }
}

void FollowNode::play_audio_async(const std::string& audio_file_path) {
    std::thread([this, audio_path = std::string(audio_file_path)]() {
        try {
            RCLCPP_INFO(this->get_logger(), "Async audio playback started for: %s", audio_path.c_str());

            if (!client_assistant_abort_ || !client_set_wake_event_) {
                throw std::runtime_error("Service clients are not initialized");
            }
            auto send_abort_request = [this]() {
                if (client_assistant_abort_->service_is_ready()) {
                    auto request = std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>();
                    client_assistant_abort_->async_send_request(request);
                    RCLCPP_INFO(this->get_logger(), "Sent AssistantAbort request");
                } else {
                    RCLCPP_WARN(this->get_logger(), "AssistantAbort service not ready");
                }
            };
            auto disable_wake_event = [this]() {
                if (client_set_wake_event_->service_is_ready()) {
                    auto request = std::make_shared<homi_speech_interface::srv::SetWakeEvent::Request>();
                    request->target = false;
                    client_set_wake_event_->async_send_request(request);
                    RCLCPP_INFO(this->get_logger(), "Disabled wake event");
                } else {
                    RCLCPP_WARN(this->get_logger(), "SetWakeEvent service not ready");
                }
            };
            auto play_audio = [this, &audio_path]() {
                const std::string command = "aplay \"" + audio_path + "\"";
                RCLCPP_INFO(this->get_logger(), "Executing: %s", command.c_str());
                
                const int ret = std::system(command.c_str());
                if (ret != 0) {
                    throw std::runtime_error("Audio playback failed with code: " + std::to_string(ret));
                }
            };
            struct WakeEventEnabler {
                rclcpp::Client<homi_speech_interface::srv::SetWakeEvent>::SharedPtr client;
                rclcpp::Logger logger;

                WakeEventEnabler(rclcpp::Client<homi_speech_interface::srv::SetWakeEvent>::SharedPtr c, 
                               rclcpp::Logger l) : client(c), logger(l) {}
                
                ~WakeEventEnabler() {
                    try {
                        if (client && client->service_is_ready()) {
                            auto request = std::make_shared<homi_speech_interface::srv::SetWakeEvent::Request>();
                            request->target = true;
                            client->async_send_request(request);
                            RCLCPP_INFO(logger, "Re-enabled wake event");
                        }
                    } catch (...) {
                        RCLCPP_ERROR(logger, "Failed to re-enable wake event in destructor");
                    }
                }
            };

            send_abort_request();
            disable_wake_event();
            WakeEventEnabler enabler(client_set_wake_event_, this->get_logger());
            std::this_thread::sleep_for(std::chrono::seconds(2));   //CSPID-2025-000U6J
            play_audio();

            RCLCPP_INFO(this->get_logger(), "Audio playback completed successfully");

        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Exception in audio thread: %s", e.what());
        } catch (...) {
            RCLCPP_ERROR(this->get_logger(), "Unknown exception in audio thread");
        }
    }).detach(); // 分离线程自主运行
}

std::string FollowNode::getResourcePath(const std::string &file_name){
  static const std::string package_share_dir = ament_index_cpp::get_package_share_directory("robdog_control");
  auto pathstr= package_share_dir + "/resource/" + file_name;
  RCLCPP_INFO(this->get_logger(), "file dir is : %s", pathstr.c_str());
  return pathstr;
}

void FollowNode::target_position_callback(const geometry_msgs::msg::PoseStamped::SharedPtr msg){
    if (!bStartServer_){
        return;
    }
    msg_cnt_=0;
    geometry_msgs::msg::PoseStamped pos;
    pos.header.stamp = this->get_clock()->now();
    pos.pose = msg->pose;
    if (pubTargetPos_) {
        pubTargetPos_->publish(pos);
    } else {
        RCLCPP_ERROR(this->get_logger(), "Publisher not initialized!");
    }
    lastPose=pos;
    lastTargetCaliReqTime= Clock::now();
    bContinue_=true;
    alreadyStarted_=1;
    if (pubCatchPos_) {
        pubCatchPos_->publish(pos);
    } else {
        RCLCPP_ERROR(this->get_logger(), "Publisher not initialized!");
    }
    RCLCPP_INFO_THROTTLE(this->get_logger(),*this->get_clock(),1000,"[Target POS] target_calibration_req target pos: %f, %f", pos.pose.position.x, pos.pose.position.y);
}

void FollowNode::watchDogforsubTargetCalibrationReq(){
    if (!bStartServer_){
        return;
    }
    if (bContinue_) {
        const auto  elapsed_time = std::chrono::duration_cast<std::chrono::seconds>(Clock::now() - lastTargetCaliReqTime).count();
        if (elapsed_time > NO_TARGETPOSION_TIMEOUT) {
            RCLCPP_INFO(this->get_logger(),"No Target Calibration Req message for more than two seconds. Stop Move.");
            bContinue_ = false; 
        }
        if(RobotInfoMgr::getInstance().getBatteryLevel()<=FOLLOW_BATTERYLOWLEVEL)
        {
            std::string errMsg="battery is too low";
            sendWarningandNotifyAPP(LowBattery,"LowBattery",errMsg);
            RCLCPP_WARN(this->get_logger(), "battery is too low,stop follow");
            play_audio_async(getResourcePath("audio/follow/lowPower.wav"));  
        }
        auto robotTemperature=RobotInfoMgr::getInstance().getRobotTemperature();
        for (double temp : robotTemperature) {
            if (temp > FOLLOW_JOINT_TMP_THRED)   
            {
                std::string errMsg="tempature is too high";
                sendWarningandNotifyAPP(AbnormalTemperatures,"tempature is too high",errMsg);
                RCLCPP_WARN(this->get_logger(), "tempature is too high,stop follow");
            }
        }
        if(RobotInfoMgr::getInstance().getRobotCPUTemperature()>FOLLOW_CPU_TMP_THRED)
        {
            std::string errMsg="tempature is too high";
            sendWarningandNotifyAPP(AbnormalTemperatures,"tempature is too high",errMsg);
            RCLCPP_WARN(this->get_logger(), "tempature is too high,stop follow");
        }
    }
}
void FollowNode::pull_up_down_processs(int switchKey){
    Json::Value json_msg;
    json_msg["action"]=switchKey;
    Json::StreamWriterBuilder writer;
    std::string json_str = Json::writeString(writer, json_msg);
    std_msgs::msg::String msg;
    msg.data = json_str;
    pullUpProcessPub_->publish(msg);
    RCLCPP_INFO(this->get_logger(), "pull_up_processs Sent message: '%s'", msg.data.c_str());
}

void FollowNode::subRbtServerInfo(const std_msgs::msg::String::SharedPtr msg){
    std::string strSendMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    if(!reader.parse(strSendMsg, value)){
        RCLCPP_INFO(this->get_logger(),"subR_bt_ServerInfo function Json parse error");
        return; 
    }
    std::string strAction = value["action"].asString();
    if ("startFollow" == strAction){
        if(RobotInfoMgr::getInstance().getBatteryLevel()<=FOLLOW_BATTERYLOWLEVEL){
            std::string errMsg="battery is too low";
            sendWarningandNotifyAPP(LowBattery,"LowBattery",errMsg);
            RCLCPP_WARN(this->get_logger(), "battery is too low,stop follow");
            play_audio_async(getResourcePath("audio/follow/lowPower.wav"));  
            return;
        }
        {
            current_mode_ =OperationMode::FOLLOW_MODE;
            auto msg = std_msgs::msg::UInt8();
            msg.data = static_cast<uint8_t>(current_mode_.load());
            mode_publisher_->publish(msg);
            RCLCPP_INFO(this->get_logger(),"Current mode is FOLLOW_MODE");
        }
        bStartServer_ = true;
        pull_up_down_processs(10);
    }
    else if ("startUwbSummon" == strAction){
        RCLCPP_INFO(this->get_logger(), "get robot msg : %s", msg->data.c_str());
        {
            current_mode_ =OperationMode::FOLLOW_MODE;
            auto msg = std_msgs::msg::UInt8();
            msg.data = static_cast<uint8_t>(current_mode_.load());
            mode_publisher_->publish(msg);
            RCLCPP_INFO(this->get_logger(),"Current mode is FOLLOW_MODE");
        }
        bStartServer_ = true;
        pull_up_down_processs(9);
        RCLCPP_INFO(this->get_logger(), "Start a Fixed-Point Summoning mission");
    }
    else if ("endFollow" == strAction){
        RCLCPP_INFO(this->get_logger(), "get robot msg : %s", msg->data.c_str());
        stopFollow();
        RCLCPP_INFO(this->get_logger(), "endFollow, tracker stop");
    }
    else
        RCLCPP_INFO(this->get_logger(), "Unknow action from websocketNvidia");

}
void FollowNode::stopFollow() {
    RCLCPP_INFO(this->get_logger(), "stop follow");
    auto twist_msg = std::make_unique<geometry_msgs::msg::Twist>();
    twist_msg->linear.x = 0.0;  
    twist_msg->linear.y = 0.0;
    twist_msg->linear.z = 0.0;
    twist_msg->angular.z = 0.0;
    twist_msg->angular.x = 0.0;
    twist_msg->angular.y = 0.0;
    velPub_->publish(std::move(twist_msg));
    pull_up_down_processs(11);
    bStartServer_ = false;
    bContinue_=false;
    alreadyStarted_=0;
}
void FollowNode::tripsigHandle(const std_msgs::msg::String::SharedPtr msg){
    std::string strSendMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    if(!reader.parse(strSendMsg, value)){
        RCLCPP_ERROR(rclcpp::get_logger("trip navi control"), "reader.parse error in tripsigHandle");
        return; 
    }
    if (!value.isMember("mode")||value["mode"].asUInt()!=1)
        return;    
    if (value.isMember("action")&&value["action"].asUInt()==1)
    {
        {
            current_mode_ =OperationMode::TRIP_MODE;
            auto msg = std_msgs::msg::UInt8();
            msg.data = static_cast<uint8_t>(current_mode_.load());
            mode_publisher_->publish(msg);
            RCLCPP_INFO(this->get_logger(),"Current mode is TRIP_MODE");
        }
        RCLCPP_INFO(rclcpp::get_logger("trip navi control"), "get robot msg : %s", msg->data.c_str());
        bStartServer_ = true;
    }else if(value.isMember("action")&&value["action"].asUInt()==12){
        RCLCPP_INFO(rclcpp::get_logger("trip navi control"), "get robot msg : %s", msg->data.c_str());
        bStartServer_ = false;
        RCLCPP_INFO(rclcpp::get_logger("trip navi control"), "END TRIP");
    }
}
void FollowNode::navCallback(const std_msgs::msg::String::SharedPtr msg)
{
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value["status"].asInt()==2){
        RCLCPP_INFO(rclcpp::get_logger("navCallback"), "Obtain information from the navigation interface: the end point has been reached: %s", msg->data.c_str());
        bStartServer_=false;
    }
}
void FollowNode::sendWarnigtoPlat(Json::Value warningBody){
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_ALARM";
    response["event"] = "device_alarm_report";
    response["eventId"] = "robdog_plat_" + std::to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
    response["seq"] = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
    Json::Value body;
    Json::Reader reader;
    response["body"] = warningBody;
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    sendDataToPlatform(jsonString);
}
void FollowNode::nofifyAPPFollowStatus(int warningCode){
    RobotState::getInstance().setFollowMeStatus("off");
    RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"report follow me status : %d", warningCode);
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "BUSINESS_REPORT";
    response["event"] = "data_report";
    response["eventId"] = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
    response["seq"] = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
    response["body"]["type"] = "followMe";
    response["body"]["data"]["status"]="off";
    const auto& error_map = FollowErrors::get_error_map();
    if (auto it = error_map.find(warningCode); it != error_map.end()) 
        response["body"]["data"]["code"] = it->second;
    response["body"]["data"]["isFirstTime"]=(alreadyStarted_==1)?false:true;
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"Follow me  warning msg is : %s", jsonString.c_str());
    sendDataToPlatform(jsonString);
}