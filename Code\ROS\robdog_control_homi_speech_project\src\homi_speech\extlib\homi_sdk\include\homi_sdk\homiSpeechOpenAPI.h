/*************************************************************
 *
 * This is a part of the homiSpeech SDK.
 * Copyright:2023 homiSpeech
 * File name:homiSpeechOpenAPI.h
 * Version:1.0.1
 * All rights reserved.
 *
 *************************************************************/

#ifndef HOMISPEECHOPENAPI_H
#define HOMISPEECHOPENAPI_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include "homiSpeechdefine.h"

typedef struct
{
 
    /**
    *【回调】
    *
    * @description：收到语音唤醒上报事件后，调用此接口打开录音
    *
    * @param[in] andioFormat    交互音频数据的格式信息，详细见homiAudioINDataFormat
    *
    * @return
    * - = 0:  成功
    * - < 0:  失败
    */
    Homi_int32(*Homi_AudioRecordStart)(HomiAudioINDataFormat audioFormat);


    /**
    *【回调】AIUI下发的停止录音
    *
    * @description：停止录音
    *
    * @param[in] 空
    *
    * @return
    * - = 0:  成功
    * - < 0:  失败
    */
    Homi_int32(*Homi_AudioRecordStop)(void);

   
     /**
     * 【回调】AIUI下发的音频数据播放
	 * @description：Homi接受AIUI下发的音频数据包，并在机器人上播放
     * @param [in] timestamp  时间戳(ms)
     * @param [in] flags  标志位，保留字段，默认0
     * @param [in] buff  音频数据
     * @param [in] len   音频数据长度
     * @return 
     * - = 0:  成功
     * - < 0:  失败
     */
    Homi_int32 (*Homi_AudioPlayData)(Homi_int64 timestamp, Homi_int32 flags, Homi_uint8 *buff, Homi_int32 len);

    /**
     * 【回调】拨打电话的号码
	 * @description：Homi接受AIUI下发的电话号码，返回给用户
     * @param [in] phonenumber  电话号码
     * @param [in] dataLen  数据长度
     * @return 
     * - = 0:  成功
     * - < 0:  失败
     */
    Homi_int32 (*Homi_PhoneCall)(Homi_char *phonenumber, Homi_int32 dataLen);

    /**
     * 【回调】Homi接受AIUI下发的url
	 * @description：Homi接受AIUI下发的url，返回给用户
     * @param [in] url  url链接
     * @param [in] dataLen  数据长度
     * @return 
     * - = 0:  成功
     * - < 0:  失败
     */
    Homi_int32 (*Homi_Url)(Homi_char *url, Homi_int32 dataLen);

    /**
     * 【回调】Homi接受AIUI下发的音乐播放歌单
	 * @description：Homi接受AIUI下发的歌单，返回给用户
     * @param [in] subEvent     音乐事件： 
     * media.play               开始播放歌曲 
     * media.pause              暂停播放歌曲 
     * media.resume             恢复播放歌曲 
     * media.stop               停止播放歌曲
     * @param [in] playListUrl  歌单的url
     * @param [in] infoJson     当前歌曲信息
     * @param [in] nextJson     下一首歌曲信息
     * @return 
     * - = 0:  成功
     * - < 0:  失败
     */
    Homi_int32 (*Homi_AudioMeidaPlay)(Homi_char *subEvent,Homi_char *playListUrl,Homi_char *infoJson,Homi_char *nextJson);
    
    /**
     * 【回调】Homi接受AIUI下发的文字
	 * @description：Homi接受AIUI下发的文字，返回给用户
     * @param [in] text  文本
     * @param [in] len  数据长度
     * @return 
     * - = 0:  成功
     * - < 0:  失败
     */
    Homi_int32 (*Homi_TextOutput)(Homi_uint8 *text, Homi_int32 len);

    /**
     * 【回调】Homi接受一次会话结束的标志
	 * @description：Homi接受AIUI下发一次会话结束，返回给用户
     * @return 
     * - = 0:  成功
     * - < 0:  失败
     */
    Homi_int32 (*Homi_End)(void);

    /**
     * 【回调】Homi接受演示版本命令
	 * @description：Homi接受AIUI下发演示指令，返回给用户
     * @return 
     * - = 0:  成功
     * - < 0:  失败
     */
    Homi_int32 (*Homi_Msg)(Homi_char *msg, Homi_int32 dataLen);
	
} homiDevCallBackFunList;

/**
 * Homi状态通知接口
 *
 * @description：当Homi状态发生变化时，通过该接口上报状态给SDK。
 *
 * @param[in] state, 详见HomiSPEECH_STATE
 *
 * @return
 * - = 0:  成功
 * - > 0:  失败
 * - = 3200,  TOKEN为空
 * - = 3201,  媒体通道连接失败
 * - = 3202,  媒体通道关闭失败
 * - = 3203,  暂无该状态选项
 */
extern Homi_int32 homiSetSpeechState(HomiSPEECH_STATE state);

/**
 * 音频内容推送接口
 *
 * @description：设备SDK推动音频内容
 *
 * @param[in] data   发送数据的首字节指针
 * @param[in] dataLen  本次发送数据的长度
 * @param[in] timestamp     该帧时间戳(ms)，可以传入绝对时间戳，也可以传入相对时间戳
 * @param[in] flags  标志位，保留字段，默认0
 *
 * @return
 * - = 0:  成功
 * - = 3300:  失败
 */
extern Homi_int32 homiSpeechPushData(Homi_char *data, Homi_int32 dataLen, Homi_uint64 timestamp, Homi_int32 flags);

/**
 * SDK初始化
 *
 * @param[in] callBackFunList   响应SDK的回调函数
 * @param[in] jsonParam         json格式化后的字符串，提供动态的参数配置。
 * json格式信息参考如下：
 * {
    "url":"http://**************:10000/robot/business/api/device/client/connect/url",
    "config":{
        "sn":"7378392000000008",
        "deviceId":"7378392000000008",
        "macId":"20:FF:00:00:00:01",	
        "deviceType":"XF80",	
        "firmwareVersion":"1.0.1"},
    "authorization":"00000000"
    };
 *  "url":<云端地址，必填>   
 *  "sn":<ID号，必填>
 *  "deviceId":<ID号，必填>
 *  "macId":<Mac地址，必填>
 *  "deviceType":<设备型号，必填>
 *  "firmwareVersion":<固件版本号，必填>
 *  "authorization":<授权信息，必填> 
 *
 * @return
 * Homi_RET_CMCCINIT_ERROR  = 3100,  //CMCC初始化失败
 * Homi_RET_CALLBACK_ERROR = 3101   //注册回调函数失败
 * Homi_RET_BADPARAMETER   = 3102   //入参校验失败
 * Homi_RET_CMD_SEND_ERROR = 3103   //信令通道发送失败
 * Homi_RET_SUCCESS        =  0     //初始化成功
 */
extern Homi_int32 homiDevInit(homiDevCallBackFunList *callBackFunList, Homi_char *jsonParam);

/**
 * 功能：设备获取SDK版本号
 * 获取SDK版本号信息
 *
 * @param [out] verison 设备版本号
 * @param [in] version_len 调用者预分配的字符串长度
 * @return
 * - = 0:  成功
 * - = 3400:  失败
 */
extern Homi_int32 homiGetSDKVersion(Homi_char *version, Homi_int32 version_len);

#ifdef __cplusplus
}
#endif

#endif //HomiSPEECH_OPENAPI_H
