import launch
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration
from launch_ros.substitutions import FindPackageShare
from launch_ros.actions import Node
from launch.actions import RegisterEventHandler, OpaqueFunction
from launch.event_handlers import OnProcessExit
import os
from ament_index_python.packages import get_package_share_directory
from launch.launch_description_sources import PythonLaunchDescriptionSource
import yaml
from configparser import ConfigParser
# 读取并处理BOM
def read_ini_file(file_path):
    with open(file_path, 'r', encoding='utf-8-sig') as file:
        content = file.read()
    return content

# 写入INI文件
def write_ini_file(file_path, config):
    with open(file_path, 'w', encoding='utf-8') as configfile:
        config.write(configfile)

conf = ConfigParser()
conf.read('/etc/cmcc_robot/cmcc_dev.ini')
def create_audio_recorder_node(context):
    audio_recorder_node = Node(
        package="audio_recorder",
        namespace="audio_recorder",
        name="audio_recorder_restart_node",
        executable="audio_recorder_node"
    )

    audio_recorder_restart = RegisterEventHandler(
        event_handler=OnProcessExit(
            target_action=audio_recorder_node,
            on_exit=[
                LogInfo(msg='audio_recorder_restart_node restart'),
                OpaqueFunction(function=create_audio_recorder_node)
            ]
        )
    )

    return [audio_recorder_node, audio_recorder_restart]

def generate_launch_description():
    config_share_dir = get_package_share_directory('launch_package')

    expression = Node(
        package="expression",
        executable="expression_node",
        #name='expression_node',  
        output='log',  
        respawn=True
    )

    robdog_control = Node(
        package="robdog_control",
        executable="robdog_control_node",
        output='log',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
    )

    video_gst_config = os.path.join(
        get_package_share_directory("video_gst"),
        "config",
        "param.yaml"
    )
    script_path = os.path.join(
        get_package_share_directory("video_gst"),
        "script",
        "find_camera.sh"
    )

    video_gst_node = Node(
        package="video_gst",
        namespace="video_gst",
        name="video_gst_node",
        executable="video_gst_node",
        parameters=[
            video_gst_config,
            {"script_path": script_path}
        ]
    )

    audio_recorder_node = Node(
        package="audio_recorder",
        namespace="audio_recorder",
        name="audio_recorder_node",
        executable="audio_recorder_node"
    )

    audio_recorder_restart = RegisterEventHandler(
        event_handler=OnProcessExit(
            target_action=audio_recorder_node,
            on_exit=[
                LogInfo(msg='audio_recorder_node restart'),
                OpaqueFunction(function=create_audio_recorder_node)
            ]
        )
    )

    player_config = os.path.join(
        get_package_share_directory("audio_player"),
        "config",
        "param.yaml"
    )

    player_script = os.path.join(
        get_package_share_directory("audio_player"),
        "script",
        "get_playback_card.sh"
    )

    player_res = os.path.join(
        get_package_share_directory("audio_player"),
        "resource"
    )

    audio_player_node = Node(
        package="audio_player",
        namespace="audio_player",
        name="audio_player_node",
        executable="audio_player_node",
        parameters=[
            player_config,
            {"alsa_playout_sh_name": player_script,
             "res_path": player_res}
        ]
    )

    ovd_ini_path = os.path.join(get_package_share_directory("live_stream"), "launch","DeviceConf.ini")
    ovd_ini_content = read_ini_file(ovd_ini_path)

    ovd_conf = ConfigParser(delimiters=':', strict=False, allow_no_value=True)
    ovd_conf.optionxform = str
    ovd_conf.read_string(ovd_ini_content)

    ovd_conf['deviceConfiguration']['devId'] = conf.get('factory', 'devSn')
    ovd_conf['deviceConfiguration']['devCmei'] = conf.get('factory', 'devCmei')
    ovd_conf['deviceConfiguration']['macAddr'] = conf.get('factory', 'macAddr')

    ovd_conf['deviceClientInfo']['OVDLoginPassword'] = conf.get('factory', 'OVDLoginPassword')
    ovd_conf['deviceClientInfo']['OVDMediaEncPassword'] = conf.get('factory', 'OVDMediaEncPassword')

    ovd_data_path = os.path.join(os.path.expanduser("~"), ".ovd")
    os.makedirs(ovd_data_path, exist_ok=True)

    ovd_data_test_path = os.path.join(os.path.expanduser("~"), ".ovd/test")
    os.makedirs(ovd_data_path, exist_ok=True)

    if(ovd_conf.get('deviceClientInfo', 'servicescheduleurl') == "https://video.komect.com/ovp/getServerAddr"):
        ovd_conf['deviceConfigInfo']['ovd_data_path'] = ovd_data_path
        ovd_conf['deviceConfigInfo']['SDK_partition_mounting_path'] = ovd_data_path
        ovd_conf['deviceConfigInfo']['ovd_log_path'] = ovd_data_path
    else:
        ovd_conf['deviceConfigInfo']['ovd_data_path'] = ovd_data_test_path
        ovd_conf['deviceConfigInfo']['SDK_partition_mounting_path'] = ovd_data_test_path
        ovd_conf['deviceConfigInfo']['ovd_log_path'] = ovd_data_test_path  

    ovd_conf['deviceConfigInfo']['ovd_ai_path'] = ovd_data_path

    write_ini_file(ovd_ini_path, ovd_conf)    
    live_stream_node = Node(
        package="live_stream",
        namespace="live_stream",
        name="live_stream_node",
        executable="live_stream_node"
    )
    homi_speech_yaml = IncludeLaunchDescription(        # 包含指定路径下的另外一个launch文件
    PythonLaunchDescriptionSource([os.path.join(
        get_package_share_directory('homi_speech'), 'launch'),
        '/speech_helper_near.launch.py'])
    )

    homi_audio_player_yaml = IncludeLaunchDescription(        # 包含指定路径下的另外一个launch文件
    PythonLaunchDescriptionSource([os.path.join(
        get_package_share_directory('homi_audio_player'), 'launch'),
        '/homi_player.launch.py'])
    )

    return LaunchDescription([
            expression,
            robdog_control,
            video_gst_node,
            audio_recorder_node,
            audio_recorder_restart,
            audio_player_node,
            homi_speech_yaml,
            live_stream_node,
            homi_audio_player_yaml
    ])