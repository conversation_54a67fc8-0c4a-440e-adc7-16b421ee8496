#ifndef __VS_DEFINE_H__
#define __VS_DEFINE_H__

// voice suite(VS) sdk header file

#define SERVER_IP "127.0.0.1"
#define SERVER_PORT (9090)

//错误码
typedef enum {
	ERR_OK,				//成功
	ERR_CB_NULL,
	ERR_INIT_ALREADY,
	ERR_INVALID_PARAM,
}VS_ERR_e;

//音频降噪模式
typedef enum {
	NR_MODE_NONE = 0,	//不使用
	NR_MODE_INTERACT,	//交互降噪
	NR_MODE_CALL,		//通话降噪
	NR_MODE_MAX,		//不使用
}VS_NR_Mode_e;

//VAD状态（仅交互音频使用）
typedef enum {
	VAD_STATUS_MUTE = 0,//静音片段
	VAD_STATUS_START,	//开始说话
	VAD_STATUS_KEEP,	//持续说话
	VAD_STATUS_END,		//结束说话
}VS_VAD_Status_e;

//唤醒事件数据结构体
typedef struct {
	char ivwWord[64];	//唤醒词
	int angle;			//声源定位角度
	char *extraInfo;	//扩展信息
}Event_wakeup_word_st;

////命令词事件数据结构体
//typedef struct {
//	char keyWord[64];	//关键词
//	char *extraInfo;	//扩展信息
//}Event_key_word_st;

////降噪模式切换事件数据结构体
//typedef struct {
//	int mode_last; //之前降噪模式
//	int mode_cur; //当前降噪模式
//}Event_mode_st;
//不提供，只提供切换成功与否结果，当前模式由自己记录

//结果通知事件数据结构体
typedef struct {
	int ack; //结果通知（0 成功，-1 失败）
}Event_ret_st;

//事件类型
typedef enum {
	EVENT_TYPE_NONE,			//不使用
	EVENT_WAKEUP_WORD,			//唤醒词事件
	EVENT_KEY_WORD,				//命令词事件
	EVENT_NR_MODE_SET_RET,		//降噪模式设置结果
	EVENT_WAKEUP_WORD_UPDATE_RET,	//唤醒词更新结果
	EVENT_OTA_RET,				//ota升级结果
}IFLY_EVENT_e;

typedef enum
{
	ESR_AUDIO_BEGIN = 0,
	ESR_AUDIO_CONTINUE,
	ESR_AUDIO_END,
} ESR_AUDIO_STATUS;

typedef enum
{
	ESR_RESULT_NONE = 0,
	ESR_RESULT_CONTINUE,
	ESR_RESULT_FINISH,
}ESR_RESULT_STATUS;

typedef enum
{
	ESR_LANGUAGE_CHINESE = 0,
	ESR_LANGUAGE_ENGLISH,
	ESR_LANGUAGE_RUSSIAN,
	ESR_LANGUAGE_JAPANESE,
	ESR_LANGUAGE_KOREAN,
	ESR_LANGUAGE_UIGHUR,
	ESR_LANGUAGE_SPANISH,
	ESR_LANGUAGE_FRENCH,
	ESR_LANGUAGE_Arabic,
	ESR_LANGUAGE_MAYLAY,
	ESR_LANGUAGE_INDONESIAN,
	ESR_LANGUAGE_THAI,
	ESR_LANGUAGE_HINDI,
	ESR_LANGUAGE_GERMAN,
	ESR_LANGUAGE_COUNT
}ESR_LANGUAGE_TYPE;

typedef struct EsrResultElem_
{
	const char*	key;
	const char*   sub_key;
	const char*   value;
	int32_t		    seg_id;
	void*			reserved;
}EsrResultElem;

typedef struct EsrResult_
{
	ESR_RESULT_STATUS	status;
	EsrResultElem*		elems;
	int32_t				elem_count;
	void*				reserved;
}EsrResult;
#endif
