/*
 * Copyright(c) 2006 to 2020 ZettaScale Technology and others
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0, or the Eclipse Distribution License
 * v. 1.0 which is available at
 * http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * SPDX-License-Identifier: EPL-2.0 OR BSD-3-Clause
 */


/**
 * @file
 */

#ifndef CYCLONEDDS_DOMAIN_DOMAIN_HPP_
#define CYCLONEDDS_DOMAIN_DOMAIN_HPP_

#include "dds/core/types.hpp"

namespace org
{
namespace eclipse
{
namespace cyclonedds
{
namespace domain
{
OMG_DDS_API uint32_t any_id();

OMG_DDS_API uint32_t default_id();
}
}
}
}

#endif /* CYCLONEDDS_DOMAIN_DOMAIN_HPP_ */
