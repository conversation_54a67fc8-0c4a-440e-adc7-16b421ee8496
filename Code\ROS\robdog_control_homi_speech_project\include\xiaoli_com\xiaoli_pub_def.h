/*
  *Copyright(C),GYJ
  *FileName:  xiaoli_pub_def.h
*/
#pragma once
#include <map>
#include <string>
#include <unordered_map>
#include <vector> 
enum HomiRobotStatus {
  ROBDOG_STATUS_STATE                            = 0X00000000,  //机器狗的状态开始标志位
  ROBDOG_STATUS_GETDOWN,                                        //趴下状态
  ROBDOG_STATUS_FORWARD_JUMPPING,                               //正在执行向前跳
  ROBDOG_STATUS_READYTOSTAND,                                   //准备起立状态
  ROBDOG_STATUS_STANDING,                                       //正在起立状态
  ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT,                //力控状态（静止站立）且步态为平地低速步态
  ROBDOG_STATUS_BODYROTWISTING_BY_AXISCOMMAND,                  //正在以平地低速步态踏步或正在根据轴指令扭动身体
  ROBDOG_STATUS_BODYROTWISTING,                                 //正在执行扭身体
  ROBDOG_STATUS_BODYJUMPPING,                                   //正在执行扭身跳
  ROBDOG_STATUS_FORCE_CTRL_GEN_OBS_GAIT,                        //力控状态（静止站立）且步态为通用越障步态
  ROBDOG_STATUS_GEN_OBS_GAIT_STEPPING,                          //正在以通用越障步态踏步
  ROBDOG_STATUS_FORCE_CTRL_LEVEL_MIDSPEED_GAIT,                 //力控状态（静止站立）且步态为平地中速步态
  ROBDOG_STATUS_LEVEL_MIDSPEED_GAIT_STEPPING,                   //正在以平地中速步态踏步
  ROBDOG_STATUS_FORCE_CTRL_LEVEL_HIGHSPEED_GAIT,                //力控状态（静止站立）且步态为平地高速步态
  ROBDOG_STATUS_LEVEL_HIGHSPEED_GAIT_STEPPING,                  //正在以平地高速步态踏步
  ROBDOG_STATUS_FORCE_CTRL_GROUND_GRIPPING_GAIT,                //力控状态（静止站立）且步态为抓地越障步态
  ROBDOG_STATUS_GROUND_GRIPPING_GAIT_STEPPING,                  //正在以抓地越障步态踏步
  ROBDOG_STATUS_MOONWALKING,                                    //正在执行太空步
  ROBDOG_STATUS_FORCE_CTRL_HIGHSTEP_OBS_GAIT,                   //力控状态（静止站立）且步态为高踏步越障步态
  ROBDOG_STATUS_HIGHSTEP_OBS_GAIT_STEPPING,                     //正在以高踏步越障步态踏步
  ROBDOG_STATUS_PRONEING,                                       //正在趴下状态
  ROBDOG_STATUS_LOSS_CTRL,                                      //失控保护状态
  ROBDOG_STATUS_ATTITUDE_ADJUST_MODE,                           //姿态调整状态
  ROBDOG_STATUS_ROLL_OVERING,                                   //正在执行翻身
  ROBDOG_STATUS_RESET,                                          //回零状态
  ROBDOG_STATUS_BACKFLIP,                                       //正在执行后空翻
  ROBDOG_STATUS_HELLO,                                          //正在执行打招呼
  ROBDOG_STATUS_AI,                                             //AI模式
  ROBDOG_STATUS_SITDOWNING,                                     //正在执行坐下
  ROBDOG_STATUS_SITDOWN                                         //坐下状态
};

enum HomiUtRobotStatus {
  UT_ROBDOG_STATUS_STATE                            = 0X00000000,  //机器狗的状态开始标志位
  UT_ROBDOG_STATUS_FREE_WALK,
  UT_ROBDOG_STATUS_DAMPING,
  UT_ROBDOG_STATUS_LOCK_STAND,
  UT_ROBDOG_STATUS_SQUAT,
  UT_ROBDOG_STATUS_LOCOMOTION,
  UT_ROBDOG_STATUS_SIT,
  UT_ROBDOG_STATUS_FRONT_JUMP,
  UT_ROBDOG_STATUS_FRONT_POUNCE,
  UT_ROBDOG_STATUS_BALANCE_STAND,
  UT_ROBDOG_STATUS_STATIC_WALK,
  UT_ROBDOG_STATUS_TROT_RUN,
  UT_ROBDOG_STATUS_ECONOMIC_GAIT,
  UT_ROBDOG_STATUS_POSE,
  UT_ROBDOG_STATUS_RECOVERYSTANDING,
  UT_ROBDOG_STATUS_FREE_AVOID,
  UT_ROBDOG_STATUS_FREE_BOUND,
  UT_ROBDOG_STATUS_FREE_JUMP,
  UT_ROBDOG_STATUS_CLASSIC,
  UT_ROBDOG_STATUS_HAND_STAND,
  UT_ROBDOG_STATUS_FRONT_FLIP,
  UT_ROBDOG_STATUS_BACK_FLIP,
  UT_ROBDOG_STATUS_LEFT_FLIP,
  UT_ROBDOG_STATUS_CROSS_STEP,
  UT_ROBDOG_STATUS_WALK_UPRIGHT,
  UT_ROBDOG_STATUS_PULL
};

#define WEBSOCKET_CON_TIMEOUT     30                            // webSocket 连接超时时间 30s
#define WEBSOCKET_SERVER_PORT     19003                            // webSocket 服务器端口号
// #define WEBSOCKET_SERVER_URL    "ws://*************:19002"                            // webSocket
#define WEBSOCKET_SERVER_URL    "ws://127.0.0.1:19003"                            // webSocket
#define CLIENT_LAUNCHER           "launcher"                    //讯飞主控的连接
#define CLIENT_CTRL               "ctrl"                        //temi的主控的连接 
#define CLIENT_DEEPROBOT          "deeprobot"                   //云深处主控的连接
#define CLIENT_NVIDIA             "nvidia"                      //nvidia连接
#define CLIENT_ANDROID             "android"                      //android连接


enum NAVIGATIONStatus {
    NAVIGATION_STATUS_RELOCATION_FINISHED = 0,  // 重定位成功
    NAVIGATION_STATUS_MOVE_TASK_FINISHED = 1,  // 移动任务完成
    NAVIGATION_STATUS_POINT_UNREACHABLE = 2,  // 目标点不可达
    NAVIGATION_STATUS_MOVEMENT_CANCEL_SUCCESS = 3,  // 移动任务取消成功
    NAVIGATION_STATUS_CANNOTGO = 4, // 无法出发
    NAVIGATION_STATUS_SINGLE_POINT_NAVIGATION_COMPLETED = 5, // 单点导航完成
    NAVIGATION_STATUS_UNREACHABLE_DURING_MOVEMENT = 6,   //移动过程中点位不可达
    NAVIGATION_STATUS_OUT_OF_RANGE = 7 //非上述状态值
    
    // NAVIGATION_STATUS_NAVIGATION_NODE_STARTED = 4,  // 导航节点已开启
    // NAVIGATION_STATUS_NAVIGATION_NODE_STOPPED = 5,  // 导航节点已关闭
    // NAVIGATION_STATUS_NAVIGATION_TASK_PAUSED = 6,  // 导航任务已暂停
    // NAVIGATION_STATUS_NAVIGATION_TASK_CANCELED = 7,  // 导航任务已取消
    // NAVIGATION_STATUS_RELOCALIZATION_FINISHED = 8,  // 重定位任务已完成
    // NAVIGATION_STATUS_NAVIGATION_TASK_COMPLETED = 9,  // 整体导航任务已完成
    // NAVIGATION_STATUS_DRIFT_DETECTED = 10,  // 检测到漂移，正在自动重定位
    // NAVIGATION_STATUS_INVALID_COMMAND_FORMAT = 11,  // 下发指令的消息格式错误
    // NAVIGATION_STATUS_MISSING_OR_INVALID_TASK_PARAM = 12,  // 下发指令未提供“任务”参数，或“任务”参数不合法
    // NAVIGATION_STATUS_INVALID_GAIT_TYPE = 13,  // 下发的步态类型不合法
    // NAVIGATION_STATUS_TARGET_UNREACHABLE = 14,  // 下发的目标点位不可达或点位不合法
    // NAVIGATION_STATUS_MAP_ID_NOT_FOUND = 15,  // 导航任务启动失败，地图id不存在
    // NAVIGATION_STATUS_INVALID_COMMAND_OUTSIDE_NAVIGATION = 16,  // 不处于导航流程时，收到导航结束/目标点设置/暂停/取消/重定位指令
    // NAVIGATION_STATUS_MAP_ID_MISMATCH = 17,  // 导航结束/目标点设置/暂停/取消/重定位指令的地图id与当前地图id不匹配
    // NAVIGATION_STATUS_COMMAND_FAILED = 18,  // 目标点设置/暂停/取消/重定位任务失败
    // NAVIGATION_STATUS_NEW_NAVIGATION_COMMAND_DURING_NAVIGATION = 19,  // 处于导航流程时，收到新的导航开始指令
    // NAVIGATION_STATUS_NEW_NAVIGATION_COMMAND_DURING_MAPPING = 20,  // 处于建图流程时，收到导航开始指令
    // NAVIGATION_STATUS_RELOCALIZATION_ERROR_LARGE = 21,  // 重定位误差过大，请手动进行定位或检查机器狗所处地图是否正确
    // NAVIGATION_STATUS_NODE_CRASHED = 22  // 节点崩溃，请重启导航流程
};

const int base_map = 10000;
const int base_navigation = 20000;
const int base_charging = 50000;

enum rtNavigationCode {
  // LOCALIZATION code

  // NAVIGATION code
  NAVIGATION_INIT = 2000,                      // 初始化                    "navigation/state_transition/INITIALIZE"
  NAVIGATION_EXIT,                             // 退出                      "navigation/state_transition/EXIT"
  NAVIGATION_FAILURE,                          // 失败                      "navigation/state_transition/FAILURE"
  NAVIGATION_ABNORMAL,                         // 异常                      "navigation/state_transition/ABNORMAL"
  NAVIGATION_WAITING,                          // 等待目标点                "navigation/state_transition/WAITING"
  NAVIGATION_REACHED,                          // 到达目标点                "navigation/state_transition/REACHED"
  NAVIGATION_NO_PATH,                          // 没有规划出路径            "navigation/state_transition/NO_PATH"
  NAVIGATION_GOAL_OCCUPIED,                    // 目标点被占据              "navigation/state_transition/GOAL_OCCUPIED"
  NAVIGATION_GOAL_CHANGED,                     // 目标点被修改              "navigation/state_transition/GOAL_CHANGED"
  NAVIGATION_GOAL_CANCELLED,                   // 目标点被取消              "navigation/state_transition/GOAL_CANCELLED"
  NAVIGATION_TIMEOUT_ODOMETRY,                 // 里程计超时                "navigation/state_transition/TIMEOUT_ODOMETRY"
  NAVIGATION_TIMEOUT_POINTCLOUD,               // 点云超时                  "navigation/state_transition/TIMEOUT_POINTCLOUD"
                                                                                            
  // AUTOCHARGE code                                                                          
  AUTOCHARGE_INIT = 3000,                      // 初始化                    "autocharge/state_transition/INITIALIZE"
  AUTOCHARGE_EXIT,                             // 退出                      "autocharge/state_transition/EXIT"
  AUTOCHARGE_SUCCESS,                          // 充电成功                  "autocharge/state_transition/SUCCESS"
  AUTOCHARGE_FAILURE,                          // 充电失败                  "autocharge/state_transition/FAILURE"
  AUTOCHARGE_GO_TO_CHARGE_BOARD,               // 前往充电板                "autocharge/state_transition/GO_TO_CHARGE_BOARD"
  AUTOCHARGE_REACHED_CHARGE_BOARD,             // 到达充电板                "autocharge/state_transition/REACHED_CHARGE_BOARD"
  AUTOCHARGE_SIT_DOWN,                         // 卧倒                      "autocharge/state_transition/SIT_DOWN"
  AUTOCHARGE_CHECK_POWER_CONNECT,              // 检查充电连接              "autocharge/state_transition/CHECK_POWER_CONNECT"
  AUTOCHARGE_TIMEOUT_RUNNING,                  // 运行超时                  "autocharge/state_transition/TIMEOUT_RUNNING"
  AUTOCHARGE_TIMEOUT_ODOMETRY,                 // 里程计超时                "autocharge/state_transition/TIMEOUT_ODOMETRY"
  AUTOCHARGE_TIMEOUT_POINTCLOUD,               // 点云超时                  "autocharge/state_transition/TIMEOUT_POINTCLOUD"
  AUTOCHARGE_TIMEOUT_DETECT,                   // 检测超时                  "autocharge/state_transition/TIMEOUT_DETECT"
  AUTOCHARGE_TIMEOUT_CONNECT_POWER,            // 电源连接超时              "autocharge/state_transition/TIMEOUT_CONNECT_POWER"

  // PATROL code                            
  PATROL_INIT = 4000,                          // 初始化                     "patrol/state_transition/INITIALIZE"
  PATROL_EXIT,                                 // 退出                       "patrol/state_transition/EXIT"
  PATROL_FAILURE,                              // 失败                       "patrol/state_transition/FAILURE"
  PATROL_PAUSE,                                // 暂停                       "patrol/state_transition/PAUSE"
  PATROL_STAND_UP,                             // 站立                       "patrol/state_transition/STAND_UP"
  PATROL_SELECT_GOAL_POINT,                    // 选择目标点                 "patrol/state_transition/SELECT_GOAL_POINT"
  PATROL_NO_GOAL_POINT_TO_SELECT,              // 没有目标点选择             "patrol/state_transition/NO_GOAL_POINT_TO_SELECT"
  PATROL_NAVIGATE_TO_GOAL_POINT,               // 正在导航到目标点           "patrol/state_transition/NAVIGATE_TO_GOAL_POINT"
  PATROL_GOAL_POINT_REACHED,                   // 到达目标点                 "patrol/state_transition/GOAL_POINT_REACHED"
  PATROL_GOAL_POINT_UNREACHABLE,               // 目标点不可到达             "patrol/state_transition/GOAL_POINT_UNREACHABLE"
  PATROL_NEED_CHARGE,                          // 电量不足需要充电           "patrol/state_transition/NEED_CHARGE"
  PATROL_NAVIGATE_TO_CHARGE_BOARD,             // 导航到充电板               "patrol/state_transition/NAVIGATE_TO_CHARGE_BOARD"
  PATROL_NAVIGATE_TO_CHARGE_BOARD_FAILED,      // 导航到充电板失败           "patrol/state_transition/NAVIGATE_TO_CHARGE_BOARD_FAILED"
  PATROL_REACHED_CHARGE_BOARD,                 // 到达充电板                 "patrol/state_transition/REACHED_CHARGE_BOARD"
  PATROL_RUN_AUTO_CHARGE,                      // 运行自动充电               "patrol/state_transition/RUN_AUTO_CHARGE"
  PATROL_IS_CHARGING,                          // 正在充电                   "patrol/state_transition/IS_CHARGING"
  PATROL_CONNECT_POWER_FAILED,                 // 充电失败                   "patrol/state_transition/CONNECT_POWER_FAILED"
  PATROL_CHARGE_FINISHED,                      // 完成充电                   "patrol/state_transition/CHARGE_FINISHED"
  PATROL_REACH_PATROL_NUMBER_LIMIT,            // 到达巡逻次数上限           "patrol/state_transition/REACH_PATROL_NUMBER_LIMIT"
  PATROL_REACH_PATROL_TIME_LIMIT,              // 到达巡逻时间上限           "patrol/state_transition/REACH_PATROL_TIME_LIMIT"
  PATROL_REACH_TOTAL_TIME_LIMIT,               // 到达巡逻+充电的时间上限    "patrol/state_transition/REACH_TOTAL_TIME_LIMIT"
  PATROL_TIMEOUT_LOCALIZATION,                 // 定位超时                   "patrol/state_transition/TIMEOUT_LOCALIZATION"
  PATROL_TIMEOUT_NAVIGATION,                   // 导航超时                   "patrol/state_transition/TIMEOUT_NAVIGATION"
  PATROL_TIMEOUT_TRY_CHARGE,                   // 充电超时                   "patrol/state_transition/TIMEOUT_TRY_CHARGE"
};


// 宇树slam接口返回值字符串到中文描述的映射表
static inline std::unordered_map<std::string, std::string> buildStatusMapping() {
    std::unordered_map<std::string, std::string> statusMap;

    
    //  Mapping 成功状态
    statusMap["mapping/start"] = "启动建图";
    statusMap["mapping/start/success"] = "建图启动成功";
    statusMap["mapping/stop"] = "停止建图";
    statusMap["mapping/stop/success"] = "建图已停止并合成地图";
    statusMap["mapping/cancel"] = "取消建图";
    statusMap["mapping/cancel/success"] = "建图取消成功";
    statusMap["mapping/get_cloud_map"] = "获取点云地图";
    statusMap["mapping/get_cloud_map/success"] = "点云地图发布成功";
    statusMap["mapping/get_status/status/0"] = "建图模块未运行";
    statusMap["mapping/get_status/status/1"] = "建图模块正在运行";

    // Localization 成功状态
    statusMap["localization/start"] = "开启定位";
    statusMap["[Localization] initialization succeed!"] = "定位初始化成功";
    statusMap["localization/stop"] = "结束定位";
    statusMap["localization/stop/success"] = "定位已成功停止";
    statusMap["localization/set_initial_pose/success"] = "初始位姿设置成功";
    statusMap["localization/get_status/status/0"] = "定位模块未运行";
    statusMap["localization/get_status/status/1"] = "定位模块正在运行";

    // Navigation 失败状态
    statusMap["navigation/state_transition/INITIALIZE"] = "导航初始化";
    statusMap["navigation/state_transition/EXIT"] = "导航退出";
    statusMap["navigation/state_transition/FAILURE"] = "导航失败";
    statusMap["navigation/state_transition/ABNORMAL"] = "导航异常";
    statusMap["navigation/state_transition/WAITING"] = "等待目标点";
    statusMap["navigation/state_transition/REACHED"] = "到达目标点";
    statusMap["navigation/state_transition/NO_PATH"] = "没有规划出路径";
    statusMap["navigation/state_transition/GOAL_OCCUPIED"] = "目标点被占据";
    statusMap["navigation/state_transition/GOAL_CHANGED"] = "目标点被修改";
    statusMap["navigation/state_transition/GOAL_CANCELLED"] = "目标点被取消";
    statusMap["navigation/state_transition/TIMEOUT_ODOMETRY"] = "里程计超时";
    statusMap["navigation/state_transition/TIMEOUT_POINTCLOUD"] = "点云超时";
    // Navigation 成功状态
    statusMap["navigation/start/success"] = "导航启动成功";
    statusMap["navigation/stop/success"] = "导航停止成功";
    statusMap["navigation/set_goal_pose/success"] = "目标位姿设置成功";
    statusMap["navigation/set_goal_pose/failed"] = "目标位姿设置失败";
    statusMap["navigation/get_status/status/0"] = "导航未运行";
    statusMap["navigation/get_status/status/1"] = "导航正在运行";

    // Autocharge 失败状态
    statusMap["autocharge/state_transition/INITIALIZE"] = "自动充电初始化";
    statusMap["autocharge/state_transition/EXIT"] = "自动充电退出";
    statusMap["autocharge/state_transition/SUCCESS"] = "充电成功";
    statusMap["autocharge/state_transition/FAILURE"] = "充电失败";
    statusMap["autocharge/state_transition/GO_TO_CHARGE_BOARD"] = "前往充电板";
    statusMap["autocharge/state_transition/REACHED_CHARGE_BOARD"] = "到达充电板";
    statusMap["autocharge/state_transition/SIT_DOWN"] = "卧倒";
    statusMap["autocharge/state_transition/CHECK_POWER_CONNECT"] = "检查充电连接";
    statusMap["autocharge/state_transition/TIMEOUT_RUNNING"] = "运行超时";
    statusMap["autocharge/state_transition/TIMEOUT_ODOMETRY"] = "里程计超时";
    statusMap["autocharge/state_transition/TIMEOUT_POINTCLOUD"] = "点云超时";
    statusMap["autocharge/state_transition/TIMEOUT_DETECT"] = "检测超时";
    statusMap["autocharge/state_transition/TIMEOUT_CONNECT_POWER"] = "电源连接超时";
    // 新增 Autocharge 成功状态
    statusMap["autocharge/start/success"] = "自动充电启动成功";
    statusMap["autocharge/stop/success"] = "自动充电停止成功";

    // Patrol 失败状态
    statusMap["patrol/state_transition/INITIALIZE"] = "巡逻初始化";
    statusMap["patrol/state_transition/EXIT"] = "巡逻退出";
    statusMap["patrol/state_transition/FAILURE"] = "巡逻失败";
    statusMap["patrol/state_transition/PAUSE"] = "巡逻暂停";
    statusMap["patrol/state_transition/STAND_UP"] = "站立";
    statusMap["patrol/state_transition/SELECT_GOAL_POINT"] = "选择目标点";
    statusMap["patrol/state_transition/NO_GOAL_POINT_TO_SELECT"] = "没有目标点可选";
    statusMap["patrol/state_transition/NAVIGATE_TO_GOAL_POINT"] = "正在导航到目标点";
    statusMap["patrol/state_transition/GOAL_POINT_REACHED"] = "到达目标点";
    statusMap["patrol/state_transition/GOAL_POINT_UNREACHABLE"] = "目标点不可达";
    statusMap["patrol/state_transition/NEED_CHARGE"] = "电量不足需要充电";
    statusMap["patrol/state_transition/NAVIGATE_TO_CHARGE_BOARD"] = "导航到充电板";
    statusMap["patrol/state_transition/NAVIGATE_TO_CHARGE_BOARD_FAILED"] = "导航到充电板失败";
    statusMap["patrol/state_transition/REACHED_CHARGE_BOARD"] = "到达充电板";
    statusMap["patrol/state_transition/RUN_AUTO_CHARGE"] = "运行自动充电";
    statusMap["patrol/state_transition/IS_CHARGING"] = "正在充电";
    statusMap["patrol/state_transition/CONNECT_POWER_FAILED"] = "充电失败";
    statusMap["patrol/state_transition/CHARGE_FINISHED"] = "完成充电";
    statusMap["patrol/state_transition/REACH_PATROL_NUMBER_LIMIT"] = "达到巡逻次数上限";
    statusMap["patrol/state_transition/REACH_PATROL_TIME_LIMIT"] = "达到巡逻时间上限";
    statusMap["patrol/state_transition/REACH_TOTAL_TIME_LIMIT"] = "达到巡逻+充电时间上限";
    statusMap["patrol/state_transition/TIMEOUT_LOCALIZATION"] = "定位超时";
    statusMap["patrol/state_transition/TIMEOUT_NAVIGATION"] = "导航超时";
    statusMap["patrol/state_transition/TIMEOUT_TRY_CHARGE"] = "充电尝试超时";
    // 新增 Patrol 成功状态
    statusMap["patrol/start/success"] = "巡逻程序启动成功";
    statusMap["patrol/stop/success"] = "巡逻程序停止成功";
    statusMap["patrol/go/success"] = "巡逻开始移动";
    statusMap["patrol/pause/success"] = "巡逻停止移动";
    statusMap["patrol/add_patrol_point/success"] = "巡逻点添加成功";
    statusMap["patrol/clear_all_patrol_points/success"] = "所有巡逻点已清空";
    statusMap["patrol/get_patrol_points/success"] = "成功获取所有巡逻点";
    statusMap["patrol/set_patrol_time_limit/success"] = "单次巡逻时间限制设置成功";
    statusMap["patrol/set_total_time_limit/success"] = "总巡逻时间限制设置成功";
    statusMap["patrol/set_charge_time_limit/success"] = "充电时间限制设置成功";

    return statusMap;
}

enum NavigationCode {
  // 建图状态码
  MAP_CODE_STARTED = 1000 + base_map, // 建图节点已开启
  MAP_CODE_STOPPED = 1001 + base_map, // 建图节点已关闭
  MAP_CODE_CANCELLED = 1002 + base_map, // 当前建图流程已取消,建图节点关闭
  MAP_CODE_DELETED = 1003 + base_map, // 已删除指定地图
  ERROR_CODE_INVALID_COMMAND_FORMAT = 1100 + base_map, // 下发指令的消息格式错误
  ERROR_CODE_MISSING_TASK_PARAM = 1101 + base_map, // 下发指令未提供“任务”参数，或“任务”参数不合法
  ERROR_CODE_MAP_DELETE_FAILED_NO_ID = 1102 + base_map, // 无指定id的地图,删除操作失败
  ERROR_CODE_MAP_SAVE_FAILED = 1103 + base_map, // 地图保存过程出错
  ERROR_CODE_MAP_ID_MISMATCH = 1200 + base_map, // 建图结束指令的地图id与当前地图id不匹配
  ERROR_CODE_MAP_UPLOAD_FAILED = 1201 + base_map, // 建图过程中上传地图失败，URL不合法或感知主机无法上传
  ERROR_CODE_NEW_MAP_COMMAND_DURING_BUILD = 1202 + base_map, // 处于建图流程时，收到新的建图开始指令或删除地图指令
  ERROR_CODE_NAVIGATION_COMMAND_DURING_BUILD = 1203 + base_map, // 处于导航流程时，收到建图开始/结束/取消/删除地图指令
  ERROR_CODE_INVALID_BUILD_COMMAND = 1204 + base_map, // 不处于建图流程时，收到建图结束/取消指令
  ERROR_CODE_NODE_CRASHED = 1205 + base_map, // 节点崩溃，请重启建图流程
  ERROR_CODE_MAPINI_TIME_OUT = 1206 + base_map, // 地图初始化超时
  ERROR_CODE_MAP_COMMAND_DURING_FOLLOW = 1207 + base_map, // 处于跟随时，收到建图开始指令
  // 导航状态码
  NAVIGATION_CODE_NODE_STARTED = 2000 + base_navigation,  // 导航节点已开启
  NAVIGATION_CODE_NODE_STOPPED = 2001 + base_navigation,  // 导航节点已关闭
  NAVIGATION_CODE_SINGLE_POINT_NAVIGATION_COMPLETED = 2002 + base_navigation,  // 单点导航任务已完成,返回下发点位时的uid
  NAVIGATION_CODE_NAVIGATION_TASK_PAUSED = 2003 + base_navigation,  // 导航任务已暂停
  NAVIGATION_CODE_NAVIGATION_TASK_CANCELED = 2004 + base_navigation,  // 导航任务已取消
  NAVIGATION_CODE_RELOCALIZATION_FINISHED = 2005 + base_navigation,  // 重定位任务已完成
  NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED = 2006 + base_navigation,  // 整体导航任务已完成,返回任务的taskId
  NAVIGATION_CODE_DRIFT_DETECTED = 2100 + base_navigation,  // 检测到漂移，正在自动重定位
  NAVIGATION_CODE_INVALID_COMMAND_FORMAT = 2101 + base_navigation,  // 下发指令的消息格式错误
  NAVIGATION_CODE_MISSING_OR_INVALID_TASK_PARAM = 2102 + base_navigation,  // 下发指令未提供“任务”参数，或“任务”参数不合法
  NAVIGATION_CODE_INVALID_GAIT_TYPE = 2103 + base_navigation,  // 下发的步态类型不合法
  NAVIGATION_CODE_CANNOT_GO = 2104 + base_navigation, // 无法出发
  NAVIGATION_CODE_TARGET_UNREACHABLE = 2200 + base_navigation,  // 下发的目标点位不可达或点位不合法
  NAVIGATION_CODE_MAP_ID_NOT_FOUND = 2201 + base_navigation,  // 导航任务启动失败，地图id不存在
  NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION = 2202 + base_navigation,  // 不处于导航流程时，收到导航结束/目标点设置/暂停/取消/重定位指令
  NAVIGATION_CODE_MAP_ID_MISMATCH = 2203 + base_navigation,  // 导航结束/目标点设置/暂停/取消/重定位指令的地图id与当前地图id不匹配
  NAVIGATION_CODE_COMMAND_FAILED = 2204 + base_navigation,  // 目标点设置/暂停/取消/重定位任务失败
  NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_NAVIGATION = 2205 + base_navigation,  // 处于导航流程时，收到新的导航开始指令
  NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_MAPPING = 2206 + base_navigation,  // 处于建图流程时，收到导航开始指令
  NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE = 2207 + base_navigation,  // 重定位误差过大，请重新进行手动定位
  NAVIGATION_CODE_NODE_CRASHED = 2208 + base_navigation,  // 节点崩溃，请重启导航流程
  NAVIGATION_CODE_TASK_FAILED = 2209 + base_navigation, //导航任务失败，可能是路径规划或导航执行失败
  NAVIGATION_CODE_TIME_OUT = 2210 + base_navigation,
  NAVIGATION_CODE_NO_AUTOREPOSITION = 2211+ base_navigation,
  // 充电状态码
  CHARGING_CODE_POINT_ENTRY_STARTED = 5000 + base_charging, // 点位录入功能启动成功
  CHARGING_CODE_QRCODE_DETECTED = 5001 + base_charging, // 视野内出现二维码,开始对准电极片
  CHARGING_CODE_POINT_ENTRY_COMPLETED = 5002 + base_charging, // 电极片已对准,点位录入功能完成,并返回回充点位导航坐标与对应地图id
  CHARGING_CODE_CHARGING_POINT_COORDINATES = 5003 + base_charging, // {"x":0.0, "y":0.0, "angle":0.0}, "mapId": "map1"} | 回充点位导航目标点点坐标与对应地图id
  CHARGING_CODE_CHARGING_STARTED = 5010 + base_charging, // 回充功能启动成功
  CHARGING_CODE_CHARGING_COMPLETED = 5011 + base_charging, // 回充功能完成,可以趴下充电
  CHARGING_ERROR_INVALID_COMMAND_FORMAT = 5100 + base_charging, // 下发指令格式错误
  CHARGING_ERROR_NAVIGATION_NODE_NOT_STARTED = 5101 + base_charging, // 导航节点未开启,回充功能需要保持导航常开
  CHARGING_ERROR_POINT_FUNCTION_FAILED = 5102 + base_charging,  //点位功能开启失败，未开启导航
  CHARGING_ERROR_POINT_ENTRY_FAILED_NO_QRCODE = 5200 + base_charging, // 点位录入功能启动失败,视野内未出现二维码
  CHARGING_ERROR_POINT_ENTRY_FAILED_ALGORITHM_ERROR = 5201 + base_charging, // 点位录入功能启动失败,算法出现异常
  CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT = 5210 + base_charging, // 回充功能失败,未在指定时间内对齐电极片
  CHARGING_ERROR_CHARGING_FUNCTION_FAILED_NO_POLE = 5211 + base_charging, // 回充功能开启失败，未拿到桩点
};

static inline std::unordered_map<std::string, std::vector<NavigationCode>> statusToCodesMap;

static inline void buildStatusToCodeMapping() {
    statusToCodesMap.clear();

    // Map 状态映射
    statusToCodesMap["mapping/start/success"] = {MAP_CODE_STARTED};
    statusToCodesMap["mapping/stop/success"] = {MAP_CODE_STOPPED};
    statusToCodesMap["mapping/cancel/success"] = {MAP_CODE_CANCELLED};
    // statusToCodesMap["mapping/get_cloud_map"] = {};
    // statusToCodesMap["mapping/get_cloud_map/success"] = {};
    // statusToCodesMap["mapping/get_status/status/0"] = {};
    // statusToCodesMap["mapping/get_status/status/1"] = {};

    // Localization 状态映射
    statusToCodesMap["[Localization] initialization succeed!"] = {NAVIGATION_CODE_RELOCALIZATION_FINISHED};
    statusToCodesMap["localization/stop/success"] = {NAVIGATION_CODE_NODE_STOPPED};
    statusToCodesMap["[Localization] initialization failed!"] = {NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE};
    // statusToCodesMap["localization/set_initial_pose/success"] = {NAVIGATION_CODE_RELOCALIZATION_FINISHED};
    // statusToCodesMap["localization/get_status/status/0"] = {};
    // statusToCodesMap["localization/get_status/status/1"] = {};

    // Navigation 状态映射
    statusToCodesMap["navigation/start/success"] = {NAVIGATION_CODE_NODE_STARTED}; // 导航启动成功
    statusToCodesMap["navigation/stop/success"] = {NAVIGATION_CODE_NODE_STOPPED}; // 导航停止成功
    statusToCodesMap["navigation/set_goal_pose/success"] = {}; // 目标位姿设置成功
    statusToCodesMap["navigation/set_goal_pose/failed"] = {NAVIGATION_CODE_INVALID_COMMAND_FORMAT}; // 目标位姿设置失败

    statusToCodesMap["navigation/state_transition/INITIALIZE"] = {NAVIGATION_CODE_NODE_STARTED};
    statusToCodesMap["navigation/state_transition/EXIT"] = {NAVIGATION_CODE_NODE_STOPPED};
    statusToCodesMap["navigation/state_transition/FAILURE"] = {NAVIGATION_CODE_TARGET_UNREACHABLE};
    statusToCodesMap["navigation/state_transition/ABNORMAL"] = {NAVIGATION_CODE_TARGET_UNREACHABLE};
    statusToCodesMap["navigation/state_transition/WAITING"] = {};
    statusToCodesMap["navigation/state_transition/REACHED"] = {NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED};
    statusToCodesMap["navigation/state_transition/NO_PATH"] = {NAVIGATION_CODE_TARGET_UNREACHABLE};
    statusToCodesMap["navigation/state_transition/GOAL_OCCUPIED"] = {NAVIGATION_CODE_TARGET_UNREACHABLE};
    statusToCodesMap["navigation/state_transition/GOAL_CHANGED"] = {NAVIGATION_CODE_TARGET_UNREACHABLE};
    statusToCodesMap["navigation/state_transition/GOAL_CANCELLED"] = {NAVIGATION_CODE_COMMAND_FAILED};
    statusToCodesMap["navigation/state_transition/TIMEOUT_ODOMETRY"] = {NAVIGATION_CODE_TASK_FAILED};
    statusToCodesMap["navigation/state_transition/TIMEOUT_POINTCLOUD"] = {NAVIGATION_CODE_TASK_FAILED};

    // Autocharge 状态映射
    statusToCodesMap["autocharge/start/success"] = {CHARGING_CODE_CHARGING_STARTED}; // 自动充电启动成功
    statusToCodesMap["autocharge/stop/success"] = {}; // 自动充电停止成功（根据实际需求调整枚举值）

    statusToCodesMap["autocharge/state_transition/INITIALIZE"] = {};
    statusToCodesMap["autocharge/state_transition/EXIT"] = {};
    statusToCodesMap["autocharge/state_transition/SUCCESS"] = {CHARGING_CODE_CHARGING_COMPLETED};
    statusToCodesMap["autocharge/state_transition/FAILURE"] = {CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT};
    statusToCodesMap["autocharge/state_transition/GO_TO_CHARGE_BOARD"] = {};
    statusToCodesMap["autocharge/state_transition/REACHED_CHARGE_BOARD"] = {CHARGING_CODE_POINT_ENTRY_COMPLETED};
    statusToCodesMap["autocharge/state_transition/SIT_DOWN"] = {};
    statusToCodesMap["autocharge/state_transition/CHECK_POWER_CONNECT"] = {};
    statusToCodesMap["autocharge/state_transition/TIMEOUT_RUNNING"] = {CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT};
    statusToCodesMap["autocharge/state_transition/TIMEOUT_ODOMETRY"] = {CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT};
    statusToCodesMap["autocharge/state_transition/TIMEOUT_POINTCLOUD"] = {CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT};
    statusToCodesMap["autocharge/state_transition/TIMEOUT_DETECT"] = {CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT};
    statusToCodesMap["autocharge/state_transition/TIMEOUT_CONNECT_POWER"] = {CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT};

    // Patrol 状态映射
    statusToCodesMap["patrol/start/success"] = {NAVIGATION_CODE_NODE_STARTED}; // 巡逻程序启动成功
    statusToCodesMap["patrol/stop/success"] = {NAVIGATION_CODE_NODE_STOPPED}; // 巡逻程序停止成功
    // statusToCodesMap["patrol/go/success"] = {}; // 巡逻开始移动（无明确对应错误码时可留空）
    // statusToCodesMap["patrol/pause/success"] = {NAVIGATION_CODE_NAVIGATION_TASK_PAUSED}; // 巡逻停止移动
    // statusToCodesMap["patrol/add_patrol_point/success"] = {}; // 巡逻点添加成功
    // statusToCodesMap["patrol/clear_all_patrol_points/success"] = {}; // 所有巡逻点已清空
    // statusToCodesMap["patrol/get_patrol_points/success"] = {}; // 成功获取所有巡逻点
    // statusToCodesMap["patrol/set_patrol_time_limit/success"] = {}; // 单次巡逻时间限制设置成功
    // statusToCodesMap["patrol/set_total_time_limit/success"] = {}; // 总巡逻时间限制设置成功
    // statusToCodesMap["patrol/set_charge_time_limit/success"] = {}; // 充电时间限制设置成功

    statusToCodesMap["patrol/state_transition/INITIALIZE"] = {NAVIGATION_CODE_NODE_STARTED};
    statusToCodesMap["patrol/state_transition/EXIT"] = {NAVIGATION_CODE_NODE_STOPPED};
    statusToCodesMap["patrol/state_transition/FAILURE"] = {NAVIGATION_CODE_TASK_FAILED};
    statusToCodesMap["patrol/state_transition/PAUSE"] = {NAVIGATION_CODE_NAVIGATION_TASK_PAUSED};
    statusToCodesMap["patrol/state_transition/STAND_UP"] = {};
    statusToCodesMap["patrol/state_transition/SELECT_GOAL_POINT"] = {};
    statusToCodesMap["patrol/state_transition/NO_GOAL_POINT_TO_SELECT"] = {NAVIGATION_CODE_TARGET_UNREACHABLE};
    statusToCodesMap["patrol/state_transition/NAVIGATE_TO_GOAL_POINT"] = {};
    statusToCodesMap["patrol/state_transition/GOAL_POINT_REACHED"] = {NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED};
    statusToCodesMap["patrol/state_transition/GOAL_POINT_UNREACHABLE"] = {NAVIGATION_CODE_TARGET_UNREACHABLE};
    statusToCodesMap["patrol/state_transition/NEED_CHARGE"] = {};
    statusToCodesMap["patrol/state_transition/NAVIGATE_TO_CHARGE_BOARD"] = {};
    statusToCodesMap["patrol/state_transition/NAVIGATE_TO_CHARGE_BOARD_FAILED"] = {NAVIGATION_CODE_TARGET_UNREACHABLE};
    statusToCodesMap["patrol/state_transition/REACHED_CHARGE_BOARD"] = {};
    statusToCodesMap["patrol/state_transition/RUN_AUTO_CHARGE"] = {};
    statusToCodesMap["patrol/state_transition/IS_CHARGING"] = {};
    statusToCodesMap["patrol/state_transition/CONNECT_POWER_FAILED"] = {CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT};
    statusToCodesMap["patrol/state_transition/CHARGE_FINISHED"] = {CHARGING_CODE_CHARGING_COMPLETED};
    statusToCodesMap["patrol/state_transition/REACH_PATROL_NUMBER_LIMIT"] = {};
    statusToCodesMap["patrol/state_transition/REACH_PATROL_TIME_LIMIT"] = {};
    statusToCodesMap["patrol/state_transition/REACH_TOTAL_TIME_LIMIT"] = {};
    statusToCodesMap["patrol/state_transition/TIMEOUT_LOCALIZATION"] = {NAVIGATION_CODE_TIME_OUT};
    statusToCodesMap["patrol/state_transition/TIMEOUT_NAVIGATION"] = {NAVIGATION_CODE_COMMAND_FAILED};
    statusToCodesMap["patrol/state_transition/TIMEOUT_TRY_CHARGE"] = {CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT};
}

const std::map<int, std::tuple<std::string, std::string>> rtNavigationCodeDescriptions = {
    {0, {"", ""}},
    // LOCALIZATION code
    
    // NAVIGATION code
    {NAVIGATION_INIT, {"NAVIGATION_INIT", "初始化"}},
    {NAVIGATION_EXIT, {"NAVIGATION_EXIT", "退出"}},
    {NAVIGATION_FAILURE, {"NAVIGATION_FAILURE", "失败"}},
    {NAVIGATION_ABNORMAL, {"NAVIGATION_ABNORMAL", "异常"}},
    {NAVIGATION_WAITING, {"NAVIGATION_WAITING", "等待目标点"}},
    {NAVIGATION_REACHED, {"NAVIGATION_REACHED", "到达目标点"}},
    {NAVIGATION_NO_PATH, {"NAVIGATION_NO_PATH", "没有规划出路"}},
    {NAVIGATION_GOAL_OCCUPIED, {"NAVIGATION_GOAL_OCCUPIED", "目标点被占据"}},
    {NAVIGATION_GOAL_CHANGED, {"NAVIGATION_GOAL_CHANGED", "目标点被修改"}},
    {NAVIGATION_GOAL_CANCELLED, {"NAVIGATION_GOAL_CANCELLED", "目标点被取消"}},
    {NAVIGATION_TIMEOUT_ODOMETRY, {"NAVIGATION_TIMEOUT_ODOMETRY", "里程计超时"}},
    {NAVIGATION_TIMEOUT_POINTCLOUD, {"NAVIGATION_TIMEOUT_POINTCLOUD", "点云超时"}},
                                                                                          
    // AUTOCHARGE code                                                                          
    {AUTOCHARGE_INIT, {"AUTOCHARGE_INIT", "初始化"}},
    {AUTOCHARGE_EXIT, {"AUTOCHARGE_EXIT", "退出"}},
    {AUTOCHARGE_SUCCESS, {"AUTOCHARGE_SUCCESS", "充电成功"}},
    {AUTOCHARGE_FAILURE, {"AUTOCHARGE_FAILURE", "充电失败"}},
    {AUTOCHARGE_GO_TO_CHARGE_BOARD, {"AUTOCHARGE_GO_TO_CHARGE_BOARD", "前往充电板"}},
    {AUTOCHARGE_REACHED_CHARGE_BOARD, {"AUTOCHARGE_REACHED_CHARGE_BOARD", "到达充电板"}},
    {AUTOCHARGE_SIT_DOWN, {"AUTOCHARGE_SIT_DOWN", "卧倒"}}, 
    {AUTOCHARGE_CHECK_POWER_CONNECT, {"AUTOCHARGE_CHECK_POWER_CONNECT", "检查充电连接"}},
    {AUTOCHARGE_TIMEOUT_RUNNING, {"AUTOCHARGE_TIMEOUT_RUNNING", "运行超时"}},
    {AUTOCHARGE_TIMEOUT_ODOMETRY, {"AUTOCHARGE_TIMEOUT_ODOMETRY", "里程计超时"}},
    {AUTOCHARGE_TIMEOUT_POINTCLOUD, {"AUTOCHARGE_TIMEOUT_POINTCLOUD", "点云超时"}},
    {AUTOCHARGE_TIMEOUT_DETECT, {"AUTOCHARGE_TIMEOUT_DETECT", "检测超时"}},
    {AUTOCHARGE_TIMEOUT_CONNECT_POWER, {"AUTOCHARGE_TIMEOUT_CONNECT_POWER", "电源连接超时"}},
    
    // PATROL code                            
    {PATROL_INIT, {"PATROL_INIT", "初始化"}},
    {PATROL_EXIT, {"PATROL_EXIT", "退出"}},
    {PATROL_FAILURE, {"PATROL_FAILURE", "失败"}},
    {PATROL_PAUSE, {"PATROL_PAUSE", "暂停"}},
    {PATROL_STAND_UP, {"PATROL_STAND_UP", "站立"}},
    {PATROL_SELECT_GOAL_POINT, {"PATROL_SELECT_GOAL_POINT", "选择目标点"}},
    {PATROL_NO_GOAL_POINT_TO_SELECT, {"PATROL_NO_GOAL_POINT_TO_SELECT", "没有目标点选择"}},
    {PATROL_NAVIGATE_TO_GOAL_POINT, {"PATROL_NAVIGATE_TO_GOAL_POINT", "正在导航到目标点"}},
    {PATROL_GOAL_POINT_REACHED, {"PATROL_GOAL_POINT_REACHED", "到达目标点"}},
    {PATROL_GOAL_POINT_UNREACHABLE, {"PATROL_GOAL_POINT_UNREACHABLE", "目标点不可到达"}},
    {PATROL_NEED_CHARGE, {"PATROL_NEED_CHARGE", "电量不足需要充电"}},
    {PATROL_NAVIGATE_TO_CHARGE_BOARD, {"PATROL_NAVIGATE_TO_CHARGE_BOARD", "导航到充电板"}},
    {PATROL_NAVIGATE_TO_CHARGE_BOARD_FAILED, {"PATROL_NAVIGATE_TO_CHARGE_BOARD_FAILED", "导航到充电板失败"}},
    {PATROL_REACHED_CHARGE_BOARD, {"PATROL_REACHED_CHARGE_BOARD", "到达充电板"}},
    {PATROL_RUN_AUTO_CHARGE, {"PATROL_RUN_AUTO_CHARGE", "运行自动充电"}},
    {PATROL_IS_CHARGING, {"PATROL_IS_CHARGING", "正在充电"}},
    {PATROL_CONNECT_POWER_FAILED, {"PATROL_CONNECT_POWER_FAILED", "充电失败"}},
    {PATROL_CHARGE_FINISHED, {"PATROL_CHARGE_FINISHED", "完成充电"}},
    {PATROL_REACH_PATROL_NUMBER_LIMIT, {"PATROL_REACH_PATROL_NUMBER_LIMIT", "到达巡逻次数上限"}},
    {PATROL_REACH_PATROL_TIME_LIMIT, {"PATROL_REACH_PATROL_TIME_LIMIT", "到达巡逻时间上限"}},
    {PATROL_REACH_TOTAL_TIME_LIMIT, {"PATROL_REACH_TOTAL_TIME_LIMIT", "到达巡逻+充电的时间上限"}},
    {PATROL_TIMEOUT_LOCALIZATION, {"PATROL_TIMEOUT_LOCALIZATION", "定位超时"}},
    {PATROL_TIMEOUT_NAVIGATION, {"PATROL_TIMEOUT_NAVIGATION", "导航超时"}},
    {PATROL_TIMEOUT_TRY_CHARGE, {"PATROL_TIMEOUT_TRY_CHARGE", "充电超时"}}
    
};
const std::map<int, std::tuple<std::string, std::string>> navigationCodeDescriptions = {
    {0, {"", ""}},
    {MAP_CODE_STARTED, {"MAP_CODE_STARTED", "建图节点已开启"}},
    {MAP_CODE_STOPPED, {"MAP_CODE_STOPPED", "建图节点已关闭"}},
    {MAP_CODE_CANCELLED, {"MAP_CODE_CANCELLED", "当前建图流程已取消,建图节点关闭"}},
    {MAP_CODE_DELETED, {"MAP_CODE_DELETED", "已删除指定地图"}},
    {ERROR_CODE_INVALID_COMMAND_FORMAT, {"ERROR_CODE_INVALID_COMMAND_FORMAT", "下发指令的消息格式错误"}},
    {ERROR_CODE_MISSING_TASK_PARAM, {"ERROR_CODE_MISSING_TASK_PARAM", "下发指令未提供“任务”参数，或“任务”参数不合法"}},
    {ERROR_CODE_MAP_DELETE_FAILED_NO_ID, {"ERROR_CODE_MAP_DELETE_FAILED_NO_ID", "无指定id的地图,删除操作失败"}},
    {ERROR_CODE_MAP_SAVE_FAILED, {"ERROR_CODE_MAP_SAVE_FAILED", "地图保存过程出错"}},
    {ERROR_CODE_MAP_ID_MISMATCH, {"ERROR_CODE_MAP_ID_MISMATCH", "建图结束指令的地图id与当前地图id不匹配"}},
    {ERROR_CODE_MAP_UPLOAD_FAILED, {"ERROR_CODE_MAP_UPLOAD_FAILED", "建图过程中上传地图失败,URL不合法或感知主机无法上传"}},
    {ERROR_CODE_NEW_MAP_COMMAND_DURING_BUILD, {"ERROR_CODE_NEW_MAP_COMMAND_DURING_BUILD", "处于建图流程时，收到新的建图开始指令或删除地图指令"}},
    {ERROR_CODE_NAVIGATION_COMMAND_DURING_BUILD, {"ERROR_CODE_NAVIGATION_COMMAND_DURING_BUILD", "处于导航流程时，收到建图开始/结束/取消/删除地图指令"}},
    {ERROR_CODE_INVALID_BUILD_COMMAND, {"ERROR_CODE_INVALID_BUILD_COMMAND", "不处于建图流程时，收到建图结束/取消指令"}},
    {ERROR_CODE_NODE_CRASHED, {"ERROR_CODE_NODE_CRASHED", "节点崩溃，请重启建图流程"}},
    {ERROR_CODE_MAPINI_TIME_OUT, {"ERROR_CODE_MAPINI_TIME_OUT", "地图初始化超时"}},
    {ERROR_CODE_MAP_COMMAND_DURING_FOLLOW, {"ERROR_CODE_MAP_COMMAND_DURING_FOLLOW", "设备正在跟随任务中,无法建图"}},
    {NAVIGATION_CODE_NODE_STARTED, {"NAVIGATION_CODE_NODE_STARTED", "导航节点已开启"}},
    {NAVIGATION_CODE_NODE_STOPPED, {"NAVIGATION_CODE_NODE_STOPPED", "导航节点已关闭"}},
    {NAVIGATION_CODE_SINGLE_POINT_NAVIGATION_COMPLETED, {"NAVIGATION_CODE_SINGLE_POINT_NAVIGATION_COMPLETED", "单点导航任务已完成,返回下发点位时的uid"}},
    {NAVIGATION_CODE_NAVIGATION_TASK_PAUSED, {"NAVIGATION_CODE_NAVIGATION_TASK_PAUSED", "导航任务已暂停"}},
    {NAVIGATION_CODE_NAVIGATION_TASK_CANCELED, {"NAVIGATION_CODE_NAVIGATION_TASK_CANCELED", "导航任务已取消"}},
    {NAVIGATION_CODE_RELOCALIZATION_FINISHED, {"NAVIGATION_CODE_RELOCALIZATION_FINISHED", "重定位任务已完成"}},
    {NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED, {"NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED", "整体导航任务已完成,返回任务的taskId"}},
    {NAVIGATION_CODE_DRIFT_DETECTED, {"NAVIGATION_CODE_DRIFT_DETECTED", "检测到漂移，正在自动重定位"}},
    {NAVIGATION_CODE_INVALID_COMMAND_FORMAT, {"NAVIGATION_CODE_INVALID_COMMAND_FORMAT", "下发指令的消息格式错误"}},
    {NAVIGATION_CODE_MISSING_OR_INVALID_TASK_PARAM, {"NAVIGATION_CODE_MISSING_OR_INVALID_TASK_PARAM", "下发指令未提供“任务”参数，或“任务”参数不合法"}},
    {NAVIGATION_CODE_INVALID_GAIT_TYPE, {"NAVIGATION_CODE_INVALID_GAIT_TYPE", "下发的步态类型不合法"}},
    {NAVIGATION_CODE_CANNOT_GO, {"NAVIGATION_CODE_CANNOT_GO", "无法出发"}},
    {NAVIGATION_CODE_TARGET_UNREACHABLE, {"NAVIGATION_CODE_TARGET_UNREACHABLE", "下发的目标点位不可达或点位不合法"}},
    {NAVIGATION_CODE_MAP_ID_NOT_FOUND, {"NAVIGATION_CODE_MAP_ID_NOT_FOUND", "导航任务启动失败,地图id不存在"}},
    {NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION, {"NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION", "不处于导航流程时，收到导航结束/目标点设置/暂停/取消/重定位指令"}},
    {NAVIGATION_CODE_MAP_ID_MISMATCH, {"NAVIGATION_CODE_MAP_ID_MISMATCH", "导航结束/目标点设置/暂停/取消/重定位指令的地图id与当前地图id不匹配"}},
    {NAVIGATION_CODE_COMMAND_FAILED, {"NAVIGATION_CODE_COMMAND_FAILED", "目标点设置/暂停/取消/重定位任务失败"}},
    {NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_NAVIGATION, {"NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_NAVIGATION", "处于导航流程时，收到新的导航开始指令"}},
    {NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_MAPPING, {"NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_MAPPING", "处于建图流程时，收到导航开始指令"}},
    {NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE, {"NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE", "重定位误差过大，请手动进行定位或检查机器狗所处地图是否正确"}},
    {NAVIGATION_CODE_NODE_CRASHED, {"NAVIGATION_CODE_NODE_CRASHED", "节点崩溃，请重启导航流程"}},
    {NAVIGATION_CODE_TASK_FAILED, {"NAVIGATION_CODE_TASK_FAILED", "导航任务失败，可能是路径规划或导航执行失败"}},
    {NAVIGATION_CODE_TIME_OUT, {"NAVIGATION_CODE_TIME_OUT", "重定位超时"}},
    {NAVIGATION_CODE_NO_AUTOREPOSITION, {"NAVIGATION_CODE_NO_AUTOREPOSITION", "无自动重定位，请进行手动重定位"}},
    {CHARGING_CODE_POINT_ENTRY_STARTED, {"CHARGING_CODE_POINT_ENTRY_STARTED", "点位录入功能启动成功"}},
    {CHARGING_CODE_QRCODE_DETECTED, {"CHARGING_CODE_QRCODE_DETECTED", "视野内出现二维码,开始对准电极片"}},
    {CHARGING_CODE_POINT_ENTRY_COMPLETED, {"CHARGING_CODE_POINT_ENTRY_COMPLETED", "电极片已对准,点位录入功能完成,并返回回充点位导航坐标与对应地图id"}},
    {CHARGING_CODE_CHARGING_POINT_COORDINATES, {"CHARGING_CODE_CHARGING_POINT_COORDINATES", "{\"x\":0.0, \"y\":0.0, \"angle\":0.0}, \"mapId\": \"map1\" | 回充点位导航目标点点坐标与对应地图id"}},
    {CHARGING_CODE_CHARGING_STARTED, {"CHARGING_CODE_CHARGING_STARTED", "回充功能启动成功"}},
    {CHARGING_CODE_CHARGING_COMPLETED, {"CHARGING_CODE_CHARGING_COMPLETED", "回充功能完成,可以趴下充电"}},
    {CHARGING_ERROR_INVALID_COMMAND_FORMAT, {"CHARGING_ERROR_INVALID_COMMAND_FORMAT", "下发指令格式错误"}},
    {CHARGING_ERROR_NAVIGATION_NODE_NOT_STARTED, {"CHARGING_ERROR_NAVIGATION_NODE_NOT_STARTED", "导航节点未开启,回充功能需要保持导航常开"}},
    {CHARGING_ERROR_POINT_FUNCTION_FAILED, {"CHARGING_ERROR_POINT_FUNCTION_FAILED", "点位功能开启失败，未开启导航"}},
    {CHARGING_ERROR_POINT_ENTRY_FAILED_NO_QRCODE, {"CHARGING_ERROR_POINT_ENTRY_FAILED_NO_QRCODE", "点位录入功能启动失败,视野内未出现二维码"}},
    {CHARGING_ERROR_POINT_ENTRY_FAILED_ALGORITHM_ERROR, {"CHARGING_ERROR_POINT_ENTRY_FAILED_ALGORITHM_ERROR", "点位录入功能启动失败,算法出现异常"}},
    {CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT, {"CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT", "回充功能失败,未在指定时间内对齐电极片"}},
    {CHARGING_ERROR_CHARGING_FUNCTION_FAILED_NO_POLE, {"CHARGING_ERROR_CHARGING_FUNCTION_FAILED_NO_POLE", "回充功能开启失败，未拿到桩点"}}
};


/********************************机器人任务管理********************************************** */

enum HomiTaskStatus {
  ROBOT_TASK_STATUS_STARTING,              //任务开始
  ROBOT_TASK_STATUS_EXECUTING,             //任务执行中
  ROBOT_TASK_STATUS_FAILED,                //任务执行失败
  ROBOT_TASK_STATUS_COMPLETED              //任务完成
};

enum HomiTaskType {
  /*********************算法相关************************/
  ROBOT_TASK_TYPE_FOLLOWME = 0,             //主动跟随
  ROBOT_TASK_TYPE_MAPPINMG,                 //建图
  ROBOT_TASK_TYPE_NAV_PLANNING,             //执行路径规划
  /*********************音频相关************************/
  ROBOT_TASK_TYPE_TALKING  = 100,           //固话通话
  ROBOT_TASK_TYPE_VIDEO_CALL,               //视频通话

  /*********************控制相关************************/
  ROBOT_TASK_TYPE_MOVING  = 1000,           //遥控控制移动
  ROBOT_TASK_TYPE_EXECUTE_ACTION,           //执行动作

};  

enum HomiTaskSubType {
  ROBOT_TASK_SUBTYPE_STARTING,          //任务开始
};

/******************************************************************************************** */
#define MAX_JOINT_NUM 12
#ifndef UNITREE
const std::string jointNames[MAX_JOINT_NUM + 1] = {
    "FLSwingJoint",
    "FLKneeJoint",
    "FLHipJoint",
    "FRSwingJoint",
    "FRKneeJoint",
    "FRHipJoint",
    "BLSwingJoint",
    "BLKneeJoint",
    "BLHipJoint",
    "BRSwingJoint",
    "BRKneeJoint",
    "BRHipJoint"
};

const std::string MotionControlBoardUser = "ysc";
const std::string MotionControlBoardPwd = "\'";
const std::string MotionControlBoardHost = "*************";
const std::string MotionControlBoard_cmd_base = "sshpass -p \"" + MotionControlBoardPwd + "\" ssh -o StrictHostKeyChecking=no " +
            MotionControlBoardUser + "@" + MotionControlBoardHost + " ";

const std::vector<std::string> nvidiaProcessList = {
    "/follow_strategy/control_catch_turtle_v1",
    "/follow_strategy/target_twist_estimate",
    "/pixel2world_pose/pixel",
    "/cmcc_rtc/cmcc_rtc_node",
    "/follow_rcs/follow_rcs",
    "/nvidia_control/homi_ws_server",
    "/nvidia_control/nvidia_control",
    "/video_gst_nv/video_gst_nv_node",
    "/service_scripts/run_nav_outdoor.sh",
    "/nav_outdoor_ros2_main_interface/nav_outdoor_main_ros2",
    "/service_scripts/start_loc_v1.sh",
    "/action_server/actionControlNode",
    "/service_scripts/follow_console.sh",
    "/robot_follow_console/follow_console",
    "/service_scripts/follow_start.sh",
    "/robot_follow_dog/ros_control",
#ifdef YSC1_1
    "/service_scripts/start_uwb_loc.sh",
    "/robot_uwb_loc/LocationNode",
    "/service_scripts/start_accompany.sh",
    "/accompany_control/AccompanyNode",
#endif
    "/service_scripts/ros1_bridge.sh",
    "/service_scripts/ros1_console.sh",
    "/service_scripts/ros1_inquire.sh",
    "/ros1_lite_console/lite_console.py",
    "/inquire_service_status/task_status_node"
};

const std::vector<std::string> MotionControlBoardProcessList = {
#ifdef YSC1_1
    "network/network_node",
#endif
    "ble/ble_node",
    "andlink/run_network_ros2.sh",
    "andlink/run_andlink_ros2.sh",
    "andlink/andlink_node"
};

const std::vector<std::string> InteractiveBoardProcessList = {
    "/homi_speech/speech_core",
    "/homi_speech/helper",
    "/homi_speech/upload_image",
    "/audio_player_node",
    "/audio_recorder_node",
    "/expression_node",
    "/homi_player",
    "/robdog_control_node",
    "/video_gst_node"
};



const std::map<std::string, std::string> joint_map = {
    {"FRHipJoint", "右前腿机身关节"},
    {"FRThighJoint", "右前腿大腿关节"},
    {"FRCalfJoint", "右前腿小腿关节"},
    {"FLHipJoint", "左前腿机身关节"},
    {"FLThighJoint", "左前腿大腿关节"},
    {"FLCalfJoint", "左前腿小腿关节"},
    {"BRHipJoint", "右后腿机身关节"},
    {"BRThighJoint", "右后腿大腿关节"},
    {"BRCalfJoint", "右后腿小腿关节"},
    {"BLHipJoint", "左后腿机身关节"},
    {"BLThighJoint", "左后腿大腿关节"},
    {"BLCalfJoint", "左后腿小腿关节"}
};

#else

const std::vector<std::string> UnitreeProcessList = {
    "run_xiaoli_server.sh",
    "start_accompany.sh",
    "script_service/follow_console.sh",
    "run_nav_outdoor.sh",
    "start_loc_v1.sh",
    "run_unitree_transfer.sh",
    "start_uwb_loc.sh",
    "launch_package unitree_ctrl_robdog.py",
    "unitree_topic_transfer/imu_odom_transfer",
    "robot_uwb_loc/LocationNode",
    "accompany_control/AccompanyNode",
    "nav_outdoor_ros2_main_interface/nav_outdoor_main_ros2",
    "dogfollow_console/follow_console",
    "follow_start.sh",
    "dogfollow_test/follow_demo",
    "peripherals_node",
    "pwm_touch_node",
    "network_node",
    "andlink_node",
    "ble_node",
    "a2dp_node --ros-args",
    "expression_node",
    "robdog_control_node",
    "video_gst_node",
    "video_gst_neck_node",
    "audio_recorder_node",
    "audio_player_node",
    "cmcc_rtc_node",
    "homi_speech/helper",
    "homi_speech/speech_core",
    "homi_speech/upload_image.py",
    "live_stream_node",
    "homi_player",
    "homi_ws_server",
    "uwb_server",
    "follow_rcs",
    "control_catch_turtle_v1",
    "target_twist_estimate",
    "pixel",
    "nvidia_control",
    "vtn_obu_node",
    "message_obu_robot",
    "gnss_bridge_pub_pkg/gnss_pub_node"
};
#endif

#define PropertyBuilderByName(type, name, initialize)\
	public:\
	type m_##name = initialize;\
	inline void set##name(type v){\
	m_##name = v;\
}\
	inline type get##name(){ \
	return m_##name;\
}\

