cmake_minimum_required(VERSION 3.8)
project(robdog_control)

if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 11)
  set(CMAKE_CXX_STANDARD 14)
endif()

# 检测当前 Ubuntu 版本号
execute_process(
  COMMAND lsb_release -rs
  OUTPUT_VARIABLE UBUNTU_VERSION
  OUTPUT_STRIP_TRAILING_WHITESPACE
)

message(STATUS "Detected Ubuntu version: ${UBUNTU_VERSION}")

if(UBUNTU_VERSION VERSION_GREATER_EQUAL "22.04")
  message(STATUS "Ubuntu 22.04 or newer detected.")
  # 针对 Ubuntu 22.04 或更高版本的特定处理逻辑
# 设置 ROS 2 环境
  if(NOT DEFINED ENV{AMENT_PREFIX_PATH})
    set(ENV{AMENT_PREFIX_PATH} "/opt/ros/humble")
  endif()
  link_directories(/opt/ros/humble/lib)
elseif(UBUNTU_VERSION VERSION_GREATER_EQUAL "20.04")
  message(STATUS "Ubuntu 20.04 detected.")
  # 针对 Ubuntu 20.04 的特定处理逻辑
  # 设置 ROS 2 环境
  if(NOT DEFINED ENV{AMENT_PREFIX_PATH})
    set(ENV{AMENT_PREFIX_PATH} "/opt/ros/foxy")
  endif()
link_directories(/opt/ros/foxy/lib)
else()
  message(STATUS "Older Ubuntu version detected.")
  # 针对更旧版本的特定处理逻辑
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_action REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(OpenCV REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(image_transport REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(homi_speech_interface REQUIRED)
find_package(Boost REQUIRED COMPONENTS filesystem)
find_package(std_srvs REQUIRED)
find_package(rosidl_typesupport_cpp REQUIRED)
# find_package(tinyxml2 REQUIRED)
# find_package(ASound REQUIRED)

# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)

# 查找系统平台
if (${CMAKE_HOST_WIN32})
    if(CMAKE_CXX_FLAGS MATCHES "-m32")
        set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/win/x86)
    else()
        set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/win/x64)
    endif()
elseif(${CMAKE_HOST_UNIX})
    message("Linux system detected.")
    
    # Linux 平台特定处理逻辑
    if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
        message("Support x86-64/AMD64 architecture.")
        if(CMAKE_CXX_FLAGS MATCHES "-m32")
            message("32-bit architecture detected.")
            set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/x86)
        else()
            message("64-bit architecture detected.")
            set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/x86_64)
        endif()
    elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
        message("ARM 64-bit architecture detected, no support for x86-64/AMD64.")
        set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/aarch64)
    endif()
elseif (${CMAKE_SYSTEM} MATCHES "FreeBSD|OpenBSD")
    message("FreeBSD or OpenBSD system detected.")
endif()

# 检测当前 Ubuntu 版本号
execute_process(
  COMMAND lsb_release -rs
  OUTPUT_VARIABLE UBUNTU_VERSION
  OUTPUT_STRIP_TRAILING_WHITESPACE
)

message(STATUS "Detected Ubuntu version: ${UBUNTU_VERSION}")

if(UBUNTU_VERSION VERSION_GREATER_EQUAL "22.04")
  message(STATUS "Ubuntu 22.04 or newer detected.")
  # 针对 Ubuntu 22.04 或更高版本的特定处理逻辑
  set(CMAKE_CXX_STANDARD 17)
  set(tinyxml2_DIR /usr/lib/x86_64-linux-gnu)
elseif(UBUNTU_VERSION VERSION_GREATER_EQUAL "20.04")
  message(STATUS "Ubuntu 20.04 detected.")
  # 针对 Ubuntu 20.04 的特定处理逻辑
  set(CMAKE_CXX_STANDARD 14)
  set(tinyxml2_DIR /usr/lib/x86_64-linux-gnu)
else()
  message(STATUS "Older Ubuntu version detected.")
  # 针对更旧版本的特定处理逻辑
  set(CMAKE_CXX_STANDARD 11)
endif()

############ robdog_control_node ############
get_filename_component(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR} DIRECTORY)
get_filename_component(MAIN_DIR ${SRC_DIR} DIRECTORY)
message(${MAIN_DIR})

if(${CMAKE_HOST_UNIX})
    message("当前系统为 Linux 架构")
    # Linux 平台特定处理逻辑
    if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
        message("当前系统为 x86-64/AMD64 架构")
        if(CMAKE_CXX_FLAGS MATCHES "-m32")
            message("m32")
            find_library(WEBSOCKET_LIB WebSocket PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
            find_library(DDSC_LIB ddsc PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
            find_library(DDSCXX_LIB ddscxx PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
            find_library(UNITREE_SDK2_LIB unitree_sdk2 PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
        else()
            message("x64")
            find_library(WEBSOCKET_LIB WebSocket PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
            find_library(DDSC_LIB ddsc PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
            find_library(DDSCXX_LIB ddscxx PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
            find_library(UNITREE_SDK2_LIB unitree_sdk2 PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
        endif()
    elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
        message("aarch64")
        find_library(WEBSOCKET_LIB WebSocket PATHS ${MAIN_DIR}/lib/aarch64 NO_DEFAULT_PATH)
        find_library(DDSC_LIB ddsc PATHS ${MAIN_DIR}/lib/aarch64 NO_DEFAULT_PATH)
        find_library(DDSCXX_LIB ddscxx PATHS ${MAIN_DIR}/lib/aarch64 NO_DEFAULT_PATH)
        find_library(UNITREE_SDK2_LIB unitree_sdk2 PATHS ${MAIN_DIR}/lib/aarch64 NO_DEFAULT_PATH)
    endif()
elseif (${CMAKE_SYSTEM} MATCHES "FreeBSD|OpenBSD")
    # FreeBSD 或 OpenBSD 平台特定处理逻辑
endif()
if(NOT WEBSOCKET_LIB)
  message(FATAL_ERROR "Could not find libWebSocket.so")
endif()

# 添加动态库
add_library(WebSocket SHARED IMPORTED)
set_target_properties(WebSocket PROPERTIES
  IMPORTED_LOCATION ${WEBSOCKET_LIB}
)

# 安装动态库
if(NOT CMAKE_CUSTOM_LIB_INSTALL)
install(FILES ${WEBSOCKET_LIB} DESTINATION lib/${PROJECT_NAME})
else()
install(FILES ${WEBSOCKET_LIB} DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL})
endif()

include_directories(
 ${CMAKE_CURRENT_SOURCE_DIR}
 ${CMAKE_CURRENT_BINARY_DIR}
 ${CMAKE_CURRENT_SOURCE_DIR}/include
 ${CMAKE_CURRENT_SOURCE_DIR}/src
 ${OpenCV_INCLUDE_DIRS}
 ${homi_speech_interface_INCLUDE_DIRS}
 ${MAIN_DIR}/src/homi_speech_interface/include
 ${MAIN_DIR}/include
 ${Boost_INCLUDE_DIRS}
 ${MAIN_DIR}/include/unitree
 ${MAIN_DIR}/include/unitree/ddscxx
)

SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -lcurl")
set(tinyxml2_DIR /usr/lib/x86_64-linux-gnu)

add_executable(
  robdog_control_node

  src/robdog_control.cpp
  src/robdogNode/robdog_ctrl_node.h
  src/robdogNode/robdog_ctrl_node.cpp
  src/robdogNode/robdog_ctrl_api.h
  src/robdogNode/robdog_ctrl_unitree.h
  src/robdogNode/robdog_ctrl_unitree.cpp
  src/robdogNode/robdog_ctrl_deep.h
  src/robdogNode/robdog_ctrl_deep.cpp

  src/robotMgr/robot_info_mgr.h
  src/robotMgr/robot_info_mgr.cpp

  src/robotInfoCfg/read_map_point_cfg.h
  src/robotInfoCfg/read_map_point_cfg.cpp

  src/robdogCenter/robdog_center_mgr.h
  src/robdogCenter/robdog_center_mgr.cpp

  src/robdogHandPosCtrl/robdog_hand_pos.cpp
  src/robdogHandPosCtrl/robdog_hand_pos.h

  src/alarmMgr/robor_alarm_mgr.h
  src/alarmMgr/robor_alarm_mgr.cpp
  src/alarmMgr/alarm_info.h
  src/alarmMgr/alarm_info.cpp

  src/taskMgr/task_info_mgr.h
  src/taskMgr/task_info_mgr.cpp
  src/taskMgr/task_info.h
  src/taskMgr/task_info.cpp

  src/robotState/RobotState.h
  src/robotState/RobotState.cpp
  src/robotTrip/trip.cpp

  src/public/audio_ctrl.h
  src/public/audio_ctrl.cpp
  
  src/public/litedb.h
  src/public/litedb.cpp
  src/public/taskBase.h
  src/public/taskBase.cpp

  src/public/vedio_change.cpp
  src/public/tools.h

  src/robotSmartRemind/robot_smart_remind.cpp
  src/robotSmartRemind/robot_smart_remind.h

  src/followMe/followMeNode.cpp
  src/followMe/followMeNode.h
)

ament_target_dependencies(robdog_control_node rclcpp std_msgs geometry_msgs tf2_ros homi_speech_interface std_srvs rosidl_typesupport_cpp nav_msgs)

target_compile_features(robdog_control_node PUBLIC c_std_99 cxx_std_17)  # Require C99 and C++17
# ament_target_dependencies(deep_udp_ctrl rclcpp std_msgs geometry_msgs)
target_link_libraries(robdog_control_node 
  #curl 
  jsoncpp 
  tinyxml2
  #WS
  ${WEBSOCKET_LIB}
  asound
  pthread
  sqlite3
  std_srvs__rosidl_typesupport_cpp
  rosidl_typesupport_cpp
  nav_msgs::nav_msgs__rosidl_typesupport_cpp
  ${Boost_LIBRARIES}
)

find_library(UUID_LIB uuid)

#find_library(DDSC_LIB ddsc PATHS /opt/unitree_robotics/lib NO_DEFAULT_PATH)
#find_library(DDSCXX_LIB ddscxx PATHS /opt/unitree_robotics/lib NO_DEFAULT_PATH)
#find_library(UNITREE_SDK2_LIB unitree_sdk2 PATHS /opt/unitree_robotics/lib NO_DEFAULT_PATH)
#find_library(UNITREE_ROS2_IDL_CPP_LIB unitree_ros2_idl_cpp)

set_target_properties(robdog_control_node PROPERTIES
  INSTALL_RPATH "${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}"
)

if (UUID_LIB)
    target_link_libraries(robdog_control_node ${UUID_LIB})
else()
    message(FATAL_ERROR "libuuid not found!")
endif()

if (DDSC_LIB)
    target_link_libraries(robdog_control_node ${DDSC_LIB})
    get_filename_component(DDSC_LIB_PATH ${DDSC_LIB} DIRECTORY)
else()
    message(FATAL_ERROR "libddsc not found!")
endif()

if (DDSCXX_LIB)
    target_link_libraries(robdog_control_node ${DDSCXX_LIB})
    get_filename_component(DDSCXX_LIB_PATH ${DDSCXX_LIB} DIRECTORY)
else()
    message(FATAL_ERROR "libddscxx not found!")
endif()

if (UNITREE_SDK2_LIB)
    target_link_libraries(robdog_control_node ${UNITREE_SDK2_LIB})
else()
    message(FATAL_ERROR "libunitree_sdk2 not found!")
endif()

#if (UNITREE_ROS2_IDL_CPP_LIB)
#    target_link_libraries(robdog_control_node ${UNITREE_ROS2_IDL_CPP_LIB})
#else()
#    message(FATAL_ERROR "libunitree_ros2_idl_cpp not found!")
#endif()

install(TARGETS robdog_control_node DESTINATION lib/${PROJECT_NAME})
install(DIRECTORY resource DESTINATION share/${PROJECT_NAME}/)
if(CMAKE_CUSTOM_LIB_INSTALL)
if(DDSC_LIB)
install(PROGRAMS
  ${DDSC_LIB}
  ${DDSC_LIB_PATH}/libddsc.so.0
  DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL}
)
endif()
if(DDSCXX_LIB)
install(PROGRAMS
  ${DDSCXX_LIB}
  ${DDSCXX_LIB_PATH}/libddscxx.so.0
  DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL}
)
endif()
endif()
if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  set(ament_cmake_copyright_FOUND TRUE)
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

set(CMAKE_CUSTOM_PRODUCT_NO "1_0" CACHE STRING "Custom product number (e.g., 1_0. 1_1, 2_0)")
if("${CMAKE_CUSTOM_PRODUCT_NO}" STREQUAL "1_0")
  target_compile_definitions(robdog_control_node PUBLIC YSC1_0)
elseif("${CMAKE_CUSTOM_PRODUCT_NO}" STREQUAL "1_1")
  target_compile_definitions(robdog_control_node PUBLIC YSC1_1)
elseif("${CMAKE_CUSTOM_PRODUCT_NO}" STREQUAL "2_0")
  target_compile_definitions(robdog_control_node PUBLIC UNITREE)
endif()

ament_package()
