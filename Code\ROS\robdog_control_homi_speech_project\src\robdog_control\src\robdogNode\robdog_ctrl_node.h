#pragma once
#include <rclcpp/rclcpp.hpp>
#include <netinet/in.h>
#include <stdio.h>
#include <unistd.h>
#include <cstring>

// ROS2
#include "rclcpp/timer.hpp"  // 包含 TimerEvent 的定义
#include "geometry_msgs/msg/twist.hpp"
#include "homi_speech_interface/msg/robdog_action.hpp"
#include "homi_speech_interface/msg/wakeup.hpp"
#include "homi_speech_interface/msg/propriety_set.hpp"
#include "homi_speech_interface/msg/robdog_state.hpp"
#include "homi_speech_interface/msg/continue_move.hpp"
#include "homi_speech_interface/srv/assistant_abort.hpp"
#include "homi_speech_interface/srv/set_wake_event.hpp"
#include "homi_speech_interface/srv/peripherals_ctrl.hpp"
#include "homi_speech_interface/srv/peripherals_status.hpp"
#include "homi_speech_interface/msg/new_udp_connect.hpp"
#include "std_msgs/msg/string.hpp"
#include <map>
#include <string>
#include <tuple>
#include <unordered_map>
#include "robdogCenter/robdog_center_mgr.h"
#include "robdogHandPosCtrl/robdog_hand_pos.h"
#include "robdog_ctrl_deep.h"
#include "robdog_ctrl_unitree.h"
#include <bitset>
// #include <geometry_msgs/msg/twist.hpp>

static const uint16_t LOCAL_PORT_NUMBER = 2368;   // default data port
static const uint16_t REMOTE_PORT_NUMBER = 8308;  // default position port
static const float ULTS_THR=0.28;

extern homi_speech_interface::msg::ContinueMove::SharedPtr g_robot_motion;
extern rclcpp::TimerBase::SharedPtr timer;
extern bool timer_active;

/*
// 机器狗状态[基础状态]
#define STATE_PRADOWN 1        // 趴下状态
#define STATE_READY_TO_STAND 4 // 准备起立状态
#define STATE_STANDING 5       // 正在起立状态
#define STATE_FORCE_CONTROL 6   // 力控状态
#define STATE_DOWNING 7        // 正在趴下状态
#define STATE_PROTECTION 8     // 失控保护状态
#define STATE_ADJUSTMENT 9     // 姿态调整状态
#define STATE_TURN_OVER 11     // 执行翻身动作
#define STATE_ZEROING 17       // 回零状态
#define STATE_BACKFLIP 18      // 执行后空翻动作
#define STATE_GREET 20          // 执行打招呼动作
*/

#define STAND_UP_CODE  0x21010202
#define GET_DOWN_CODE  0x21010202
#define TWIST_BODY_CODE 0x21010204
#define TURN_OVER_CODE 0x21010205
#define BACKFLIP_CODE  0x21010502
#define GREETING_CODE  0x21010507
#define JUMP_FORWARD_CODE  0x2101050B
#define TWIST_JUMP_CODE 0x2101020D
#define TWIST_ASS_CODE 0x21010508
#define SHAKE_BODY_CODE 0x21010509
#define DANCE_CODE 0x2101030C

// **************************** 状态接收 *********************8
#define LOCAL_PORT 43897
#define ROBOT_STATE_CMD 0x0906
#define BATTERY_CODE 0x11050F01
#define TEMPERATURE_CODE 0x11050405
#define CPU_TEMPERATURE_CODE 0x11050E03
#define IMU_DATA_CODE 0x11050402
#define VEL_TIME_INTERVAL 200     //ms

struct EthCommand{
  uint32_t code;
  union{
    uint32_t value;
    uint32_t paramters_size;
  };
  struct {
    ///< indicate the massage whether has one more
    ///< @param 1 multiple value;
    ///< @param 0 single value;
    uint32_t type  : 8;
    ///> the sequence number of massage
    uint32_t count : 24;
  };
};

namespace command_type{
  enum CommandType{
    kSingleValue = 0,
    kMessValues = 1,
  };
}

const size_t kCommandDataBufferSize = 1024;

struct CommandMessage{
  EthCommand command;
  char data_buffer[kCommandDataBufferSize];
};

/* 机器狗厂商定义 */
typedef enum
{
    ROB_MANU_DEEP_LITE = 0,
    ROB_MANU_UNITREE_GO2,
    ROB_MANU_MAX
}RobManuModel;

// class CommandTemp {
//   private:
//     std::bitset<32> command_code_;
//     union{
//       int32_t command_value_;
//       size_t command_parameters_size_;
//     };
//     void* command_parameters_;

//   public:
//     CommandTemp();
//     CommandTemp(uint32_t command_code,int32_t command_value);
//     CommandTemp(uint32_t command_code,size_t command_parameters_size,void* command_parameters);

//     virtual ~CommandTemp();

//     std::bitset<32>& GetCommandCode();
//     int32_t GetCommandValue();
//     size_t GetCommandParametersSize();
//     void* GetCommandParameters();

//     friend std::ostream& operator<<(std::ostream& stream, CommandTemp& c);
// }; ///< end of Command

// ***************************************************************************************
class RobdogCtrlNode : public rclcpp::Node {
  public:
    RobdogCtrlNode();
    ~RobdogCtrlNode(); 
    
    void initSocket(std::string devip_str_, int port_, int remote_port);
    void updateSocket(const homi_speech_interface::msg::NewUdpConnect::SharedPtr msg) ;
    int getPacket(char *pkt, size_t packet_size);
    int getPacket_temperature(char *pkt, size_t packet_size);
    int sendPacket(uint8_t *pkt, size_t packet_size);
    int sendPacket_light(uint8_t *pkt, size_t packet_size);
    int sendPacket_temperature(uint8_t *pkt, size_t packet_size);
    void initNode();

    void StandUp();
    void GetDown();
    void HeartBeatCallback() ;
    void send_command();
    void send_command_temperature();
    void publishRobdagStateToQt(std::string robot_state_details);
    void iflyCallback(const homi_speech_interface::msg::Wakeup::SharedPtr msg);
    void handle_UDP_data(char *data,size_t length);
    void handleLightControl(unsigned int cmd);
    void deep_ctl(int cmdID,int cmdValue,int cmdValueEx);
    void userDefinedCtrlCallback(const std::shared_ptr<homi_speech_interface::msg::ProprietySet>& msg);
    void timerCallbackForRobotMove();
    void positionCtrl(float x, float y,float radian);
    void playAudio(const std::string& filePath);
    // 调用服务式播放文件
    void playAudioFile(const std::string& filePath);
    void changeExpression(const int status);
    int sendPacketUserDefine(uint8_t *pkt, size_t packet_size) ;
    void leftNine();
    void rightNine();
    void cancelHandCode();
    
    void handleHandPosAction(int pos_type);
    void handleInteractionAction(std::string skill);
    std::string getResourcePath(const std::string &file_name);
    RobManuModel getRobManuModel();
    void utStatusReportCallback();
    void peripherals_send_request();

    rclcpp::TimerBase::SharedPtr ut_status_report_timer;
    RobManuModel robManuModel = ROB_MANU_DEEP_LITE;
    std::shared_ptr<RobDog_Ctrl_Dev> robdogCtrlDev = nullptr;
    void setRobotMove(int32_t x, int32_t y, int32_t z);
    void setRobotView(int32_t yaw, int32_t pitch, int32_t roll);

    rclcpp::Logger get_logger() const { return Node::get_logger(); }
    rclcpp::Clock::SharedPtr get_clock() { return Node::get_clock(); }
  private:
    // void velCmdCallback(const std::shared_ptr<geometry_msgs::msg::Twist>& msg); 
    void velCmdCallback(const geometry_msgs::msg::Twist::SharedPtr msg);
    void MoveSkillscallback(const homi_speech_interface::msg::RobdogAction::SharedPtr msg);
    void continueMovecallback(const homi_speech_interface::msg::ContinueMove::SharedPtr msg);
    void executeAction(const std::string& MoveSkill) ;
    void startStandUpToExcute(std::function<void()> callback, std::chrono::seconds timeout);
    void startRotation(std::function<void()> callback, std::chrono::seconds timeout,const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg);
    void setRobManuModel();
    void send_peripherals_ctrl_request();
    void send_peripherals_status_request();
    void handle_peripherals_monitor_message(const std_msgs::msg::String::SharedPtr msg);
    
  protected:
    int port_;
    int remote_port_;
    std::string devip_str_;
    
    int sockfd_;
    int sockfd_customized;//与外设控制进程通信的socket
    // int sockfd_light;//与燈光外设控制进程通信的socket
    in_addr devip_;
    sockaddr_in client_addr;
    sockaddr_in light_addr;

  private:
    std::unordered_map<int, std::string> state_path_map_;
    std::map<int, std::string> state_paths_param_;  
    std::mutex mtx_;
    std::condition_variable cv_;
    
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr velCmd_;
    rclcpp::Subscription<homi_speech_interface::msg::NewUdpConnect>::SharedPtr UdpConnectCmd_;
    rclcpp::Subscription<homi_speech_interface::msg::RobdogAction>::SharedPtr actionCmd_;
    rclcpp::Subscription<homi_speech_interface::msg::ContinueMove>::SharedPtr continueMoveCmd_;
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::Subscription<homi_speech_interface::msg::Wakeup>::SharedPtr ifly_sub_;
    rclcpp::Subscription<homi_speech_interface::msg::ProprietySet>::SharedPtr userDefinedSub_;
    rclcpp::Publisher<homi_speech_interface::msg::ProprietySet>::SharedPtr statusReportPub;
    rclcpp::Publisher<homi_speech_interface::msg::RobdogState>::SharedPtr robotStatusPub_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr robotExpressionPub_;
    rclcpp::Publisher<homi_speech_interface::msg::Wakeup>::SharedPtr wakeupPub_;
    rclcpp::TimerBase::SharedPtr robActionSuspendTimer_; // 间隔固定的时延
    rclcpp::TimerBase::SharedPtr robCancelHandCode_; // 间隔固定的时延

    rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedPtr peripherals_ctrl_client_;
    rclcpp::Client<homi_speech_interface::srv::PeripheralsStatus>::SharedPtr peripherals_status_client_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr peripherals_monitor_sub_;
    using Clock = std::chrono::steady_clock;  
    std::chrono::time_point<Clock> last_vel_steady_time_;  
};
