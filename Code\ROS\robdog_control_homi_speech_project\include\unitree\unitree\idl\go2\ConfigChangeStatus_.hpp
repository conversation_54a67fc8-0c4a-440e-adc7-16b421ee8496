/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: ConfigChangeStatus_.idl
  Source: ConfigChangeStatus_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_CONFIGCHANGESTATUS__HPP
#define DDSCXX_UNITREE_IDL_GO2_CONFIGCHANGESTATUS__HPP

#include <string>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class ConfigChangeStatus_
{
private:
 std::string name_;
 std::string content_;

public:
  ConfigChangeStatus_() = default;

  explicit ConfigChangeStatus_(
    const std::string& name,
    const std::string& content) :
    name_(name),
    content_(content) { }

  const std::string& name() const { return this->name_; }
  std::string& name() { return this->name_; }
  void name(const std::string& _val_) { this->name_ = _val_; }
  void name(std::string&& _val_) { this->name_ = _val_; }
  const std::string& content() const { return this->content_; }
  std::string& content() { return this->content_; }
  void content(const std::string& _val_) { this->content_ = _val_; }
  void content(std::string&& _val_) { this->content_ = _val_; }

  bool operator==(const ConfigChangeStatus_& _other) const
  {
    (void) _other;
    return name_ == _other.name_ &&
      content_ == _other.content_;
  }

  bool operator!=(const ConfigChangeStatus_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::ConfigChangeStatus_>::getTypeName()
{
  return "unitree_go::msg::dds_::ConfigChangeStatus_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::ConfigChangeStatus_>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::ConfigChangeStatus_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::ConfigChangeStatus_>::type_map_blob_sz() { return 266; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::ConfigChangeStatus_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::ConfigChangeStatus_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x4c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0x8b,  0xab,  0x72,  0x41,  0x14,  0x34,  0x88, 
 0x06,  0xaa,  0x92,  0xd1,  0x55,  0xf0,  0xe9,  0x00,  0x34,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0xb0,  0x68,  0x93,  0x1c, 
 0x0c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0x9a,  0x03,  0x64,  0xb9, 
 0x8e,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0xb5,  0xf8,  0x3d,  0x36,  0xf2,  0x55,  0x84, 
 0x27,  0x72,  0xfc,  0x68,  0x2f,  0x66,  0xf5,  0x00,  0x76,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x33,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x2b,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73, 
 0x5f,  0x3a,  0x3a,  0x43,  0x6f,  0x6e,  0x66,  0x69,  0x67,  0x43,  0x68,  0x61,  0x6e,  0x67,  0x65,  0x53, 
 0x74,  0x61,  0x74,  0x75,  0x73,  0x5f,  0x00,  0x00,  0x36,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x13,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x70,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x6e,  0x61,  0x6d,  0x65,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x70,  0x00,  0x08,  0x00,  0x00,  0x00,  0x63,  0x6f,  0x6e,  0x74,  0x65,  0x6e,  0x74,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0xb5,  0xf8,  0x3d, 
 0x36,  0xf2,  0x55,  0x84,  0x27,  0x72,  0xfc,  0x68,  0x2f,  0x66,  0xf5,  0xf1,  0x8b,  0xab,  0x72,  0x41, 
 0x14,  0x34,  0x88,  0x06,  0xaa,  0x92,  0xd1,  0x55,  0xf0,  0xe9, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::ConfigChangeStatus_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x8b,  0xab,  0x72,  0x41,  0x14,  0x34,  0x88,  0x06,  0xaa,  0x92,  0xd1, 
 0x55,  0xf0,  0xe9,  0x00,  0x38,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0xb5,  0xf8,  0x3d,  0x36,  0xf2,  0x55,  0x84,  0x27,  0x72,  0xfc,  0x68, 
 0x2f,  0x66,  0xf5,  0x00,  0x7a,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::ConfigChangeStatus_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::ConfigChangeStatus_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::ConfigChangeStatus_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::ConfigChangeStatus_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::ConfigChangeStatus_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.name(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write_string(streamer, instance.content(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::ConfigChangeStatus_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::ConfigChangeStatus_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::ConfigChangeStatus_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.name(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read_string(streamer, instance.content(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::ConfigChangeStatus_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::ConfigChangeStatus_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::ConfigChangeStatus_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.name(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move_string(streamer, instance.content(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::ConfigChangeStatus_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::ConfigChangeStatus_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::ConfigChangeStatus_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.name(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max_string(streamer, instance.content(), 0))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::ConfigChangeStatus_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::ConfigChangeStatus_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_CONFIGCHANGESTATUS__HPP
