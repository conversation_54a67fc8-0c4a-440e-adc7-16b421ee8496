import launch
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, IncludeLaunchDescription, TimerAction
from launch.substitutions import LaunchConfiguration
from launch_ros.substitutions import FindPackageShare
from launch_ros.actions import Node
import os
from ament_index_python.packages import get_package_share_directory
from launch.launch_description_sources import PythonLaunchDescriptionSource



def generate_launch_description():
    config_share_dir = get_package_share_directory('launch_package')
    
    andlink = Node(
        package="andlink2",
        executable="andlink_node",
        #name='andlink_node',
        # output='log',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
    )
    ble = Node(
        package="ble",
        executable="ble_node",
        #name='andlink_node',
        # output='log',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
        # respawn=True
    )
    a2dp = Node(
        package="a2dp",
        executable="a2dp_node",
        #name='andlink_node',
        output='log',
        respawn=True
    )

    network = Node(
        package="network_pkg",
        executable="network_node",
        #name='andlink_node',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
        # output='log',
        # respawn=True
    )

    homi_speech_yaml = IncludeLaunchDescription(        # 包含指定路径下的另外一个launch文件
        PythonLaunchDescriptionSource([os.path.join(
        get_package_share_directory('homi_speech'), 'launch'),
        '/speech_helper_near.launch.py'])
    )
    ap_start_pkg = Node(
        package="ap_start_pkg",
        executable="ap_start_node",
        output='log',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
    )

    return LaunchDescription([
    #     andlink,
    #     ble,
    #   homi_speech_yaml,
    # network,
    ap_start_pkg,
    #   robdog_control
        # a2dp
    ])
