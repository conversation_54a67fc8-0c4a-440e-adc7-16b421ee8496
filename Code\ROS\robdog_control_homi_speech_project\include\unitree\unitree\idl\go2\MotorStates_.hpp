/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: MotorStates_.idl
  Source: MotorStates_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_GO2_MOTORSTATES__HPP
#define DDSCXX_UNITREE_IDL_GO2_MOTORSTATES__HPP

#include "unitree/idl/go2/MotorState_.hpp"

#include <vector>

namespace unitree_go
{
namespace msg
{
namespace dds_
{
class MotorStates_
{
private:
 std::vector<::unitree_go::msg::dds_::MotorState_> states_;

public:
  MotorStates_() = default;

  explicit MotorStates_(
    const std::vector<::unitree_go::msg::dds_::MotorState_>& states) :
    states_(states) { }

  const std::vector<::unitree_go::msg::dds_::MotorState_>& states() const { return this->states_; }
  std::vector<::unitree_go::msg::dds_::MotorState_>& states() { return this->states_; }
  void states(const std::vector<::unitree_go::msg::dds_::MotorState_>& _val_) { this->states_ = _val_; }
  void states(std::vector<::unitree_go::msg::dds_::MotorState_>&& _val_) { this->states_ = _val_; }

  bool operator==(const MotorStates_& _other) const
  {
    (void) _other;
    return states_ == _other.states_;
  }

  bool operator!=(const MotorStates_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_go::msg::dds_::MotorStates_>::getTypeName()
{
  return "unitree_go::msg::dds_::MotorStates_";
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::MotorStates_>::isSelfContained()
{
  return false;
}

template <> constexpr bool TopicTraits<::unitree_go::msg::dds_::MotorStates_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::MotorStates_>::type_map_blob_sz() { return 888; }
template<> constexpr unsigned int TopicTraits<::unitree_go::msg::dds_::MotorStates_>::type_info_blob_sz() { return 148; }
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::MotorStates_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x32,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0xf1,  0x13,  0xa0,  0x6c,  0x7a,  0xa8,  0x80,  0x82, 
 0x61,  0x7d,  0xd1,  0xe8,  0xed,  0xe0,  0x45,  0x00,  0x36,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x1e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf1,  0x01,  0x00,  0x00,  0xf1, 
 0x42,  0xa2,  0x28,  0x8b,  0x79,  0x8a,  0xf8,  0xdb,  0x05,  0xe1,  0x27,  0x36,  0xb1,  0xbe,  0x34,  0xd9, 
 0x55,  0xa0,  0xf1,  0x42,  0xa2,  0x28,  0x8b,  0x79,  0x8a,  0xf8,  0xdb,  0x05,  0xe1,  0x27,  0x36,  0xb1, 
 0xbe,  0x00,  0x00,  0x00,  0xce,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0xbe,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x76,  0x94,  0xf4,  0xa6,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x47,  0xbc,  0xdc,  0xd7,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0xe9,  0x16,  0x89,  0x09,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x8a,  0xf7,  0xae,  0xdf,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x6e,  0x96,  0x3d,  0x84,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x30,  0x50,  0xb6,  0xd9,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x4a,  0xe0,  0x48,  0xae,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xee,  0xf4,  0x38,  0xf7,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x1c,  0x9a,  0x44,  0xeb,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0x00,  0xf6,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0xf2,  0xb9,  0x0a,  0xca,  0x92,  0x86,  0xab,  0x4e,  0x3e,  0xae,  0x76,  0x38,  0xef,  0x5c,  0xa9,  0x00, 
 0x69,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x2c,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x24,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4d,  0x6f,  0x74,  0x6f,  0x72, 
 0x53,  0x74,  0x61,  0x74,  0x65,  0x73,  0x5f,  0x00,  0x31,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x80,  0xf2,  0x01,  0x00,  0x00,  0xf2, 
 0xd2,  0xbc,  0xfb,  0x97,  0xbe,  0x37,  0xa8,  0xbc,  0x9c,  0x41,  0xe5,  0x0b,  0xa1,  0xc9,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x73,  0x74,  0x61,  0x74,  0x65,  0x73,  0x00,  0x00,  0x00,  0xf2,  0xd2,  0xbc, 
 0xfb,  0x97,  0xbe,  0x37,  0xa8,  0xbc,  0x9c,  0x41,  0xe5,  0x0b,  0xa1,  0xc9,  0x62,  0x01,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x2b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x67,  0x6f,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a, 
 0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4d,  0x6f,  0x74,  0x6f,  0x72,  0x53,  0x74,  0x61,  0x74, 
 0x65,  0x5f,  0x00,  0x00,  0x2a,  0x01,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x05,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65, 
 0x00,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x71,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x12,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x64,  0x64,  0x71,  0x00,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x08,  0x00,  0x00,  0x00,  0x74,  0x61,  0x75,  0x5f,  0x65,  0x73,  0x74,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x71,  0x5f,  0x72,  0x61,  0x77,  0x00,  0x00,  0x00,  0x15,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x07,  0x00,  0x00,  0x00,  0x64,  0x71,  0x5f,  0x72, 
 0x61,  0x77,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x08,  0x00,  0x00,  0x00,  0x64,  0x64,  0x71,  0x5f,  0x72,  0x61,  0x77,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x1a,  0x00,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00, 
 0x0c,  0x00,  0x00,  0x00,  0x74,  0x65,  0x6d,  0x70,  0x65,  0x72,  0x61,  0x74,  0x75,  0x72,  0x65,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x6c,  0x6f,  0x73,  0x74,  0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00, 
 0x0a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x02,  0x07,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65,  0x72,  0x76,  0x65,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x40,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0xf2,  0xb9,  0x0a,  0xca, 
 0x92,  0x86,  0xab,  0x4e,  0x3e,  0xae,  0x76,  0x38,  0xef,  0x5c,  0xa9,  0xf1,  0x13,  0xa0,  0x6c,  0x7a, 
 0xa8,  0x80,  0x82,  0x61,  0x7d,  0xd1,  0xe8,  0xed,  0xe0,  0x45,  0xf2,  0xd2,  0xbc,  0xfb,  0x97,  0xbe, 
 0x37,  0xa8,  0xbc,  0x9c,  0x41,  0xe5,  0x0b,  0xa1,  0xc9,  0xf1,  0x42,  0xa2,  0x28,  0x8b,  0x79,  0x8a, 
 0xf8,  0xdb,  0x05,  0xe1,  0x27,  0x36,  0xb1,  0xbe, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_go::msg::dds_::MotorStates_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x90,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x40,  0x00,  0x00,  0x00,  0x3c,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x13,  0xa0,  0x6c,  0x7a,  0xa8,  0x80,  0x82,  0x61,  0x7d,  0xd1,  0xe8, 
 0xed,  0xe0,  0x45,  0x00,  0x3a,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x42,  0xa2,  0x28,  0x8b,  0x79,  0x8a,  0xf8, 
 0xdb,  0x05,  0xe1,  0x27,  0x36,  0xb1,  0xbe,  0x00,  0xd2,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40, 
 0x40,  0x00,  0x00,  0x00,  0x3c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xb9,  0x0a,  0xca, 
 0x92,  0x86,  0xab,  0x4e,  0x3e,  0xae,  0x76,  0x38,  0xef,  0x5c,  0xa9,  0x00,  0x6d,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0xd2,  0xbc,  0xfb,  0x97,  0xbe,  0x37,  0xa8,  0xbc,  0x9c,  0x41,  0xe5,  0x0b,  0xa1,  0xc9,  0x00, 
 0x66,  0x01,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_go::msg::dds_::MotorStates_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_go::msg::dds_::MotorStates_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_go::msg::dds_::MotorStates_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_go::msg::dds_::MotorStates_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_go::msg::dds_::MotorStates_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.states().size());
      if (!write(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!write(streamer, instance.states()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_go::msg::dds_::MotorStates_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::MotorStates_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_go::msg::dds_::MotorStates_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.states().size());
      if (!read(streamer, se_1))
        return false;
      instance.states().resize(se_1);
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!read(streamer, instance.states()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_go::msg::dds_::MotorStates_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::MotorStates_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_go::msg::dds_::MotorStates_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = uint32_t(instance.states().size());
      if (!move(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!move(streamer, instance.states()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_go::msg::dds_::MotorStates_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::MotorStates_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_go::msg::dds_::MotorStates_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(false, false))
        return false;
      {
      uint32_t se_1 = 0;
      if (!max(streamer, se_1))
        return false;
      for (uint32_t i_1 = 0; i_1 < se_1; i_1++) {
      if (!max(streamer, instance.states()[i_1], prop))
        return false;
      }  //i_1
      }  //end sequence 1
      if (!streamer.finish_consecutive())
        return false;
      streamer.position(SIZE_MAX);
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_go::msg::dds_::MotorStates_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_go::msg::dds_::MotorStates_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_GO2_MOTORSTATES__HPP
