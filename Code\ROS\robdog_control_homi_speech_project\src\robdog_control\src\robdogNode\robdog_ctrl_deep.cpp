#include "robdog_ctrl_node.h"

CommandHead command;   
Command data_cmd;   

int32_t RobDog_Ctrl_Deep::robdogCtrl_Init(void* ctrl_ptr_)
{
    if(nullptr == ctrl_ptr_) 
    {
        return ROBDOGCTRL_ERROR_FAILED;
    }

    node_ctrl_ = (RobdogCtrlNode *)ctrl_ptr_;

    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Deep::deepSendPacket(uint8_t *pkt, size_t packet_size)
{
    node_ctrl_->sendPacket(pkt, packet_size);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Deep::robdogCtrl_ContinueMove(CtrlMoveData *pMoveData)
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);
    if(pMoveData->event == "robot_move")
    {
        if(pMoveData->x != 0)
        {
            command.code = 0x21010130;
            command.type = 0;
            command.size = (pMoveData->x > 0) ? 15000 : -15000;  
            deepSendPacket((uint8_t*)&command, sizeof(CommandHead));
        }
        if(pMoveData->y != 0)
        {
            command.code = 0x21010131;
            command.type = 0;
            command.size =  (pMoveData->y > 0) ? -20000 : 20000;  
            deepSendPacket((uint8_t*)&command, sizeof(CommandHead));
        }
    }
    else if(pMoveData->event == "robot_view")
    {
        if(pMoveData->yaw != 0)
        {
            command.code = 0x21010135;
            command.type = 0;
            command.size = (pMoveData->yaw > 0) ? 15000 : -15000;
            deepSendPacket((uint8_t*)&command, sizeof(command));
        }
        if(pMoveData->pitch != 0)
        {
            command.code = 0x21010102; // 移动模式下调整俯仰角(死区是20000)
            command.type = 0;
            command.size = (pMoveData->pitch > 0) ? 25000 : -25000;
            deepSendPacket((uint8_t*)&command, sizeof(command));
        }
    }
    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Deep::robdogCtrl_Move(double x, double y, double yaw)
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    data_cmd.head.code = 0x140;
    data_cmd.head.type = 1;
    data_cmd.head.size = sizeof(double);
    memcpy(data_cmd.data, &x, sizeof(double));
    deepSendPacket((uint8_t*)&data_cmd, sizeof(CommandHead) + sizeof(double)); 

    data_cmd.head.code = 0x145;
    memcpy(data_cmd.data, &y, sizeof(double));
    deepSendPacket((uint8_t*)&data_cmd, sizeof(CommandHead) + sizeof(double));

    data_cmd.head.code = 0x141;
    memcpy(data_cmd.data, &yaw, sizeof(double));
    deepSendPacket((uint8_t*)&data_cmd, sizeof(CommandHead) + sizeof(double));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Deep::robdogCtrl_StopMove()
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = 0x21010135;
    command.type = 0;
    command.size = 0;
    deepSendPacket((uint8_t*)&command, sizeof(command)); 
    command.code = 0x21010102;
    command.type = 0;
    command.size = 0;
    deepSendPacket((uint8_t*)&command, sizeof(command));
    command.code = 0x21010130;
    command.type = 0;
    command.size = 0; 
    deepSendPacket((uint8_t*)&command, sizeof(CommandHead));
    command.code = 0x21010131;
    command.type = 0;
    command.size = 0;
    deepSendPacket((uint8_t*)&command, sizeof(CommandHead));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 起立/趴下,               0x21010202
int32_t RobDog_Ctrl_Deep::robdogCtrl_StandUp()              	
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = 0x21010202;
    command.type = 0;
    command.size = 0;

    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 起立/趴下,               0x21010202
int32_t RobDog_Ctrl_Deep::robdogCtrl_GetDown()              
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = GET_DOWN_CODE;
    command.type = 0;
    command.size = 0;

    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 坐下,                   0x21010506
int32_t RobDog_Ctrl_Deep::robdogCtrl_Sit()       	        
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = 0x21010506;
    command.type = 0;
    command.size = 0;  // 指令值
    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Deep::robdogCtrl_ChangeGait(RobdogCtrlGait gait)           	    
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    switch(gait)
    {
        case ROBDOGCTRL_GAIT_WALK:
        {
            command.code = 0x21010300; // 平地低速步态
            break;
        }
        case ROBDOGCTRL_GAIT_RUN:
        {
            command.code = 0x21010303; // 平地高速步态
            break;
        }
        case ROBDOGCTRL_GAIT_STAIRCLIMB:
        {
            command.code = 0x21010407; // 高踏步越障步态
            break;
        }
        case ROBDOGCTRL_GAIT_CLIMB:
        {
            command.code = 0x21010402; // 抓地越障步态
            break;
        }
        case ROBDOGCTRL_GAIT_OBSTACLE_CROSS:
        {
            command.code = 0x21010529;
            break;
        }
        case ROBDOGCTRL_GAIT_FLATGROUND:
        {
            command.code = 0x2101052a;
            break;
        }
        case ROBDOGCTRL_GAIT_EXIT:
        {
            command.code = 0x2101052b;
            break;
        }
        default:
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(),  "Invalid Gait!");
            return ROBDOGCTRL_ERROR_FAILED;
        }
    }

    command.type = 0;
    command.size = 0;
    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}


int32_t RobDog_Ctrl_Deep::robdogCtrl_Locomotion(RobdogCtrlMotion motion)
{
    switch(motion)
    {
        case ROBDOGCTRL_MOTION_SHAKEBODY:
        {
            command.code = 0x21010513;  //SHAKE_BODY_CODE
            break;
        }
        case ROBDOGCTRL_MOTION_NEWYEARCALL:
        {
            command.code = 0x21010512;
            break;
        }
        case ROBDOGCTRL_MOTION_CHESTOUT:
        {
            command.code = 0x21010511;
            break;
        }
        case ROBDOGCTRL_MOTION_STRETCH:
        {
            command.code = 0x21010510;
            break;
        }
        case ROBDOGCTRL_MOTION_JUMPFORWARD:
        {
            command.code = JUMP_FORWARD_CODE;
            break;
        }
        case ROBDOGCTRL_MOTION_FASTSHAKEBODY:
        {
            command.code = 0x21010509;
            break;
        }
        case ROBDOGCTRL_MOTION_TWISTASS:
        {
            command.code = TWIST_ASS_CODE;
            break;
        }
        case ROBDOGCTRL_MOTION_HELLO:
        {
            command.code = GREETING_CODE;
            break;
        }
        case ROBDOGCTRL_MOTION_FINGERHEART:
        {
            command.code = 0x21010504;
            break;
        }
        case ROBDOGCTRL_MOTION_BACKFLIP:
        {
            command.code = BACKFLIP_CODE;
            break;
        }
        case ROBDOGCTRL_MOTION_DANCE:
        {
            command.code = DANCE_CODE;
            break;
        }
        case ROBDOGCTRL_MOTION_TWISTJUMP:
        {
            command.code = TWIST_JUMP_CODE;
            break;
        }
        case ROBDOGCTRL_MOTION_TURNOVER:
        {
            command.code = TURN_OVER_CODE;
            break;
        }
        case ROBDOGCTRL_MOTION_TWIST:
        {
            command.code = TWIST_BODY_CODE;
            break;
        }
        default:
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(),  "Invalid Gait!");
            return ROBDOGCTRL_ERROR_FAILED;
        }
    }

    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.type = 0;
    command.size = 0;

    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 手动模式,               0x21010C02
int32_t RobDog_Ctrl_Deep::robdogCtrl_ManualMode()          	
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = 0x21010C02; // 手动模式
    command.type = 0;
    command.size = 0;
    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 自主模式,               0x21010C03
int32_t RobDog_Ctrl_Deep::robdogCtrl_AutoMode()       	    
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = 0x21010C03; //自主模式
    command.type = 0;
    command.size = 0;
    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 回零,                   0x21010C05
int32_t RobDog_Ctrl_Deep::robdogCtrl_ResetZero()         	
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = 0x21010C05;
    command.type = 0;
    command.size = 2;  // 指令值
    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 移动模式,               0x21010D06
int32_t RobDog_Ctrl_Deep::robdogCtrl_MoveMode()        	    
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = 0x21010D06; // 移动模式
    command.type = 0;
    command.size = 0;
    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 语音指令（起立/趴下）,   0x21010C0A
int32_t RobDog_Ctrl_Deep::robdogCtrl_VoiceStand(int32_t cmd)        	
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    CommandHead command_handpos;  
    command_handpos.code = 0x21010C0A;
    command_handpos.type = 0;
    command_handpos.size = cmd;  
    deepSendPacket((uint8_t*)&command_handpos, sizeof(command_handpos));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 开启避障功能,           0x21012109
int32_t RobDog_Ctrl_Deep::robdogCtrl_AvoidOpen()           
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 关闭避障功能,           0x21012109
int32_t RobDog_Ctrl_Deep::robdogCtrl_AvoidClose()           
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = 0x21012109; // 关闭停障
    command.type = 0;
    command.size = 0;
    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 软急停,                 0x21020C0E
int32_t RobDog_Ctrl_Deep::robdogCtrl_EmergencyStop()    	
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command.code = 0x21020C0E;
    command.type = 0;
    command.size = 2;  // 指令值
    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 心跳,                   0x21040001
int32_t RobDog_Ctrl_Deep::robdogCtrl_HeartBeat()        	
{
    // RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    struct CommandHead command;
    command.code = 0x21040001;
    command.type = 0;
    command.size = 0;
    deepSendPacket(reinterpret_cast<uint8_t*>(&command), sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人温度信息,          0x21040002
int32_t RobDog_Ctrl_Deep::robdogCtrl_Temperature()       	
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    // CommandHead command_head(0x0901, 0, 1);
    data_cmd.head.code = 0x21010E03;
    data_cmd.head.type = 0;
    data_cmd.head.size = 0;
    deepSendPacket((uint8_t*)&data_cmd, sizeof(CommandHead));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

//                         0x31010D07
int32_t RobDog_Ctrl_Deep::robdogCtrl_Position(float x, float y,float radian)
{
    Command cmd;
    float position[3]={x,y,radian};

    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    memset(&cmd,0,sizeof(Command));
    cmd.head.type = 1;
    cmd.head.size = sizeof(int)+3*sizeof(float);
    cmd.head.code = POSITION_CMD;

    memset(cmd.data, 0, sizeof(cmd.data));
    cmd.data[0] = 1; 
    memcpy(&cmd.data[1], position, sizeof(position));

    deepSendPacket((uint8_t*)&cmd, sizeof(CommandHead) + sizeof(int)+3*sizeof(float)); 

    return ROBDOGCTRL_ERROR_SUCCESS;
}

//                         0x122
int32_t RobDog_Ctrl_Deep::robdogCtrl_PositionAngVel()       
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    CommandHead cmd;
    memset(&cmd,0,sizeof(CommandHead));
    cmd.code = POSITION_ANG_VEL;
    cmd.type = 0;
    cmd.size = 790;
    deepSendPacket((uint8_t*)&cmd, sizeof(CommandHead));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 用户定义,                  0x00000160
int32_t RobDog_Ctrl_Deep::robdogCtrl_UserDefined(int32_t cmd)          	
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    command={0,0,0};
    command.code = cmd; //指令码
    command.size = 0;// 指令值
    command.type = 0; // 指令类型,复杂/简单
    deepSendPacket((uint8_t*)&command, sizeof(command));

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人状态信息,           0x0901
int32_t RobDog_Ctrl_Deep::robdogCtrl_State()             	
{
    RCLCPP_INFO(node_ctrl_->get_logger(),  "Enter Function: %s", __func__);

    // CommandHead command_head(0x0901, 0, 1);
    data_cmd.head.code = 0x0901;
    data_cmd.head.type = 1;
    data_cmd.head.size = 0;
    deepSendPacket((uint8_t*)&data_cmd, sizeof(CommandHead));

    return ROBDOGCTRL_ERROR_SUCCESS;
}