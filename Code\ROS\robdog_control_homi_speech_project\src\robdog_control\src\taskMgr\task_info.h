/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:05:05
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-01 10:26:34
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_config.h
 * @Description: 任务类型数据类，包括任务主类型，任务子类型（可以用宏定义去定义主子类型），任务状态, 任务优先级
 */
#pragma once
#include <homi_com/singleton.hpp>
#include <homi_com/context.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>

using namespace std;

class TaskInfo
{
public:
    TaskInfo();
    ~TaskInfo();

public:
    PropertyBuilderByName(std::string, UUid, "");           
    PropertyBuilderByName(std::string, TaskType, "");                                   //任务UUid
    PropertyBuilderByName(std::string, SubTaskType, "");                                //任务UUid
    PropertyBuilderByName(HomiTaskStatus, TaskStatus, ROBOT_TASK_STATUS_STARTING);      //任务状态
    PropertyBuilderByName(bool, ReExecute, false);                                      //任务UUid
};

#define TaskInfoPtr std::shared_ptr<TaskInfo>