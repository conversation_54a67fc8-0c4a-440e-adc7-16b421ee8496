import os

from ament_index_python.packages import get_package_share_directory  # 查询功能包路径的方法

from launch import LaunchDescription                 # launch文件的描述类
from launch.actions import IncludeLaunchDescription  # 节点启动的描述类
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.actions import GroupAction               # launch文件中的执行动作
from launch_ros.actions import PushRosNamespace      # ROS命名空间配置
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
import yaml
import re
from configparser import ConfigParser
import json

def get_version_list_path():
    # FIXME: should get ros2 running path to get version.list path
    version_list_path = os.path.join("/usr/bin/cmcc_robot/install/version.list")
    return version_list_path

def read_robot_body_version():
    robot_body_version = None
    version_list_path = get_version_list_path()
    try:
        with open(version_list_path, 'r') as file:
            for line in file:
                if 'robot_body' in line:
                    parts = line.split(':')
                    if len(parts) > 2:
                        version = parts[2].strip()
                        robot_body_version = version
                        break
    except FileNotFoundError:
        print(f"version.list file not found at {version_list_path}")
    except Exception as e:
        print(f"An error occurred: {e}")
    return robot_body_version

def replace_yaml_info(param):
    try:
        skd = json.loads(param["sdk_init_param"])
        conf = ConfigParser() # 创建ConfigParser对象
        conf.read('/etc/cmcc_robot/cmcc_dev.ini') # 读取INI文件
        skd["config"]["sn"]=f"{conf['factory']['devSn']}"
        skd["config"]["macId"]=f"{conf['factory']['macAddr']}"
        skd["config"]["deviceId"]=skd["config"]["sn"]
        skd["config"]["deviceType"]=f"{conf['factory']['devType']}"
        skd['config']['cmei'] =  f"{conf['factory']['devCmei']}"
        skd["config"]["snPwd"]=f"{conf['factory']['OVDMediaEncPassword']}"
        version = read_robot_body_version()
        if version:
            print(f"robot_body version: {version}")
        else:
            version="Unknow"
        skd["config"]["softwareVersion"]=version
        skd["config"]["sdkVersion"]=skd["config"]["softwareVersion"]
        param["sdk_init_param"] = json.dumps(skd)
    except:
        print("replace sdk_init_param fail")
def replace_package_paths(params):
    if isinstance(params, dict):
        for key, value in params.items():
            params[key] = replace_package_paths(value)
    elif isinstance(params, str):
        matches = re.findall(r'\$\(\s*find-pkg-share\s+(\w+)\s*\)', params)
        for match in matches:
            try:
                package_path = FindPackageShare(package=match).find(match)
                params = params.replace(f"$(find-pkg-share {match})", package_path)
            except:
                print(f"Package {match} not found")
    return params
def replace_package_paths_from_files(files):
    params = []
    for file in files:
        with open(file, 'r') as f:
            param = yaml.safe_load(f)
            if(file.endswith("param.yaml")):
                replace_yaml_info(param)
        param = replace_package_paths(param)
        params.append(param)
    return params

def generate_launch_description():
    config_files = ['param.yaml','offline_asr.yaml','speech_res.yaml','capture.yaml','wakeup.yaml']
    config_files_path = [os.path.join(
    get_package_share_directory('homi_speech'),
    'launch','config',
    file
    ) for file in config_files]
    package_name = 'homi_speech'
    name_space = 'homi_speech'

    return LaunchDescription([
        Node(
            package=package_name,
            executable='capture',
            name='capture',
            namespace=name_space,
            parameters=replace_package_paths_from_files(config_files_path)
        ),
        Node(
            package=package_name,
            executable='wakeup',
            name='wakeup',
            namespace=name_space,
            parameters=replace_package_paths_from_files(config_files_path)
        ),
        Node(
            package=package_name,
            executable='helper',
            name='helper',
            namespace=name_space,
            parameters=replace_package_paths_from_files(config_files_path)
        ),
        Node(
            package=package_name,
            executable='speech_core',
            name='speech_core',
            namespace=name_space,
            parameters=replace_package_paths_from_files(config_files_path)
        )
    ])
