/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: LowCmd_.idl
  Source: LowCmd_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_HG_LOWCMD__HPP
#define DDSCXX_UNITREE_IDL_HG_LOWCMD__HPP

#include "unitree/idl/hg/MotorCmd_.hpp"

#include <cstdint>
#include <array>

namespace unitree_hg
{
namespace msg
{
namespace dds_
{
class LowCmd_
{
private:
 uint8_t mode_pr_ = 0;
 uint8_t mode_machine_ = 0;
 std::array<::unitree_hg::msg::dds_::MotorCmd_, 35> motor_cmd_ = { };
 std::array<uint32_t, 4> reserve_ = { };
 uint32_t crc_ = 0;

public:
  LowCmd_() = default;

  explicit LowCmd_(
    uint8_t mode_pr,
    uint8_t mode_machine,
    const std::array<::unitree_hg::msg::dds_::MotorCmd_, 35>& motor_cmd,
    const std::array<uint32_t, 4>& reserve,
    uint32_t crc) :
    mode_pr_(mode_pr),
    mode_machine_(mode_machine),
    motor_cmd_(motor_cmd),
    reserve_(reserve),
    crc_(crc) { }

  uint8_t mode_pr() const { return this->mode_pr_; }
  uint8_t& mode_pr() { return this->mode_pr_; }
  void mode_pr(uint8_t _val_) { this->mode_pr_ = _val_; }
  uint8_t mode_machine() const { return this->mode_machine_; }
  uint8_t& mode_machine() { return this->mode_machine_; }
  void mode_machine(uint8_t _val_) { this->mode_machine_ = _val_; }
  const std::array<::unitree_hg::msg::dds_::MotorCmd_, 35>& motor_cmd() const { return this->motor_cmd_; }
  std::array<::unitree_hg::msg::dds_::MotorCmd_, 35>& motor_cmd() { return this->motor_cmd_; }
  void motor_cmd(const std::array<::unitree_hg::msg::dds_::MotorCmd_, 35>& _val_) { this->motor_cmd_ = _val_; }
  void motor_cmd(std::array<::unitree_hg::msg::dds_::MotorCmd_, 35>&& _val_) { this->motor_cmd_ = _val_; }
  const std::array<uint32_t, 4>& reserve() const { return this->reserve_; }
  std::array<uint32_t, 4>& reserve() { return this->reserve_; }
  void reserve(const std::array<uint32_t, 4>& _val_) { this->reserve_ = _val_; }
  void reserve(std::array<uint32_t, 4>&& _val_) { this->reserve_ = _val_; }
  uint32_t crc() const { return this->crc_; }
  uint32_t& crc() { return this->crc_; }
  void crc(uint32_t _val_) { this->crc_ = _val_; }

  bool operator==(const LowCmd_& _other) const
  {
    (void) _other;
    return mode_pr_ == _other.mode_pr_ &&
      mode_machine_ == _other.mode_machine_ &&
      motor_cmd_ == _other.motor_cmd_ &&
      reserve_ == _other.reserve_ &&
      crc_ == _other.crc_;
  }

  bool operator!=(const LowCmd_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_hg::msg::dds_::LowCmd_>::getTypeName()
{
  return "unitree_hg::msg::dds_::LowCmd_";
}

template <> constexpr bool TopicTraits<::unitree_hg::msg::dds_::LowCmd_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::LowCmd_>::type_map_blob_sz() { return 892; }
template<> constexpr unsigned int TopicTraits<::unitree_hg::msg::dds_::LowCmd_>::type_info_blob_sz() { return 148; }
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::LowCmd_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x37,  0x01,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0xf1,  0x65,  0x69,  0xfc,  0x15,  0xf0,  0xd4,  0x7a, 
 0xa8,  0x60,  0x0b,  0x9e,  0x91,  0xfb,  0xbc,  0x00,  0x87,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x77,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xb6,  0x9f,  0x36,  0x51,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0xed,  0xb8,  0xc4,  0x0a,  0x00, 
 0x24,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf1,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x23,  0xf1,  0x82,  0x7f,  0x55,  0xde,  0x9b,  0x9d,  0xe3,  0x8c,  0x62,  0xba, 
 0xcd,  0x1e,  0x56,  0xc9,  0x5b,  0xcf,  0x19,  0xa4,  0x16,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x04,  0x07,  0x9c,  0x3b, 
 0x62,  0x94,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0xf5, 
 0xad,  0x59,  0xc5,  0xf1,  0x82,  0x7f,  0x55,  0xde,  0x9b,  0x9d,  0xe3,  0x8c,  0x62,  0xba,  0xcd,  0x1e, 
 0x56,  0xc9,  0x00,  0x00,  0x83,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x73,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x15,  0xd6,  0x17,  0x12,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x76,  0x94,  0xf4,  0xa6,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x47,  0xbc,  0xdc,  0xd7,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x45,  0x80,  0xc2,  0x74,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x26,  0xb5,  0x68,  0xe4,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x87,  0x22,  0x16,  0x52,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x9c,  0x3b,  0x62,  0x94,  0x00,  0xf6,  0x01,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0xf2,  0x42,  0xb0,  0x28,  0x3a,  0xa7,  0xe1,  0xbb,  0x59,  0x2e,  0xc8,  0x98, 
 0xa0,  0xdb,  0xed,  0x00,  0xe6,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x27,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x1f,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x68,  0x67,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4c, 
 0x6f,  0x77,  0x43,  0x6d,  0x64,  0x5f,  0x00,  0x00,  0xb2,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x16,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x08,  0x00,  0x00,  0x00, 
 0x6d,  0x6f,  0x64,  0x65,  0x5f,  0x70,  0x72,  0x00,  0x00,  0x00,  0x00,  0x00,  0x1b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x0d,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x64,  0x65, 
 0x5f,  0x6d,  0x61,  0x63,  0x68,  0x69,  0x6e,  0x65,  0x00,  0x00,  0x00,  0x00,  0x30,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf2,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x23,  0xf2,  0xa2,  0xd9,  0xce,  0xd5,  0x0a,  0x79,  0x56,  0xe2,  0xa6,  0x07,  0x61,  0x3a,  0x13,  0xf1, 
 0x0a,  0x00,  0x00,  0x00,  0x6d,  0x6f,  0x74,  0x6f,  0x72,  0x5f,  0x63,  0x6d,  0x64,  0x00,  0x00,  0x00, 
 0x22,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x90,  0xf3,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x04,  0x07,  0x00,  0x00,  0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65, 
 0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x07,  0x00,  0x04,  0x00,  0x00,  0x00,  0x63,  0x72,  0x63,  0x00,  0x00,  0x00,  0xf2,  0xa2, 
 0xd9,  0xce,  0xd5,  0x0a,  0x79,  0x56,  0xe2,  0xa6,  0x07,  0x61,  0x3a,  0x13,  0xf1,  0x00,  0x00,  0x00, 
 0xe2,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x21,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x68,  0x67,  0x3a,  0x3a, 
 0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x4d,  0x6f,  0x74,  0x6f,  0x72, 
 0x43,  0x6d,  0x64,  0x5f,  0x00,  0x00,  0x00,  0x00,  0xaa,  0x00,  0x00,  0x00,  0x07,  0x00,  0x00,  0x00, 
 0x13,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x02,  0x00,  0x05,  0x00,  0x00,  0x00, 
 0x6d,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x09,  0x00,  0x02,  0x00,  0x00,  0x00,  0x71,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x64,  0x71,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x12,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x74,  0x61,  0x75,  0x00,  0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00, 
 0x04,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00,  0x03,  0x00,  0x00,  0x00,  0x6b,  0x70,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x05,  0x00,  0x00,  0x00,  0x01,  0x00,  0x09,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x6b,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00, 
 0x06,  0x00,  0x00,  0x00,  0x01,  0x00,  0x07,  0x00,  0x08,  0x00,  0x00,  0x00,  0x72,  0x65,  0x73,  0x65, 
 0x72,  0x76,  0x65,  0x00,  0x00,  0x00,  0x00,  0x00,  0x40,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0xf2,  0x42,  0xb0,  0x28,  0x3a,  0xa7,  0xe1,  0xbb,  0x59,  0x2e,  0xc8,  0x98,  0xa0,  0xdb,  0xed,  0xf1, 
 0x65,  0x69,  0xfc,  0x15,  0xf0,  0xd4,  0x7a,  0xa8,  0x60,  0x0b,  0x9e,  0x91,  0xfb,  0xbc,  0xf2,  0xa2, 
 0xd9,  0xce,  0xd5,  0x0a,  0x79,  0x56,  0xe2,  0xa6,  0x07,  0x61,  0x3a,  0x13,  0xf1,  0xf1,  0x82,  0x7f, 
 0x55,  0xde,  0x9b,  0x9d,  0xe3,  0x8c,  0x62,  0xba,  0xcd,  0x1e,  0x56,  0xc9, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_hg::msg::dds_::LowCmd_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x90,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x40,  0x00,  0x00,  0x00,  0x3c,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x65,  0x69,  0xfc,  0x15,  0xf0,  0xd4,  0x7a,  0xa8,  0x60,  0x0b,  0x9e, 
 0x91,  0xfb,  0xbc,  0x00,  0x8b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x82,  0x7f,  0x55,  0xde,  0x9b,  0x9d,  0xe3, 
 0x8c,  0x62,  0xba,  0xcd,  0x1e,  0x56,  0xc9,  0x00,  0x87,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40, 
 0x40,  0x00,  0x00,  0x00,  0x3c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x42,  0xb0,  0x28, 
 0x3a,  0xa7,  0xe1,  0xbb,  0x59,  0x2e,  0xc8,  0x98,  0xa0,  0xdb,  0xed,  0x00,  0xea,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x1c,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0xa2,  0xd9,  0xce,  0xd5,  0x0a,  0x79,  0x56,  0xe2,  0xa6,  0x07,  0x61,  0x3a,  0x13,  0xf1,  0x00, 
 0xe6,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_hg::msg::dds_::LowCmd_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_hg::msg::dds_::LowCmd_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_hg::msg::dds_::LowCmd_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_hg::msg::dds_::LowCmd_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_hg::msg::dds_::LowCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.mode_pr()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.mode_machine()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_cmd()) {  //array depth 1
      if (!write(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!write(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_hg::msg::dds_::LowCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::LowCmd_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_hg::msg::dds_::LowCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.mode_pr()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.mode_machine()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (auto & a_1:instance.motor_cmd()) {  //array depth 1
      if (!read(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!read(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_hg::msg::dds_::LowCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::LowCmd_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_hg::msg::dds_::LowCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.mode_pr()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.mode_machine()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_cmd()) {  //array depth 1
      if (!move(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!move(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_hg::msg::dds_::LowCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::LowCmd_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_hg::msg::dds_::LowCmd_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.mode_pr()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.mode_machine()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 2:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, false))
        return false;
      for (const auto & a_1:instance.motor_cmd()) {  //array depth 1
      if (!max(streamer, a_1, prop))
        return false;
      }  //array depth 1
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 3:
      if (!streamer.start_member(*prop))
        return false;
      if (!streamer.start_consecutive(true, true))
        return false;
      if (!max(streamer, instance.reserve()[0], instance.reserve().size()))
        return false;
      if (!streamer.finish_consecutive())
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 4:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.crc()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_hg::msg::dds_::LowCmd_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_hg::msg::dds_::LowCmd_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_HG_LOWCMD__HPP
