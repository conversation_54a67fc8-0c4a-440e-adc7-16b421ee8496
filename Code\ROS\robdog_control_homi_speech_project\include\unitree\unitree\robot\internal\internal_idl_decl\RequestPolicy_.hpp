/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: RequestPolicy_.idl
  Source: RequestPolicy_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_REQUESTPOLICY__HPP
#define DDSCXX_REQUESTPOLICY__HPP

#include <cstdint>

namespace unitree_api
{
namespace msg
{
namespace dds_
{
class RequestPolicy_
{
private:
 int32_t priority_ = 0;
 bool noreply_ = false;

public:
  RequestPolicy_() = default;

  explicit RequestPolicy_(
    int32_t priority,
    bool noreply) :
    priority_(priority),
    noreply_(noreply) { }

  int32_t priority() const { return this->priority_; }
  int32_t& priority() { return this->priority_; }
  void priority(int32_t _val_) { this->priority_ = _val_; }
  bool noreply() const { return this->noreply_; }
  bool& noreply() { return this->noreply_; }
  void noreply(bool _val_) { this->noreply_ = _val_; }

  bool operator==(const RequestPolicy_& _other) const
  {
    (void) _other;
    return priority_ == _other.priority_ &&
      noreply_ == _other.noreply_;
  }

  bool operator!=(const RequestPolicy_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_api::msg::dds_::RequestPolicy_>::getTypeName()
{
  return "unitree_api::msg::dds_::RequestPolicy_";
}

template <> constexpr bool TopicTraits<::unitree_api::msg::dds_::RequestPolicy_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::RequestPolicy_>::type_map_blob_sz() { return 266; }
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::RequestPolicy_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::RequestPolicy_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x4b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0x76,  0x57,  0x81,  0x5a,  0xa1,  0x5f,  0x25, 
 0x32,  0xc9,  0x00,  0x8d,  0x48,  0x4d,  0x4f,  0x00,  0x33,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0xb9,  0x88,  0x29,  0x5c,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x01,  0x14,  0x70,  0x9c,  0x93,  0x00, 
 0x8e,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x99,  0xe3,  0xb9,  0xf0,  0xba,  0xac,  0xdc, 
 0x09,  0x3a,  0x18,  0x6d,  0x89,  0x7a,  0x17,  0x00,  0x76,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x2f,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x27,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x61,  0x70,  0x69,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64, 
 0x73,  0x5f,  0x3a,  0x3a,  0x52,  0x65,  0x71,  0x75,  0x65,  0x73,  0x74,  0x50,  0x6f,  0x6c,  0x69,  0x63, 
 0x79,  0x5f,  0x00,  0x00,  0x3a,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x17,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0x00,  0x09,  0x00,  0x00,  0x00,  0x70,  0x72,  0x69,  0x6f, 
 0x72,  0x69,  0x74,  0x79,  0x00,  0x00,  0x00,  0x00,  0x16,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x01,  0x00,  0x08,  0x00,  0x00,  0x00,  0x6e,  0x6f,  0x72,  0x65,  0x70,  0x6c,  0x79,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x99,  0xe3,  0xb9, 
 0xf0,  0xba,  0xac,  0xdc,  0x09,  0x3a,  0x18,  0x6d,  0x89,  0x7a,  0x17,  0xf1,  0x76,  0x57,  0x81,  0x5a, 
 0xa1,  0x5f,  0x25,  0x32,  0xc9,  0x00,  0x8d,  0x48,  0x4d,  0x4f, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::RequestPolicy_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x76,  0x57,  0x81,  0x5a,  0xa1,  0x5f,  0x25,  0x32,  0xc9,  0x00,  0x8d, 
 0x48,  0x4d,  0x4f,  0x00,  0x37,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x99,  0xe3,  0xb9,  0xf0,  0xba,  0xac,  0xdc,  0x09,  0x3a,  0x18,  0x6d, 
 0x89,  0x7a,  0x17,  0x00,  0x7a,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_api::msg::dds_::RequestPolicy_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_api::msg::dds_::RequestPolicy_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_api::msg::dds_::RequestPolicy_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_api::msg::dds_::RequestPolicy_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_api::msg::dds_::RequestPolicy_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.priority()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.noreply()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_api::msg::dds_::RequestPolicy_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestPolicy_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_api::msg::dds_::RequestPolicy_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.priority()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.noreply()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_api::msg::dds_::RequestPolicy_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestPolicy_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_api::msg::dds_::RequestPolicy_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.priority()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.noreply()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_api::msg::dds_::RequestPolicy_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestPolicy_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_api::msg::dds_::RequestPolicy_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.priority()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.noreply()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_api::msg::dds_::RequestPolicy_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestPolicy_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_REQUESTPOLICY__HPP
