#include <rclcpp/rclcpp.hpp>
#include <homi_speech/def.h>
#include <atomic>
#include <homi_speech/OfflineAsrEngine.h>
#include <homi_sdk/ThreadPool.h>
#define ESR_LIB_XUNFEI
#ifdef ESR_LIB_XUNFEI
#include <homi_sdk/vs_api.h>
#else
#include <homi_sdk/hy_esr_api.hpp>
#endif
#include <homi_speech/json.hpp>
#include <boost/locale.hpp>
#include "offline_speech_result_parser.h"
namespace homi::app {
#ifdef ESR_LIB_XUNFEI
    class ImplOfflineStream : public std::enable_shared_from_this<ImplOfflineStream>,public IAudioStreamOutput
    {
    public:
        ImplOfflineStream(const std::shared_ptr<homi::framework::ThreadPool> &threadPoolPtr
            ,std::shared_ptr<OfflineSpeechResultParser> parserPtr
            ,const std::string &eventId
            ,const std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> &callback)
        :_eventId(eventId),_callback(callback),_config{SampleFormat::PCM_S16LE, 16000,1},_threadPoolWeakPtr(threadPoolPtr),_parserWeakPtr(parserPtr)
        {
            s_count.fetch_add(1);
            _isClosed = false;
            _status = ESR_AUDIO_END;
        }
        virtual ~ImplOfflineStream()
        {
            if(!_isClosed)
            {
                VS_esr_stop();
            }
            auto k = s_count.fetch_sub(1);
            RCLCPP_INFO(rclcpp::get_logger("offlineAsrEngine"),"~ImplOfflineStream %d",k-1);
        }
        bool OnInit()
        {
            auto ret = VS_esr_start();
            if (ret != 0) {
                RCLCPP_ERROR(rclcpp::get_logger("offlineAsrEngine"),"VS_esr_start ret: %d",ret);
            }
            return true;
        }
        void _close()
        {
            if(!_isClosed){
                VS_esr_stop();
            }
            _isClosed = true;
        }
        virtual StreamErrorCode write(std::shared_ptr<AudioFrame> OutDataPtr,const std::chrono::milliseconds &wait) override
        {
            
            HOMI_SPEECH_UNUSED(wait);
            if(OutDataPtr==nullptr||OutDataPtr->data.empty()) {
                RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"write failed OutDataPtr empty!");
                return StreamErrorCode::Failure;
            }
            std::unique_lock<std::mutex> lock(_mutex);
            if(_isClosed){
                RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"write failed: closed");
                return StreamErrorCode::Failure;
            }
            if(ESR_AUDIO_END == _status){ _status = ESR_AUDIO_BEGIN;}
            else if(ESR_AUDIO_BEGIN == _status){_status = ESR_AUDIO_CONTINUE;}
            else{}
            auto data = (char *)OutDataPtr->data.data();
            VS_esr_write(data,OutDataPtr->data.size(),_status);
            EsrResult* rec_result = NULL;
            auto ret = VS_esr_get_result(&rec_result, "readable,vad");
            if(ret) 
            {
                _close();
                RCLCPP_ERROR(rclcpp::get_logger("offlineAsrEngine"),"write failed! VS_esr_get_result ret: %d",ret);
                return StreamErrorCode::Failure;
            }
            if(rec_result!=NULL&&rec_result->elem_count>0)
            {                
                auto ptr = std::make_shared<std::vector<std::shared_ptr<std::string>>>();
                for(int i=0;i<rec_result->elem_count;i++)
                {
                    auto msg = std::make_shared<std::string>(rec_result->elems[i].value);
                    ptr->push_back(msg);
                }
                auto threadPoolPtr = _threadPoolWeakPtr.lock();
                if(threadPoolPtr==nullptr)
                {
                    _close();
                    RCLCPP_ERROR(rclcpp::get_logger("offlineAsrEngine"),"write failed! _threadPoolWeakPtr is null");
                    return StreamErrorCode::Failure;
                }
                std::weak_ptr<ImplOfflineStream> myselfWeak = shared_from_this();
                auto status = rec_result->status;
                std::weak_ptr<OfflineSpeechResultParser> parserWeakPtr = _parserWeakPtr;
                threadPoolPtr->enqueue([ptr,myselfWeak,status,parserWeakPtr](){
                    auto myself = myselfWeak.lock();
                    auto parser = parserWeakPtr.lock();
                    if(myself==nullptr) return;
                    if(parser==nullptr) return;
                    bool hadFsa = false;
                    try{
                        for(auto msg: *ptr)
                        {
                            if(msg==nullptr) continue;
                            auto utfmsg = boost::locale::conv::to_utf<char>(msg->c_str(), "GBK");
                            RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"OfflineAsrEngine: %s",utfmsg.c_str());

                                auto json = nlohmann::json::parse(utfmsg);
                                auto iter = json.find("mode");
                                if(iter!=json.end() && iter.value()=="fsa")
                                {
                                    hadFsa = true;
                                    nlohmann::json result=nlohmann::json::object();
                                    if(parser->parse(json,result))
                                    {
                                        RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"OfflineAsrEngine parse success %s",result.dump().c_str());
                                        auto subType = result["resSubType"].get<int>();
                                        auto instruction = result["online"];
                                        myself->_callback(myself->_eventId,subType,instruction.dump());
                                        break;
                                    }else{
                                        if(status==ESR_RESULT_FINISH){
                                            myself->_callback(myself->_eventId,0,"");
                                            RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"OfflineAsrEngine parse failed and finish");
                                        }
                                        else
                                        {
                                            RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"OfflineAsrEngine parse failed");
                                        }
                                    }
                                }
                        }
                    }catch(const std::exception& e)
                    {
                        RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"OfflineAsrEngine: %s",e.what());
                    }
                    if(!hadFsa&&status==ESR_RESULT_FINISH)
                    {
                        myself->_callback(myself->_eventId,0,"");
                    }
                });
                if(rec_result->status == ESR_RESULT_FINISH)
                {
                    _close();
                    RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"write failed! EOF");
                    return StreamErrorCode::EEOF;
                }
            }
            return StreamErrorCode::Success;
        }
        virtual StreamErrorCode getAudioConfig(AudioConfig &config) 
        {
            config = _config;
            return StreamErrorCode::Success;
        }
        virtual StreamErrorCode close(bool flush,const std::chrono::milliseconds &wait) override
        {
            HOMI_SPEECH_UNUSED(flush);
            HOMI_SPEECH_UNUSED(wait);
            return StreamErrorCode::Success;
            std::unique_lock<std::mutex> lock(_mutex);
            _close();
            return StreamErrorCode::Success;
        }
        const std::string _eventId;
        const std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> _callback;
        const AudioConfig _config;
    private:
        std::mutex _mutex;
        std::condition_variable _cond;
        bool _isClosed;
        ESR_AUDIO_STATUS _status;
        const std::weak_ptr<homi::framework::ThreadPool> _threadPoolWeakPtr;
        const std::weak_ptr<OfflineSpeechResultParser> _parserWeakPtr;
        static std::atomic<int> s_count;
    };
    std::atomic<int> ImplOfflineStream::s_count{0};
    class ImplOfflineAsrEngine : public IOffineAsrEngine
    {
    public:
        ImplOfflineAsrEngine(const std::string &aiserJson,const std::string &parserRule): _aiserJson(aiserJson),_parserRule(parserRule)
        {
            vsInited = false;
        }
        bool OnInit()
        {
            RCLCPP_INFO(rclcpp::get_logger("offlineAsrEngine"),"ImplOfflineAsrEngine OnInit :xunfei");
            std::string cfg;
            _threadPoolPtr = std::make_shared<homi::framework::ThreadPool>(1);
            if(_threadPoolPtr==nullptr) return false;
            try
            {
                auto json = nlohmann::json::parse(_parserRule);
                _parserPtr = std::make_shared<OfflineSpeechResultParser>(json);
                cfg = nlohmann::json::parse(_aiserJson)["xunfei"].dump();
                RCLCPP_INFO(rclcpp::get_logger("offlineAsrEngine"),"ImplOfflineAsrEngine OnInit config:%s",cfg.c_str());
            }
            catch(const std::exception& e)
            {
                std::cerr << e.what() << '\n';
                RCLCPP_ERROR(rclcpp::get_logger("offlineAsrEngine"),"ImplOfflineAsrEngine OnInit error :%s",e.what());
                return false;
            }
            if(_parserPtr==nullptr) return false;
            VS_CallBackFunList VS_CB;
            auto ret = VS_init(VS_CB, (char*)cfg.c_str());
            if(ret!=0) {
                RCLCPP_ERROR(rclcpp::get_logger("offlineAsrEngine"),"OnInit failed! VS_init ret: %d",ret);
                return false;
            }
            vsInited = true;
            return true;
        }
        virtual std::shared_ptr<IAudioStreamOutput> createAudioStreamOutput(const std::string &eventId
            ,std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> callback) override
        {
            if(_threadPoolPtr==nullptr) return nullptr;
            auto prt =  std::make_shared<ImplOfflineStream>(_threadPoolPtr,_parserPtr,eventId, callback);
            if(!prt->OnInit()) return nullptr;
            return prt;
        }
        virtual ~ImplOfflineAsrEngine(){
            if(vsInited){
                VS_unInit();
            }
        };
    private:
        bool vsInited;
        const std::string _aiserJson;
        const std::string _parserRule;
        std::shared_ptr<homi::framework::ThreadPool> _threadPoolPtr;
        std::shared_ptr<OfflineSpeechResultParser> _parserPtr;
    };
#else
    class ImplOfflineStream : public std::enable_shared_from_this<ImplOfflineStream>,public IAudioStreamOutput
    {
    public:
        ImplOfflineStream(const std::shared_ptr<homi::framework::ThreadPool> &threadPoolPtr
            ,std::shared_ptr<OfflineSpeechResultParser> parserPtr
            ,const std::string &eventId
            ,const std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> &callback)
        :_eventId(eventId),_callback(callback),_config{SampleFormat::PCM_S16LE, 16000,1},_threadPoolWeakPtr(threadPoolPtr),_parserWeakPtr(parserPtr)
        {
            s_count.fetch_add(1);
            _isClosed = false;
        }
        virtual ~ImplOfflineStream()
        {
            auto k = s_count.fetch_sub(1);
            RCLCPP_INFO(rclcpp::get_logger("offlineAsrEngine"),"~ImplOfflineStream %d",k-1);
        }
        bool OnInit()
        {
            _esrPtr = hy_esr::createESR();
            if(_esrPtr==nullptr)
            {
                _isClosed=true;
                return false;
            }
            else return true;
        }
        void _close()
        {
            _isClosed = true;
        }
        virtual StreamErrorCode write(std::shared_ptr<AudioFrame> OutDataPtr,const std::chrono::milliseconds &wait) override
        {
            HOMI_SPEECH_UNUSED(wait);
            if(OutDataPtr==nullptr||OutDataPtr->data.empty()) return StreamErrorCode::Failure;
            std::unique_lock<std::mutex> lock(_mutex);
            if(_isClosed){return StreamErrorCode::Failure;}
            auto data = (char *)OutDataPtr->data.data();
            _esrPtr->ESRwrite(data,OutDataPtr->data.size());
            std::string esrResult;
            auto ret = _esrPtr->ESRresult(esrResult);
            if(ret==0&&!esrResult.empty())
            {            
                auto threadPoolPtr = _threadPoolWeakPtr.lock();
                if(threadPoolPtr==nullptr)
                {
                    _close();
                    return StreamErrorCode::Failure;
                }
                std::weak_ptr<ImplOfflineStream> myselfWeak = shared_from_this();
                std::weak_ptr<OfflineSpeechResultParser> parserWeakPtr = _parserWeakPtr;
                threadPoolPtr->enqueue([esrResult,myselfWeak,parserWeakPtr](){
                    auto myself = myselfWeak.lock();
                    auto parser = parserWeakPtr.lock();
                    if(myself==nullptr) return;
                    if(parser==nullptr) return;
                    try{
                        RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"OfflineAsrEngine: %s",esrResult.c_str());
                        auto json = nlohmann::json::parse(esrResult);
                        nlohmann::json result=nlohmann::json::object();
                        if(parser->parse(json,result))
                        {
                            RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"OfflineAsrEngine parse success %s",result.dump().c_str());
                            auto subType = result["resSubType"].get<int>();
                            auto instruction = result["online"];
                            myself->_callback(myself->_eventId,subType,instruction.dump());
                        }else{
                            RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"OfflineAsrEngine parse failed");
                        }
                    }catch(const std::exception& e)
                    {
                        RCLCPP_WARN(rclcpp::get_logger("offlineAsrEngine"),"OfflineAsrEngine: %s",e.what());
                    }
                });
            }
            return StreamErrorCode::Success;
        }
        virtual StreamErrorCode getAudioConfig(AudioConfig &config) 
        {
            config = _config;
            return StreamErrorCode::Success;
        }
        virtual StreamErrorCode close(bool flush,const std::chrono::milliseconds &wait) override
        {
            HOMI_SPEECH_UNUSED(flush);
            HOMI_SPEECH_UNUSED(wait);
            return StreamErrorCode::Success;
            std::unique_lock<std::mutex> lock(_mutex);
            _close();
            return StreamErrorCode::Success;
        }
        const std::string _eventId;
        const std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> _callback;
        const AudioConfig _config;
    private:
        std::mutex _mutex;
        std::condition_variable _cond;
        bool _isClosed;
        const std::weak_ptr<homi::framework::ThreadPool> _threadPoolWeakPtr;
        const std::weak_ptr<OfflineSpeechResultParser> _parserWeakPtr;
        static std::atomic<int> s_count;
        std::shared_ptr<hy_esr::IESR> _esrPtr;
    };
    std::atomic<int> ImplOfflineStream::s_count{0};
    class ImplOfflineAsrEngine : public IOffineAsrEngine
    {
    public:
        ImplOfflineAsrEngine(const std::string &aiserJson,const std::string &parserRule): _aiserJson(aiserJson),_parserRule(parserRule)
        {
            vsInited = false;
        }
        bool OnInit()
        {
            RCLCPP_INFO(rclcpp::get_logger("offlineAsrEngine"),"ImplOfflineAsrEngine OnInit :hy_esr");
            _threadPoolPtr = std::make_shared<homi::framework::ThreadPool>(1);
            std::string cfg;
            if(_threadPoolPtr==nullptr) return false;
            try
            {
                auto json = nlohmann::json::parse(_parserRule);
                _parserPtr = std::make_shared<OfflineSpeechResultParser>(json);
                cfg = nlohmann::json::parse(_aiserJson)["hy_esr"].get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger("offlineAsrEngine"),"ImplOfflineAsrEngine OnInit config:%s",cfg.c_str());
            }
            catch(const std::exception& e)
            {
                std::cerr << e.what() << '\n';
                RCLCPP_INFO(rclcpp::get_logger("offlineAsrEngine"),"ImplOfflineAsrEngine OnInit error :%s",e.what());
                return false;
            }
            if(_parserPtr==nullptr) return false;
            RCLCPP_INFO(rclcpp::get_logger("offlineAsrEngine"),"ImplOfflineAsrEngine OnInit:ESRinit:%s",cfg.c_str());
            auto ret = hy_esr::ESRinit(cfg);
            if(ret!=0) return false;
            vsInited = true;
            return true;
        }
        virtual std::shared_ptr<IAudioStreamOutput> createAudioStreamOutput(const std::string &eventId
            ,std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> callback) override
        {
            if(_threadPoolPtr==nullptr) return nullptr;
            auto prt =  std::make_shared<ImplOfflineStream>(_threadPoolPtr,_parserPtr,eventId, callback);
            if(!prt->OnInit()) return nullptr;
            return prt;
        }
        virtual ~ImplOfflineAsrEngine(){
            if(vsInited){
                hy_esr::ESRunInit();
            }
        };
    private:
        bool vsInited;
        const std::string _aiserJson;
        const std::string _parserRule;
        std::shared_ptr<homi::framework::ThreadPool> _threadPoolPtr;
        std::shared_ptr<OfflineSpeechResultParser> _parserPtr;
    };
#endif
    class ImplOfflineAsrEngineNone : public IOffineAsrEngine
    {
    public:
        virtual std::shared_ptr<IAudioStreamOutput> createAudioStreamOutput(const std::string &eventId
            ,std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> callback) override
        {
            HOMI_SPEECH_UNUSED(eventId);
            HOMI_SPEECH_UNUSED(callback);
            return nullptr;
        }
    };
    std::shared_ptr<IOffineAsrEngine> createOfflineAsrEngine(const std::string &aiserJson,const std::string &parseRule)
    {
        auto ptr =  std::make_shared<ImplOfflineAsrEngine>(aiserJson,parseRule);
        if(!ptr->OnInit())
        {
            return std::make_shared<ImplOfflineAsrEngineNone>();
        }
        return ptr;
    }
}