#ifndef LITEDB_H
#define LITEDB_H

#include <sqlite3.h>
#include <vector>
#include <string>
#include <map>
#include <rclcpp/rclcpp.hpp>

class SQLiteDB {
public:
    explicit SQLiteDB(const std::string& dbPath,rclcpp::Logger logger);
    ~SQLiteDB();

    bool open();
    void close();

    bool execute(const std::string& sql);
    bool createTable(const std::string& tableName, const std::string& columns);
    bool insert(const std::string& tableName, const std::map<std::string, std::string>& values);
    bool update(const std::string& tableName, const std::map<std::string, std::string>& values, const std::string& condition);
    bool remove(const std::string& tableName, const std::string& condition);
    std::vector<std::vector<std::string>> query(const std::string& sql);

private:
    static int staticCallback(void* data, int argc, char** argv, char** azColName);
    int callback(int argc, char** argv, char** azColName);
    std::string dbPath;
    rclcpp::Logger logger_; 
    sqlite3* db;
    std::vector<std::vector<std::string>> results;
};

#endif // SQLITE_DB_H