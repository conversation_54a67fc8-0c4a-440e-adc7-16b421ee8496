/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: RequestLease_.idl
  Source: RequestLease_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_REQUESTLEASE__HPP
#define DDSCXX_REQUESTLEASE__HPP

#include <cstdint>

namespace unitree_api
{
namespace msg
{
namespace dds_
{
class RequestLease_
{
private:
 int64_t id_ = 0;

public:
  RequestLease_() = default;

  explicit RequestLease_(
    int64_t id) :
    id_(id) { }

  int64_t id() const { return this->id_; }
  int64_t& id() { return this->id_; }
  void id(int64_t _val_) { this->id_ = _val_; }

  bool operator==(const RequestLease_& _other) const
  {
    (void) _other;
    return id_ == _other.id_;
  }

  bool operator!=(const RequestLease_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_api::msg::dds_::RequestLease_>::getTypeName()
{
  return "unitree_api::msg::dds_::RequestLease_";
}

template <> constexpr bool TopicTraits<::unitree_api::msg::dds_::RequestLease_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::RequestLease_>::type_map_blob_sz() { return 218; }
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::RequestLease_>::type_info_blob_sz() { return 100; }
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::RequestLease_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x3b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf1,  0xe7,  0xaa,  0x98,  0x61,  0xf7,  0xb0,  0x22, 
 0x20,  0x9b,  0xda,  0x6c,  0x89,  0x3f,  0xca,  0x00,  0x23,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0xb8,  0x0b,  0xb7,  0x74,  0x00, 
 0x6d,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x0c,  0xd7,  0x3d,  0x1a,  0x1e,  0x87,  0xee, 
 0x27,  0x8f,  0x84,  0xee,  0x9f,  0x41,  0xe3,  0x00,  0x55,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x2e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74, 
 0x72,  0x65,  0x65,  0x5f,  0x61,  0x70,  0x69,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64, 
 0x73,  0x5f,  0x3a,  0x3a,  0x52,  0x65,  0x71,  0x75,  0x65,  0x73,  0x74,  0x4c,  0x65,  0x61,  0x73,  0x65, 
 0x5f,  0x00,  0x00,  0x00,  0x19,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0x00,  0x03,  0x00,  0x00,  0x00,  0x69,  0x64,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x22,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0xf2,  0x0c,  0xd7,  0x3d, 
 0x1a,  0x1e,  0x87,  0xee,  0x27,  0x8f,  0x84,  0xee,  0x9f,  0x41,  0xe3,  0xf1,  0xe7,  0xaa,  0x98,  0x61, 
 0xf7,  0xb0,  0x22,  0x20,  0x9b,  0xda,  0x6c,  0x89,  0x3f,  0xca, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::RequestLease_>::type_info_blob() {
  static const uint8_t blob[] = {
 0x60,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0xe7,  0xaa,  0x98,  0x61,  0xf7,  0xb0,  0x22,  0x20,  0x9b,  0xda,  0x6c, 
 0x89,  0x3f,  0xca,  0x00,  0x27,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x28,  0x00,  0x00,  0x00,  0x24,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x0c,  0xd7,  0x3d,  0x1a,  0x1e,  0x87,  0xee,  0x27,  0x8f,  0x84,  0xee, 
 0x9f,  0x41,  0xe3,  0x00,  0x59,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_api::msg::dds_::RequestLease_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_api::msg::dds_::RequestLease_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_api::msg::dds_::RequestLease_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_api::msg::dds_::RequestLease_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_api::msg::dds_::RequestLease_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_api::msg::dds_::RequestLease_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestLease_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_api::msg::dds_::RequestLease_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_api::msg::dds_::RequestLease_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestLease_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_api::msg::dds_::RequestLease_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_api::msg::dds_::RequestLease_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestLease_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_api::msg::dds_::RequestLease_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.id()))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_api::msg::dds_::RequestLease_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::RequestLease_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_REQUESTLEASE__HPP
