#!/bin/bash

# 检查参数数量是否正确
if [ "$#" -ne 2 ]; then
    echo "usage: $0 srcdir dstdir"
    exit 1
fi

echo $0 $@

#SPEECH_SRC_DIR=$(cd `dirname $0`; pwd)
#SPEECH_WORK_DIR="/home/<USER>/.homi_speech"
SPEECH_SRC_DIR=$1
SPEECH_WORK_DIR=$2

mkdir -p $SPEECH_WORK_DIR
cp -r $SPEECH_SRC_DIR/launch/res $SPEECH_WORK_DIR/
cp -r $SPEECH_SRC_DIR/scripts $SPEECH_WORK_DIR/
chmod 777 $SPEECH_WORK_DIR/scripts/*.sh
dos2unix $SPEECH_WORK_DIR/scripts/*.sh
#cp -f $SPEECH_WORK_DIR/scripts/set_default_audio.sh /usr/local/bin
#cp -f $SPEECH_WORK_DIR/scripts/55-usb-audio.rules /etc/udev/rules.d
# 重新加载udev
#udevadm control --reload-rules
#udevadm trigger
