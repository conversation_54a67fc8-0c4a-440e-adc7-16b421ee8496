#ifndef __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_ALSAHELPER_H_
#define __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_ALSAHELPER_H_
#include <alsa/asoundlib.h>
#include <rclcpp/rclcpp.hpp>
#include <homi_speech/def.h>
#include <queue>
#include <vector>
#include <memory>
#include <homi_speech/homi_err.h>
#include <homi_speech/homi_exception.h>
#include <boost/thread.hpp>
namespace homi_speech
{
    class AlsaHelperConfig
    {
    private:
        uint32_t _rateNear;
        uint32_t _bufferTime;
        snd_pcm_uframes_t _bufferSize;
        uint32_t _periodTime;
        snd_pcm_uframes_t _periodSize;
        snd_pcm_format_t _format;
        uint32_t _bitsPerFrame;
        uint32_t _channels;
        uint32_t _periodBytes;   
    public:
        AlsaHelperConfig()
        {
            clear();
        }
        void clear()
        {
            _rateNear = 0;
            _bufferTime = 0;
            _bufferSize = 0;
            _periodTime = 0;
            _format = SND_PCM_FORMAT_UNKNOWN;
            _bitsPerFrame = 0;
            _channels = 0;
            _periodBytes = 0;
        }
        std::string toString()
        {
            return  "_rateNear: "+std::to_string(_rateNear)+
                    " , _bufferTime: "+std::to_string(_bufferTime)+
                    " , _bufferSize: "+std::to_string(_bufferSize)+
                    " , _periodTime: "+std::to_string(_periodTime)+
                    " , _periodSize: "+std::to_string(_periodSize)+
                    " , _bitsPerFrame: "+std::to_string(_bitsPerFrame)+
                    " , _channels: "+std::to_string(_channels)+
                    " , _periodBytes: "+std::to_string(_periodBytes)+
                    " , _format: "+std::string(snd_pcm_format_name(_format));
        }
        uint32_t getRateNear()
        {
            return _rateNear;
        }
        uint32_t getBufferTime()
        {
            return _bufferTime;
        }
        snd_pcm_uframes_t getBufferSize()
        {
            return _bufferSize;
        }
        uint32_t getPeriodTime()
        {
            return _periodTime;
        }
        snd_pcm_uframes_t getPeriodSize()
        {
            return _periodSize;
        }
        snd_pcm_format_t getFormat()
        {
            return _format;
        }
        uint32_t getBitPerFrame()
        {
            return _bitsPerFrame;
        }
        uint32_t getChannels()
        {
            return _channels;
        }
        uint32_t getPeriodBytes()
        {
            return _periodBytes;
        }
        snd_pcm_uframes_t sizeToFrame(size_t size)
        {
            return (size*8)/_bitsPerFrame;
        }
        size_t frameToSize(snd_pcm_uframes_t frame)
        {
            return (frame * _bitsPerFrame) / 8;
        }
        void config(snd_pcm_t *pcmHandle,const char *format,const uint32_t channels,const uint32_t rate,
            const uint32_t nearBufferTime = 1000000,
            const snd_pcm_uframes_t startDelay = 200000,
            const snd_pcm_uframes_t stopDelay = 900000)
        {
            int err = 0;
            snd_pcm_uframes_t avail_min = 0;
            snd_pcm_uframes_t start_threshold = 0;
            snd_pcm_uframes_t stop_threshold = 0;
            if(pcmHandle==nullptr)
            {
                _homi_err = homi_speech::homi_code::NULL_HANDLE;
                THROW_HOMI_E(_homi_err);
            }
            _format = snd_pcm_format_value(format);
            if(_format==SND_PCM_FORMAT_UNKNOWN)
            {
                _homi_err = homi_speech::homi_code::INVALID_PARAM;
                THROW_HOMI_E(_homi_err);
            }
            /*硬件参数*/
            snd_pcm_hw_params_t *params;
            /*软件参数*/
            snd_pcm_sw_params_t *swparams;

            snd_pcm_hw_params_alloca(&params);
            snd_pcm_sw_params_alloca(&swparams);

            /*设置所有配置，相当与初始化硬件配置变量*/
            err = snd_pcm_hw_params_any(pcmHandle, params);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set hw_params any fail,%s",snd_strerror(err));
            }
            /*下面都是设置硬件相关参数*/
            /*设置访问方式这里使用直接写入交错数据，即使用readi/writei*/
            err = snd_pcm_hw_params_set_access(pcmHandle, params,
                            SND_PCM_ACCESS_RW_INTERLEAVED);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set access type fail,%s",snd_strerror(err));
            }
            err = snd_pcm_hw_params_set_format(pcmHandle, params, _format);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set format fail,%s",snd_strerror(err));
            }
            _channels = channels;
            err = snd_pcm_hw_params_set_channels(pcmHandle, params, _channels);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set channel fail,%s",snd_strerror(err));
            }
            /*set nearest rate, return the rate in ainfo->rate*/
            _rateNear = rate;
            err = snd_pcm_hw_params_set_rate_near(pcmHandle, params, &_rateNear, NULL);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set rate near fail,%s",snd_strerror(err));
            }

            if (_rateNear > rate * 1.05 || _rateNear < rate * 0.95) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"rate is not near:%u",_rateNear);
            }
            /*获取驱动的最大缓存时间参数, 驱动设置的128k, 就是dma ringbuffer的size*/
            err = snd_pcm_hw_params_get_buffer_time_max(params,
                        &_bufferTime, NULL);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"get buffe time max fail,%s",snd_strerror(err));
            }
            /*当输入参数 16k 1 channel 16bit时，buffer time 为4.096s*/
            if (_bufferTime > nearBufferTime)
                _bufferTime = nearBufferTime;
            /*读写单元长度*/
            _periodTime = _bufferTime / 50;

            err = snd_pcm_hw_params_set_period_time_near(pcmHandle, params,
                            &_periodTime, NULL);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set period time near fail,%s",snd_strerror(err));
            }

            err = snd_pcm_hw_params_set_buffer_time_near(pcmHandle, params,
                            &_bufferTime, NULL);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set buffer time near fail,%s",snd_strerror(err));
            }
            /*设置硬件参数，在这个末尾会将codec的音频路径中的功能单元上电*/
            err = snd_pcm_hw_params(pcmHandle, params);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"fail to install hw params,%s",snd_strerror(err));
            }
            /*以frame为单位*/
            snd_pcm_hw_params_get_period_size(params, &_periodSize, NULL);
            snd_pcm_hw_params_get_buffer_size(params, &_bufferSize);
            /*下面是软件参数*/
            /*获取软件参数*/
            snd_pcm_sw_params_current(pcmHandle, swparams);
            avail_min = _periodSize;
            /*最小可用数据帧数, 当buffer里边的数据小于这个值时，读写操作会阻塞等待*/
            err = snd_pcm_sw_params_set_avail_min(pcmHandle, swparams, avail_min);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set avail min fail,%s",snd_strerror(err));
            }
            /*数据启动门限，当输入的读写数据大于这个值时，读写操作才会启动*/
            start_threshold = (double)_rateNear * startDelay /nearBufferTime;
            err = snd_pcm_sw_params_set_start_threshold(pcmHandle, swparams, start_threshold);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set start threshold fail,%s",snd_strerror(err));
            }
            /*停止门限，有效数据达到这个值时，说明处理过程出现问题，会报overrun*/
            stop_threshold = (double)_rateNear * stopDelay /nearBufferTime;
            err = snd_pcm_sw_params_set_stop_threshold(pcmHandle, swparams, stop_threshold);
            if (err < 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"set stop threshold fail,%s",snd_strerror(err));
            }
            /*设置软件参数*/
            err = snd_pcm_sw_params(pcmHandle, swparams);
            if (err < 0 ) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"fail to install sw params,%s",snd_strerror(err));
            }

            err = snd_pcm_format_physical_width(_format);
            if(err<0)
            { 
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"snd_pcm_format_physical_width is fail,%s",snd_strerror(err));           
            }
            _bitsPerFrame = err * _channels;
            _periodBytes = _periodSize * _bitsPerFrame / 8;     
        }
    };
    class AlsaHelperPlayback
    {
    private:
        AlsaHelperConfig _config;
        std::shared_ptr<std::vector<unsigned char>> _remainData;
        std::queue<std::shared_ptr<std::vector<unsigned char>>> _periodDataQueue;
        snd_pcm_t *_handle;
        boost::mutex _mtx;
        boost::condition_variable _cv;
        std::string _name;
        std::string _format;
        unsigned int _channels;
        unsigned int _rate;
        bool _isPreemted;
        void _setSilenceAndPushToQueue()
        {
            if(_remainData&&_remainData->size()>0)
            {
                if(_remainData->size()<_config.getPeriodBytes())
                {
                    const size_t size = _remainData->size();
                    _remainData->resize(_config.getPeriodBytes());
                    const unsigned char* buffer = _remainData->data();
                    RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"need snd_pcm_format_set_silence,size=%lu",size);
                    auto err = snd_pcm_format_set_silence(_config.getFormat(),
                        (void *)&buffer[size],
                        _config.sizeToFrame((_config.getPeriodBytes() - size)) * _config.getChannels());
                    if(err<0)
                    {
                        _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                        THROW_HOMI_ES(_homi_err,"snd_pcm_format_set_silence fail,%s",snd_strerror(err));    
                    }   
                    _periodDataQueue.push(_remainData);      
                    _remainData.reset();               
                }
                else if(_remainData->size()==_config.getPeriodBytes())
                {
                    _periodDataQueue.push(_remainData);  
                    _remainData.reset();
                }else{
                    _homi_err = homi_speech::homi_code::ALSA_HELPER_ERROR;
                    THROW_HOMI_E(_homi_err);   
                }
            }         
        }
        int _writeiOnePeriod()
        {
            if(!_periodDataQueue.empty())
            {
                auto data = _periodDataQueue.front();
                if(data==nullptr||data->size()==0||data->size()!=_config.getPeriodBytes())
                {
                    RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"----%d %ld %u",(data==nullptr),data->size(),_config.getPeriodBytes());
                    _homi_err = homi_speech::homi_code::ALSA_HELPER_ERROR;
                    THROW_HOMI_E(_homi_err);                    
                }
                int err = snd_pcm_writei(_handle,data->data(),_config.getPeriodSize());
                if(err<0)
                {
                    RCLCPP_WARN(rclcpp::get_logger("AlsaHelper"),"snd_pcm_writei fail,%s",snd_strerror(err));
                    snd_pcm_prepare(_handle);
                    err = 0;                 
                }
                _periodDataQueue.pop();
                return err;
            }
            return 0;
        }
        void _close()
        {
            if(_handle)
            {
                auto err = snd_pcm_drop(_handle);
                if(err<0)
                {
                    RCLCPP_WARN(rclcpp::get_logger("AlsaHelper"),"snd_pcm_drop fail,%s",snd_strerror(err));
                }
                err = snd_pcm_close(_handle);
                if(err<0)
                {
                    RCLCPP_WARN(rclcpp::get_logger("AlsaHelper"),"snd_pcm_close fail,%s",snd_strerror(err));  
                }
            }
            if(_remainData) _remainData.reset();
            while(!_periodDataQueue.empty())_periodDataQueue.pop();
            _handle=nullptr;
            _config.clear();
        }
        void _openAndConfig()
        {
            int ret = snd_pcm_open(&_handle,_name.c_str(),SND_PCM_STREAM_PLAYBACK,0);
            if(ret<0)
            {
                RCLCPP_ERROR(rclcpp::get_logger("AlsaHelper"),"snd_pcm_open fail, ret=%d err: %s\n",ret, snd_strerror(ret));  
                RCLCPP_WARN(rclcpp::get_logger("AlsaHelper"), "retry ...\n");
                usleep(200000);  // 等待 
                ret = snd_pcm_open(&_handle,_name.c_str(),SND_PCM_STREAM_PLAYBACK,0);
                if (ret<0) {
                    RCLCPP_ERROR(rclcpp::get_logger("AlsaHelper"),"retry snd_pcm_open fail, ret=%d err: %s\n",ret, snd_strerror(ret));  
                    _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                    THROW_HOMI_ES(_homi_err,"snd_pcm_open error,%s",snd_strerror(ret));
                }
            }
            _config.config(_handle,_format.c_str(),_channels,_rate);
        }
        // void _setMaxVolume()
        // {
        //     snd_mixer_t *mixer;
        //     snd_mixer_open(&mixer, 0);
        //     snd_mixer_attach(mixer, _name.c_str());
        //     snd_mixer_selem_register(mixer, NULL, NULL);
        //     snd_mixer_load(mixer);
        //     snd_mixer_selem_id_t *sid;
        //     snd_mixer_selem_id_alloca(&sid);
        //     snd_mixer_selem_id_set_index(sid, 0);
        //     snd_mixer_selem_id_set_name(sid, "Master"); // 或者使用你想要调节的音频通道的名字
        //     snd_mixer_elem_t* elem = snd_mixer_find_selem(mixer, sid);
        //     long min, max, volume;
        //     snd_mixer_selem_get_playback_volume_range(elem, &min, &max);
        //     snd_mixer_selem_get_playback_volume(elem, SND_MIXER_SCHN_MONO, &volume);
        //     snd_mixer_selem_set_playback_volume_all(elem, max);
        //     snd_mixer_close(mixer);
        // }
    public:
        AlsaHelperPlayback():_handle(nullptr),_isPreemted(false)
        {
        }
        ~AlsaHelperPlayback()
        {
            close();
        }
        void acquireControl()
        {
            boost::lock_guard<boost::mutex> lock(_mtx);
            if(_isPreemted==true)
                return;
            _isPreemted=true;
            if(_handle)
            {
                snd_pcm_pause(_handle,1);
            }
        }
        void releaseControl()
        {
            boost::lock_guard<boost::mutex> lock(_mtx);
            if(_isPreemted==false)
                return;
            _isPreemted=false;
            if(_handle)
            {
                snd_pcm_pause(_handle,0);
            }
            _cv.notify_all();
        }
        bool isPreemted()
        {
            boost::lock_guard<boost::mutex> lock(_mtx);
            return _isPreemted;
        }
        template<typename _Rep, typename _Period>
        bool waitForPreemted(const boost::chrono::duration<_Rep, _Period>& rtime)
        {
            boost::unique_lock<boost::mutex> lock(_mtx);
            return _cv.wait_for(lock,rtime,[this](){ return _isPreemted==false;});
        }
        void waitPreemted()
        {
            boost::unique_lock<boost::mutex> lock(_mtx);
            _cv.wait(lock,[this](){ return _isPreemted==false;});
        }
        int drain(const bool blockInPreempt=false)
        {
            unsigned char tmp[4];
            RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"drain blockInPreempt==%s",(blockInPreempt==true)?"true":"false");        
            return writei(tmp,0,blockInPreempt,true); 
        }
        void close()
        {
            boost::lock_guard<boost::mutex> lock(_mtx);
            RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"close");
            _close(); 
        }
        u_int32_t getPeriodBytes() noexcept
        {
            boost::lock_guard<boost::mutex> lock(_mtx);
            return _config.getPeriodBytes();
        }
        snd_pcm_uframes_t getPeriodSize() noexcept
        {
            boost::lock_guard<boost::mutex> lock(_mtx);
            return _config.getPeriodSize();
        }
        void openAndConfig(const std::string &name,const std::string &format,const unsigned int channels,const unsigned int rate,const bool blockInPreempt=false)
        {
            RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"name=%s,foramt=%s,channels=%u,rate=%u",name.c_str(),format.c_str(),channels,rate);
            boost::unique_lock<boost::mutex> lock(_mtx);
            if(_isPreemted)
            {
                if(blockInPreempt)
                {
                    while(_isPreemted)
                    {
                        lock.unlock();
                        boost::this_thread::sleep_for(boost::chrono::milliseconds(200));
                        lock.lock();
                    }
                }
                else
                {
                    _homi_err = homi_speech::homi_code::ALSA_PREEMPTED;
                    THROW_HOMI_E(_homi_err);  
                }
            }
            _name = name;
            _format = format;
            _channels = channels;
            _rate = rate;
            _close();
            //_setMaxVolume();
            _openAndConfig();
            RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"%p %s",_handle ,_config.toString().c_str()); 
        }
        int writei(const unsigned char *buffer,const size_t size,const bool blockInPreempt=false,const bool isDrain=false)
        {
            RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"size=%lu,isDrain=%s",size,std::to_string(isDrain).c_str());
            if(buffer==nullptr&&size!=0)
            {
                return 0;
            }
            boost::unique_lock<boost::mutex> lock(_mtx);
            if(_handle==nullptr)
            {
                _homi_err = homi_speech::homi_code::ALSA_CLOSE;
                THROW_HOMI_E(_homi_err);
            }
            if(_isPreemted)
            {
                if(blockInPreempt)
                {
                    while(_isPreemted)
                    {
                        lock.unlock();
                        boost::this_thread::sleep_for(boost::chrono::milliseconds(200));
                        lock.lock();
                    }
                }
                else
                {
                    _homi_err = homi_speech::homi_code::ALSA_PREEMPTED;
                    THROW_HOMI_E(_homi_err);  
                }
            }
            if(_remainData)
            {
                _remainData->insert(_remainData->end(),buffer,buffer+size);
            }
            else
            {
                _remainData = std::make_shared<std::vector<unsigned char>>(buffer,buffer+size);
            }
            unsigned int page=_remainData->size()/_config.getPeriodBytes();
            unsigned int i;    
            unsigned char * ptr = _remainData->data(); 
            for(i=0;i<page;i++)
            {
                _periodDataQueue.push(std::make_shared<std::vector<unsigned char>>(ptr+i*_config.getPeriodBytes(),ptr+(i+1)*_config.getPeriodBytes()));
            }
            std::copy(_remainData->begin()+i*_config.getPeriodBytes(),_remainData->end(),_remainData->begin());
            _remainData->resize(std::distance(_remainData->begin()+i*_config.getPeriodBytes(),_remainData->end()));
            if(isDrain)
            {
                _setSilenceAndPushToQueue();
            }
            int ret =0;
            while(!_periodDataQueue.empty())
            {
                auto availd = snd_pcm_avail_update(_handle);
                RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"availd = %ld %lu",availd,_config.getPeriodSize());
                if(availd<0)
                {
                    snd_pcm_prepare(_handle);
                }
                else if(availd<(int)_config.getPeriodSize())
                {
                    auto wait = _config.getPeriodTime();
                    lock.unlock();
                    boost::this_thread::sleep_for(boost::chrono::microseconds(wait));
                    lock.lock();
                    if(_handle==nullptr)
                    {
                        _homi_err = homi_speech::homi_code::ALSA_CLOSE;
                        THROW_HOMI_E(_homi_err);
                    }
                    if(_isPreemted)
                    {
                        if(blockInPreempt)
                        {
                            while(_isPreemted)
                            {
                                lock.unlock();
                                boost::this_thread::sleep_for(boost::chrono::milliseconds(200));
                                lock.lock();
                            }
                        }
                        else
                        {
                            _homi_err = homi_speech::homi_code::ALSA_PREEMPTED;
                            THROW_HOMI_E(_homi_err);  
                        }
                    }
                }else
                {
                    ret+=_writeiOnePeriod();
                }
            }
            if(isDrain)
            {
                RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"isDrain");
                auto availd = snd_pcm_avail_update(_handle);
                RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"availd = %ld",availd);
                while(availd>=0 && availd<(snd_pcm_sframes_t)(_config.getBufferSize()-_config.getPeriodSize()))
                {
                    auto wait = _config.getPeriodTime();
                    lock.unlock();
                    boost::this_thread::sleep_for(boost::chrono::microseconds(wait));
                    lock.lock();
                    if(_handle==nullptr)
                    {
                        _homi_err = homi_speech::homi_code::ALSA_CLOSE;
                        THROW_HOMI_E(_homi_err);
                    }
                    if(_isPreemted)
                    {
                        if(blockInPreempt)
                        {
                            while(_isPreemted)
                            {
                                lock.unlock();
                                boost::this_thread::sleep_for(boost::chrono::milliseconds(200));
                                lock.lock();
                            }
                        }
                        else
                        {
                            _homi_err = homi_speech::homi_code::ALSA_PREEMPTED;
                            THROW_HOMI_E(_homi_err);  
                        }
                    }
                    availd = snd_pcm_avail_update(_handle);  
                    RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"availd = %ld",availd);              
                }
                RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"snd_pcm_drain begin");
                snd_pcm_drain(_handle);
                RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"snd_pcm_drain end");
                _close();
            }
            return ret;
        }
    };
    class AlsaHelperCapture
    {
    private:
        AlsaHelperConfig _config;
        snd_pcm_t *_handle;
        boost::mutex _mtx;
        std::string _name;
        std::string _format;
        unsigned int _channels;
        unsigned int _rate;
        void _openAndConfig()
        {
            int ret = snd_pcm_open(&_handle,_name.c_str(),SND_PCM_STREAM_CAPTURE,0);
            if(ret<0)
            {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"snd_pcm_open error,%s",snd_strerror(err));   
            }
            _config.config(_handle,_format.c_str(),_channels,_rate);
        }
        void _close()
        {
            if(_handle)
            {
                snd_pcm_close(_handle);
                _handle=nullptr;
            }
        }
    public:
        AlsaHelperCapture():_handle(nullptr)
        {

        }
        ~AlsaHelperCapture()
        {
            _close();
        }
        void close()
        {
            boost::lock_guard<boost::mutex> lock(_mtx);
            return _close();
        }
        void openAndConfig(const std::string &name,const std::string &format,const unsigned int channels,const unsigned int rate)
        {
            boost::lock_guard<boost::mutex> lock(_mtx);
            RCLCPP_DEBUG(rclcpp::get_logger("AlsaHelper"),"name=%s,foramt=%s,channels=%u,rate=%u",name.c_str(),format.c_str(),channels,rate);
            _name = name;
            _format = format;
            _channels = channels;
            _rate = rate;
            _close();
            _openAndConfig();
        }
        int readi(void *buffer, snd_pcm_uframes_t size)
        {
            boost::lock_guard<boost::mutex> lock(_mtx);
            if(buffer==nullptr)
            {
                RCLCPP_WARN(rclcpp::get_logger("AlsaHelper"),"buffer is nullptr");
                return 0;  
            }
            if(_handle==nullptr)
            {
                _homi_err = homi_speech::homi_code::ALSA_CLOSE;
                THROW_HOMI_E(_homi_err); 
            }
            snd_pcm_sframes_t ret = snd_pcm_readi(_handle, buffer, size);
            if (ret <= 0) {
                _homi_err = homi_speech::homi_code::ALSA_LIB_ERROR;
                THROW_HOMI_ES(_homi_err,"snd_pcm_open error,%s",snd_strerror(ret));   
            }else{
                return (ret*_config.getBitPerFrame())/8;
            }
            
        }
        u_int32_t getPeriodBytes()
        {
            return _config.getPeriodBytes();
        }
        snd_pcm_uframes_t getPeriodSize()
        {
            return _config.getPeriodSize();
        }
    };
    int getCardId(const std::string &sh, const std::string &deviceName)
    {
        // 构建调用shell脚本的命令
        std::string command = sh + " \"" + deviceName + "\"";

        // 调用shell脚本并获取输出
        FILE* pipe = popen(command.c_str(), "r");
        if (!pipe) {
            return -1;
        }
        char buffer[128];
        std::string result = "";
        while (!feof(pipe)) {
            if (fgets(buffer, 128, pipe) != NULL)
                result += buffer;
        }
        pclose(pipe);

        // 去除输出中的换行符
        result.erase(result.find_last_not_of("\n") + 1);

        RCLCPP_INFO(rclcpp::get_logger("AlsaHelper"),"command = %s  getCardId = %s",command.c_str(), result.c_str());

        if(result.empty())
        {
            RCLCPP_INFO(rclcpp::get_logger("AlsaHelper"),"result.empty()");
            return -1;
        }
        int cardId = -1;
        try
        {
            cardId = std::stoi(result);
        }
        catch(const std::exception& e)
        {
            cardId = -1;
            RCLCPP_INFO(rclcpp::get_logger("AlsaHelper"),"string to int error %s",e.what());
        }
        return cardId;
    }
}
#endif  // __DEEPROBOTS_APPLICATION_ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_ALSAHELPER_H_
