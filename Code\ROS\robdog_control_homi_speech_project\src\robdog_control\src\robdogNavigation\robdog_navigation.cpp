
/****************************** 建图导航相关 **********************************/
#include "libWebSocket.h"
#include "robot_smart_remind.h" 

#include "robotState/RobotState.h"
#include "robotInfoCfg/read_map_point_cfg.h"
#include "robotMgr/robot_info_mgr.h"

#include <rclcpp/rclcpp.hpp>
#include <netinet/in.h>
#include <stdio.h>
#include <unistd.h>

#include <geometry_msgs/msg/twist.hpp>
#include <std_msgs/msg/string.hpp>
#include <cmath> 
#include <filesystem>
#include <fstream>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/assistant_take_photo.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>
#include <iostream>
#include <jsoncpp/json/json.h>
#include <random>
#include <string>
#include <tf2/transform_datatypes.h> // ROS 2 中的 tf2
#include <thread>
#include <vector>
#include <atomic>
#include <chrono>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <iostream>
#include <thread>
#include "xiaoli_com/xiaoli_pub_def.h"
#include <homi_com/homi_utils.hpp>
#include "robdogNode/robdog_ctrl_node.h"

#include "public/tools.h" // 为了播放表情
#include <sys/stat.h>
#include <dirent.h>
using namespace std;
using namespace WS;


void notifyWsMsgCallback(void *handle, const char *msg, int index) {
    nConnectIndex_ = index;
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "notifyWsMsgCallback: %s", msg);
    currentTimeStramp_ = base::homiUtils::getCurrentTimeStamp();

    RobdogCenter *parent = (RobdogCenter *)handle;
    Json::Reader reader;
    Json::Value value;

    if (false == reader.parse(msg, value)) {
        return;
    }
    if (!value["type"].isNull()) {
        string strType = value["type"].asString();
        if ("connect_success" == strType) {
            bConnected_ = true;
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
            Json::Value value;
            Json::Value params;
            value["client_type"] = CLIENT_LAUNCHER;
            value["action"] = "success";
            WS_Send(value.toStyledString().c_str(), nConnectIndex_);
        }
    }
    if (!value["action"].isNull()) {
        if (parent) {
            parent->parseWsActionMsg(value);
        }
    }
}


RobdogNigationCtrl::RobdogNigationCtrl() {

}

RobdogNigationCtrl::~RobdogNigationCtrl() {
    // ------------------------ WebSocket传输给感知主机 ------------------------
    Json::Value reqValue;
    reqValue["client_type"] = CLIENT_LAUNCHER;
    reqValue["target_client"] = CLIENT_NVIDIA;  
    // -------------- 关闭导航节点 ----------------
    reqValue["action"] = "navigation_control";
    Json::Value additionalParams;
    additionalParams["action"] = 0; 
    additionalParams["mapId"] = RobotState::getInstance().getMapId(); //  导航功能使用的地图
    // additionalParams["batchId"] = RobotState::getInstance().getbatchId(); // "1234"; // 多点导航任务的id(就是batchid)
    reqValue["params"] = additionalParams;
    WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "close navigation: %s", reqValue.toStyledString().c_str());

    // -------------- 关闭建图节点 ----------------
    reqValue["action"] = "mapping_control";
    Json::Value additionalParams_1;
    additionalParams_1["action"] = 2; 
    additionalParams_1["mapId"] = RobotState::getInstance().getMapId(); 
    reqValue["params"] = additionalParams_1;
    WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "close buildmap: %s", reqValue.toStyledString().c_str());
    
}

void RobdogNigationCtrl::init(RobdogCtrlNode* node) {
    nav_ctrl_ = node;
    lastMoveMessageTime = nav_ctrl_->now();

    //Yaml文件读取参数，写到形参
    std::string map_points_path = node_->getResourcePath("config/map_points.json");
    // node_->declare_parameter<string>("map_points_path", "/home/<USER>/.config/map_points.json"); 
    // map_points_path = node_->get_parameter("map_points_path").as_string();
    ReadMapCfg::getInstance().loadConfig(map_points_path); // 点位信息

    std::string robotdog_file_path = node_->getResourcePath("config/config_robotdog.xml");
    // node_->declare_parameter<string>("robotdog_file_path", "/home/<USER>/.config/config_robotdog.xml"); 
    // robotdog_file_path = node_->get_parameter("robotdog_file_path").as_string();  
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "robotdog_file_path: %s", robotdog_file_path.c_str());
    RobotState::getInstance().loadConfig(robotdog_file_path);

    node_->declare_parameter<string>("ws_connect_url", "ws://192.168.1.110:19002"); 
    strConnectUrl_ = node_->get_parameter("ws_connect_url").as_string();  
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ws_connect_url: %s", strConnectUrl_.c_str());

    node_->declare_parameter<int>("ws_connect_port", 19002); 
    uint32_t ws_port = node_->get_parameter("ws_connect_port").as_int();  
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ws_connect_port: %d", ws_port);

    // WS服务启动
    WS_Init(EN_WS_ClIENT, ws_port);
    //设置接受msg的回调函数
    WS_SetMsgCallback(notifyWsMsgCallback, this);
    WS_Connect(strConnectUrl_.c_str());

    /****************************** 感知算法交互模块 **************************/
    // 基本是websocket发过去，不直接用这里的发布器
    // actionPlanningMove_pub = node_->create_publisher<std_msgs::msg::String>(
    //     "/navigation_control", 10); // 发布机器狗固定点位坐标到感知主机
    // mappingControl_pub = node_->create_publisher<std_msgs::msg::String>(
    //     "/mapping_control", 10); // 地图更新给感知主机
    // publishVirtualWall = node_->create_publisher<std_msgs::msg::String>(
    //     "/virtual_wall_control", 10); // 虚拟墙
    // nvidiaService_client = node_->create_client<std_srvs::srv::Trigger>(
    //     "/current_task");//查询算法当前任务状态，包括建图导航漫步充电
}

// 从string转到json
Json::Value parseJson_1(const std::string& jsonString) {
    Json::CharReaderBuilder readerBuilder;
    Json::Value data;
    std::string errs;
    std::istringstream stream(jsonString);
    if (!Json::parseFromStream(readerBuilder, stream, &data, &errs)) {
        std::cerr << "JSON 解析错误: " << errs ;
    }
    return data;
}

//--------------------------------------------------------------------------- 导航相关功能 ---------------------------------------------------------------------------
//--------------------------------------------------------------------------- 导航相关功能 ---------------------------------------------------------------------------
// 有新任务到来更新点位信息
void RobdogNigationCtrl::updateMapPoints(const std::string& event, const Json::Value& points) {
    // 创建一个新的 root 对象
    Json::Value root;
    Json::Value mapping_point;

    // 根据 event 字段确定对应的点位类型
    std::string point_type;
    if (event == "deliverExpress" || event == "fetchExpress") {
        point_type = "press_point";
    } else if (event == "takePhotos") {
        point_type = "photo_point";
    } else if (event == "familyMovePoint") {
        point_type = "familyMovePoint";
    } else if (event == "parkPatrol") {
        point_type = "patrol_point";
    } else {
        std::cerr << "Unknown updateMapPoints event type: " << event << std::endl;
        return;
    }

    // 将 points 信息添加到对应的点位类型中
    mapping_point[point_type]["points"] = points;

    // 设置 root 的 mapping_point 节点
    root["mapping_point"] = mapping_point;

    // 保存修改后的 map_points.json 文件
    std::ofstream outFile(map_points_path);
    outFile << root.toStyledString();
    outFile.close();

    std::ifstream inFile(map_points_path);
    if (!inFile.is_open()) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to open file: %s", map_points_path.c_str());
        return;
    }

    std::string content((std::istreambuf_iterator<char>(inFile)), std::istreambuf_iterator<char>());
    inFile.close();

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Content of map_points.json:\n%s", content.c_str());
}

// 执行多点移动任务时需要读取点位信息
void RobdogNigationCtrl::readMappingPoints() {
    // 检查参数是否已经声明
    if (!node_->has_parameter("map_points_path")) {
        std::string map_points_path = node_->getResourcePath("config/map_points.json");
        node_->declare_parameter<std::string>("map_points_path", map_points_path);
    } else {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "参数 'map_points_path' 已经声明，跳过重新声明");
    } 
    map_points_path = node_->get_parameter("map_points_path").as_string();

    ReadMapCfg::getInstance().loadConfig(map_points_path); // 点位信息

}

//导航节点关闭超时函数
void RobdogCenter::navTimeoutHandler(const Json::Value &value,const std::string& topic_name) {
    startTimeout([this]() {
        sendInitializationResponse(false); // 超时未关闭按初始化失败处理
    }, [this, value, topic_name]() {
        response_start_time = std::chrono::steady_clock::now();
        handleEvent(value["event"].asString(), value, value["body"], topic_name); 
        mapTimeoutHandler();
    },Navigation_node, std::chrono::seconds(20));
}
//--------------------------------------------------------------------------- 建图相关功能 ---------------------------------------------------------------------------
//--------------------------------------------------------------------------- 建图相关功能 ---------------------------------------------------------------------------

// 发送建图初始化响应
void RobdogCenter::sendInitializationResponse(bool success) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "APP_DEVICE_INTERACTION";
    response["event"] = "robot_map_draw_init_response";
    response["eventId"] = getEventId();
    response["requestId"] = "requestId"+to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());

    Json::Value body;
    body["code"] = success ? 0 : 1201;
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "robot_map_draw_init_response: %s", jsonString.c_str());
    sendRequestData(jsonString);
}

void RobdogCenter::startTimeout(std::function<void()> timeoutCallback, std::function<void()> changeCallback, std::atomic<bool>& waiting_flag, std::chrono::seconds timeout_duration) {
    waiting_flag = true;
    auto start_time = std::chrono::steady_clock::now(); // 记录开始等待的时间

    // 异步执行超时处理
    std::async(std::launch::async, [this, timeoutCallback, changeCallback, start_time, &waiting_flag, timeout_duration]() {
        while (std::chrono::steady_clock::now() - start_time < timeout_duration) {
            if (!waiting_flag) {
                changeCallback(); // 调用changeCallback函数
                return; // 如果waiting_flag变为false，退出循环
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 每100毫秒检查一次
        }
        if (waiting_flag) {
            waiting_flag = false;
            timeoutCallback(); // 执行超时回调函数
        }
    });
}

//建图初始化超时函数
void RobdogCenter::mapTimeoutHandler() {
    startTimeout([this]() {
        sendInitializationResponse(false); // 超时视为失败
    }, [this]() {
        // 处理Navigation_node变为false的情况
    },waiting_for_response, std::chrono::seconds(20));
}

// 处理导航任务和其他通用动作
// ****************** event是robot_action但是没有actiontype **************************
void RobdogCenter::handleGeneralAction(const Json::Value &jBody) {
    expresstion_count = 0;
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "8959379483739289398");
    //导航任务需要切换到自主模式
    homi_speech_interface::msg::RobdogAction zzmsg;
    zzmsg.actiontype = "NavCtrl";
    zzmsg.actionargument = "AutoMode";
    // publishAction(zzmsg);
    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));

    // system("/home/<USER>/updateexpression.sh  "
    //         "/home/<USER>/resource/left_right_look.mp4");
    std::string taskType;
    if (!jBody["event"].isNull()) {
        taskType = jBody["event"].asString();
        RobotState::getInstance().setMoveTaskType(taskType);
    } else {
        RobotState::getInstance().setMoveTaskType("");
    }
    // ******************* 处理不同的事件类型 ****************
    if (taskType == "takePhotos") {
        // 处理拍照任务
        handleTakePhotos();
    } else if (taskType == "deliverExpress") {
        // 取快递
        handleDeliverExpress();
    } else if (taskType == "fetchExpress") {
        // 寄快递
        handleFetchExpress();
    } else if (taskType == "cancelMovement") {
        // 处理取消移动任务
        handleCancelMovement();
    } else {
        // 处理其他任务
        homi_speech_interface::msg::SIGCEvent move_msg;
        Json::StreamWriterBuilder writerBuilder;
        std::string moveMsgs = Json::writeString(writerBuilder, jBody);
        move_msg.event = moveMsgs;
        // moveToTarget(move_msg);
        moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(move_msg));

    }
}

void RobdogCenter::sendNavigationAction(int action,std::string mapId) {
    Json::Value reqValue;
    reqValue["client_type"] = CLIENT_LAUNCHER;
    reqValue["target_client"] = CLIENT_NVIDIA;
    reqValue["action"] = "navigation_control";

    Json::Value params;
    params["action"] = action;
    params["mapId"] = mapId;
    // std::string current_mapid = RobotState::getInstance().getMapId();
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---current_mapid: %s", __LINE__, current_mapid.c_str());
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---send mapid: %s", __LINE__, mapId.c_str());
    reqValue["params"] = params;

    // 添加 action 的值到日志中
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The message of val_env: %s, action: %d", reqValue.toStyledString().c_str(), action);
    WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
}

void RobdogCenter::sendMapAction(int action,std::string url,std::string mapId) {
    Json::Value reqValue;
    reqValue["client_type"] = CLIENT_LAUNCHER;
    reqValue["target_client"] = CLIENT_NVIDIA;
    reqValue["action"] = "mapping_control";

    Json::Value modifiedJBody;
    modifiedJBody["action"] = action;
    modifiedJBody["mapId"] = mapId;
    modifiedJBody["url"] = url;
    // std::string current_mapid = RobotState::getInstance().getMapId();
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---current_mapid: %s", __LINE__, current_mapid.c_str());
    reqValue["params"] = modifiedJBody;
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of value_map: %s,action: %d", reqValue.toStyledString().c_str(),action);
    WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
}

// ****************** event是map_draw **************************
void RobdogCenter::handleMapDraw(const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d -----map_draw", __LINE__);

    long long mapId = jBody["mapId"].asInt64();
    std::string current_mapid = std::to_string(mapId);
    std::string url = jBody["url"].asString();
    std::string action = jBody.get("action", "").asString();
    int actionInt;
    if(mapId == -1){
        RobotState::getInstance().setMapId(current_mapid);
    }


    if (action == "start") {
        actionInt = 0;
        // // 打印状态值
        // RobotStateEnum state = RobotState::getInstance().getCurrentState();
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---CurrentState: %d",__LINE__, static_cast<int>(state));
    } else if (action == "cancel") {
        actionInt = 2;
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
    } else if (action == "delete") {
        actionInt = 3;
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
    } else if (action == "complete") {
        actionInt = 1;
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
    } else {
        return ; // 处理其他情况，当前仅有暂停，直接丢弃不处理
    }

    sendMapAction(actionInt,url,current_mapid);

    // if (action == "complete") 
    // {
    //     auto start_time = std::chrono::steady_clock::now();
    //     auto timeout_duration = std::chrono::seconds(15);   //最多等待15秒后给算法平台发送开启导航消息

    //     while (map_node == true) {
    //         auto duration = std::chrono::steady_clock::now() - start_time;
    //         if (duration > timeout_duration) {
    //             RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Timeout waiting for map_node to become false");
    //             break;
    //         }
    //         // 等待一段时间再检查
    //         std::this_thread::sleep_for(std::chrono::milliseconds(100));
    //     }

    //     // 只有在 map_node 变为 false 时才发送 JSON 数据
    //     if (map_node == false) {
    //         auto duration = std::chrono::steady_clock::now() - start_time;
    //         auto seconds = std::chrono::duration_cast<std::chrono::seconds>(duration).count();
    //         // 打印时间差
    //         std::cout << "Time difference: " << seconds << " seconds" << std::endl;
    //         // 上报地图id给导航平台，开启导航
    //         reqValue["client_type"] = CLIENT_LAUNCHER;
    //         reqValue["target_client"] = CLIENT_NVIDIA;         
    //         reqValue["action"] = "navigation_control";
            
    //         // ************ 按照导航协议构造navigation_control数据 ************
    //         Json::Value value_map;
    //         value_map["action"] = 1;
    //         value_map["entityId"] = current_mapid;
    //         reqValue["params"] = value_map;
    //         RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The message of value_map complete: %s", reqValue.toStyledString().c_str());
    //         WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
    //     }
    // }
}


// ****************** event是move_points【导航任务都是从这个字段下发】 **************************
void RobdogCenter::handleMovePoints(const Json::Value &inValue) {
    Json::Value jBody = inValue["body"];
    // 处理移动点的逻辑
    //导航任务需要切换到自主模式
    homi_speech_interface::msg::RobdogAction zzmsg;
    zzmsg.actiontype = "NavCtrl";
    zzmsg.actionargument = "AutoMode";
    // publishAction(zzmsg);
    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));

    // 防止因为趴下状态而导致的无法出发
    // if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN){
    //     homi_speech_interface::msg::RobdogAction sumsg;
    //     sumsg.actiontype = "motorSkill";
    //     sumsg.actionargument = "standUp";
    //     // publishAction(zzmsg);
    //     publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(sumsg));
    // } 

    expresstion_count = 0;
    // 定点移动
    // event：throwGarbage：丢垃圾；deliverExpress：寄快递；takePhotos：拍照；welcomeHome：欢迎回家;园区巡逻(v1.8)：parkPatrol
    // system("/home/<USER>/updateexpression.sh  /home/<USER>/resource/left_right_look.mp4");
    std::string taskType;
    if (!jBody["event"].isNull()) {
        taskType = jBody["event"].asString();
        RobotState::getInstance().setMoveTaskType(taskType);
    } else {
        RobotState::getInstance().setMoveTaskType("");
    }
   
    // // ******************* 处理不同的事件类型 ****************
    if (taskType == "takePhotos") {
        // 处理拍照任务
        handleTakePhotos();
    } else if (taskType == "deliverExpress") {
        // 处理快递任务
        handleDeliverExpress();
    } else if (taskType == "fetchExpress") {
        // 寄快递
        handleFetchExpress();
    } else if (taskType == "parkPatrol") {
        // 园区巡逻
        handleParkPatrol();
    } else if (taskType == "cancelMovement") {
        // 处理取消移动任务
        handleCancelMovement();
    }  else if (taskType == "familyMovePoint") {
        // 触发导航路径上报
        std::string PathEventId = inValue["eventId"].asString();
        RobotState::getInstance().setPathEventId(PathEventId);
        if (!jBody["mapId"].isNull()) {
            repositioning = true;
            std::string current_mapid = jBody["mapId"].asString();
            checkNvidiaServiceStatus(true,current_mapid);
            std::thread([this,taskType, inValue, jBody]() {
                std::unique_lock<std::mutex> lock(resultMutex);
                auto timeout = std::chrono::milliseconds(5000); 
                if (resultCV.wait_for(lock, timeout, [this] { return repositioningResult.load(); })) {
                    if (repositioningResult.load()) {
                        handleReportFamilyMovePoint();
                    } else {
                        // 条件不满足，直接返回
                        return;
                    }
                } else {
                    // 超时处理
                    RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Timeout waiting for repositioning result!");
                    return;
                }
            }).detach();
        } else {
            // 处理其他任务
            homi_speech_interface::msg::SIGCEvent move_msg;
            Json::StreamWriterBuilder writerBuilder;
            std::string moveMsgs = Json::writeString(writerBuilder, jBody);
            move_msg.event = moveMsgs;
            // moveToTarget(move_msg);
            moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(move_msg));
            
        }
    }
}


// ****************** event是point_report **************************
void RobdogCenter::handlePointReport(const Json::Value &jBody) {
    int type = 0;
    int interval = 0;
    if(!jBody["type"].isNull() && !jBody["interval"].isNull()){
        type = jBody["type"].asInt();
        interval = jBody["interval"].asInt();
    }
    else{
        return;
    }
    if (type == 1) {
        // 上报点位时开启导航
        // Json::Value reqValue;
        // reqValue["client_type"] = CLIENT_LAUNCHER;
        // reqValue["target_client"] = CLIENT_NVIDIA;         
        // reqValue["action"] = "navigation_control";
        
        // Json::Value value_startnav;
        // value_startnav["action"] = 1;
        // long long mapId = jBody["mapId"].asInt64();
        // std::string current_mapid = std::to_string(mapId);
        // value_startnav["mapId"] = current_mapid;
        // reqValue["params"] = value_startnav;
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of value_startnav: %s", reqValue.toStyledString().c_str());
        // WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
        
        // 处理点报告的逻辑
        RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "cmd: point_report");
        robPoseStatusTimer_->cancel();
        auto new_duration = std::chrono::milliseconds(interval);
        robPoseStatusTimer_ = node_->create_wall_timer(new_duration, 
            std::bind(&RobdogCenter::timerRobotPoseCallback, this));
    } else if (type == 2) { // 取消点位上报
        // 关闭导航
        // Json::Value reqValue;
        // reqValue["client_type"] = CLIENT_LAUNCHER;
        // reqValue["target_client"] = CLIENT_NVIDIA;    
        // reqValue["action"] = "navigation_control";
        // Json::Value additionalParams;
        // additionalParams["action"] = 0; 
        // // additionalParams["entityId"] = RobotState::getInstance().getMapId(); //  导航功能使用的地图
        // // 优先使用APP下发的mapid
        // long long mapId = jBody["mapId"].asInt64();
        // std::string current_mapid = std::to_string(mapId);
        // additionalParams["mapId"] = current_mapid;

        // reqValue["params"] = additionalParams;
        // WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "close navigation: %s", reqValue.toStyledString().c_str());

        robPoseStatusTimer_->cancel();  // 停止点位上报
    }
}


// ****************** event是navigation_notify **************************
void RobdogCenter::handleNavigationNotify(const Json::Value &jBody) {
    // 处理导航通知的逻辑（重定位请求）
    int type = jBody["type"].asInt();
    if (type == 0) {
        Json::Value modifiedBody = jBody;   // jBody本身是不能修改的
        
        Json::Value pointsArray = Json::Value(Json::arrayValue);
        pointsArray.append(-1);
        modifiedBody["points"] = pointsArray;
        Json::StreamWriterBuilder writerBuilder;
        std::string navCtrlMsgs = Json::writeString(writerBuilder, modifiedBody);
        std_msgs::msg::String nav_ctrl_msg;
        nav_ctrl_msg.data = navCtrlMsgs;
        actionPlanningMove_pub->publish(nav_ctrl_msg);

        //WebSocket传输给感知主机
        Json::Value reqValue;
        reqValue["client_type"] = CLIENT_LAUNCHER;
        reqValue["target_client"] = CLIENT_NVIDIA;
        reqValue["action"] = "navigation_control";
        reqValue["params"] = modifiedBody;
        WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);

    }
}


int  RobdogCenter::handleNavigationCheckStatus() {
    //TODO  
    float originLatitude = getLatitude();
    float originLongitude = getLongitude();
    if (originLatitude == 0 || originLongitude == 0){
        return CHECK_RESULT_FAIL2;
    }
    // int ret = getTripErrorCode();
    // return ret;
    return CHECK_RESULT_OK;
}

void RobdogCenter::handleNavigationRequest(const Json::Value &inValue) {

    Json::Value jBody = inValue["body"];
    setEventId(inValue["eventId"].asString());
    int type = jBody["type"].asInt();
    // setTripId(jBody["tripId"].asInt64());
    setUserPhone(jBody["userPhone"].asString());

    float originLatitude = getLatitude();
    float originLongitude = getLongitude();
    Json::Value body;
    body["tripId"] = jBody["tripId"];

    Json::Value response = inValue;
    response["event"] = "navigation_response";
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";

    int checkStatus = handleNavigationCheckStatus();
    body["checkResult"] = checkStatus;
    if(checkStatus == CHECK_RESULT_OK){
        
        setTripErrorCode(CHECK_RESULT_OK);
        Json::Value all_json_array = getGaoDeNavigation(originLatitude, originLongitude, jBody["destination"]["lat"].asFloat(),jBody["destination"]["lon"].asFloat());

        body["navigationPath"] = all_json_array;
        // TODO:
        int distance = getDistance();
        int battery = static_cast<int>(RobotInfoMgr::getInstance().getBatteryLevel());
        body["distance"] = distance;     //剩余距离
        body["duration"] = getDuration();     //剩余时间
        body["power"] = battery;
        body["estimatedCostPower"] = static_cast<int>(distance*100/1500);
        body["maxDistance"] = 2500;
    }

    response["body"] = body;
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "handleNavigationRequest : %s", jsonString.c_str());
    sendRequestData(jsonString);

}

void RobdogCenter::handleMapReposition(const Json::Value &jBody){
    std::string mapId = jBody["mapId"].asString();
    std::string current_mapid = RobotState::getInstance().getMapId();
    if(mapId != current_mapid){
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d----handleMapReposition mapId %s is not the same as current mapId %s", __LINE__,mapId.c_str(), current_mapid.c_str());
        RobotState::getInstance().setMapId(mapId);
        RobotState::getInstance().saveConfig();
    }
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleMapReposition mapId:%s",mapId.c_str());
    repositioning = true;
    checkNvidiaServiceStatus(true,mapId);//导航没开则先打开导航
}


void RobdogCenter::timerRobotPoseCallback() {
    Json::Value body = RobotState::getInstance().getCurRobotPose();
    if (body.isNull()) {
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "timerRobotPoseCallback: no position"); //【发的太频繁先注释】
        return;
    }
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "timerRobotPoseCallback: has position");
    
    // ------------- 上报点位信息 ----------------------
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_INTERACTION";
    response["event"] = "point_report";
    response["eventId"] = "robdog_plat_" + to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = 0;

    Json::Value currentPoint;
    currentPoint["x"] = body["x"].asDouble();
    currentPoint["y"] = body["y"].asDouble();
    currentPoint["angle"] = body["angle"].asDouble();
    response["body"] = currentPoint;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "point report cmd : %s", jsonString.c_str());

    // 调用服务并处理响应【给平台上报点位信息】
    sendRequestData(jsonString);   
    if(!Navigation_node){ //导航节点关闭后停止上报
        robPoseStatusTimer_->cancel();
    }
}

// 路径上报
void RobdogCenter::timerRobotPathCallback() {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), " --------------------- Start TimerRobotPathCallback ---------------------");
    Json::Value body = RobotState::getInstance().getCurRobotPose();
    if (body.isNull()) {
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "timerRobotPoseCallback: no position"); //【发的太频繁先注释】
        return;
    }
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "timerRobotPoseCallback: has position");
    
    // ------------- 上报路径信息 ----------------------
    // 创建输出 JSON 对象
    Json::Value outputRoot;
    // 填充基础字段
    outputRoot["deviceId"] = RobotState::getInstance().getDeviceId();
    outputRoot["domain"] = "DEVICE_INTERACTION";
    outputRoot["event"] = "move_path_report";
    outputRoot["eventId"] = RobotState::getInstance().getPathEventId(); // "robdog_plat_" + to_string(base::homiUtils::getCurrentTimeStamp());
    // 使用当前时间戳作为 seq
    std::time_t now = std::time(nullptr);
    outputRoot["seq"] = std::to_string(now);
    outputRoot["response"] = "false";
    // Json::Value body;
    // // 新增字段：导航状态以及当前路径的唯一id
    // body["status"] = params["status"];
    // body["batchId"] = params["batchId"];
    // outputRoot["body"] = body; // status和batchId默认是由感知主机上传的
    int status = 0;
    if(!body["status"].isNull()){ // status只能等于1或者2
        status = body["status"].asInt();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "status == %d", status);
    }
    // else{ // status为空的时候不再上报
    //     robPathStatusTimer_->cancel();
    //     return;
    // }
    if(status == 2){ // 导航已结束，此时没有点位信息
        Json::Value currentPoint;
        body["currentPoint"] = currentPoint;
        Json::Value pathArray(Json::arrayValue);
        body["path"] = pathArray;
        outputRoot["body"] = body; 
    }
    else if(at_target_){ // 导航任务已经完成，但是没有收到status==2，以导航状态为依据再发一次
        body["status"] = 2;
        body["batchId"] = RobotState::getInstance().getbatchId();

        Json::Value currentPoint;
        body["currentPoint"] = currentPoint;
        Json::Value pathArray(Json::arrayValue);
        body["path"] = pathArray;
        outputRoot["body"] = body; 
    }
    else{
        // 当前点 (currentPoint)
        Json::Value currentPoint;
        if(!body["x"].isNull() && !body["y"].isNull() && !body["angle"].isNull()){
            currentPoint["x"] = body["x"].asDouble();
            currentPoint["y"] = body["y"].asDouble();
            currentPoint["angle"] = body["angle"].asDouble();
        }
        body["currentPoint"] = currentPoint;

        // 移动路径 (path) 
        Json::Value pathArray(Json::arrayValue);
        if (!body["path"].isNull() && body["path"].isArray()) {
            for (const auto& pathPoint : body["path"]) {
                Json::Value pathItem;
                if (pathPoint.isMember("path_x") && pathPoint.isMember("path_y") && pathPoint.isMember("path_angle")) {
                    pathItem["x"] = pathPoint["path_x"].asDouble();
                    pathItem["y"] = pathPoint["path_y"].asDouble();
                    pathItem["angle"] = pathPoint["path_angle"].asDouble();
                    pathArray.append(pathItem);
                }
            }
        }
        body["path"] = pathArray;
        outputRoot["body"] = body;
    }
    
    Json::FastWriter writer_1;
    std::string jsonString_1 = writer_1.write(outputRoot);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "path report cmd : %s", jsonString_1.c_str());

    // 调用服务并处理响应
    sendRequestData(jsonString_1);
    
    // auto request_sigc_data_1 = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();
    // request_sigc_data_1->data = jsonString_1;  // 设置请求数据
    // auto ret_1 = platform_client->wait_for_service(std::chrono::seconds(1));
    // if(ret_1==false)
    // {
    //     RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
    // }
    // auto result_1 = platform_client->async_send_request(request_sigc_data_1, std::bind(&RobdogCenter::plat_srv_callback, this, std::placeholders::_1));   
    
    // if(!Navigation_node){ //导航节点关闭后停止上报
    //     robPathStatusTimer_->cancel();
    // }
    if(status == 2 || at_target_) robPathStatusTimer_->cancel();  
}


// 发送建图初始化响应
void RobdogCenter::sendMapCompleteResponse(int code,long mapId) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "APP_DEVICE_INTERACTION";
    response["event"] = "device_map_save_response";
    response["eventId"] = getEventId();
    response["requestId"] = "requestId"+to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());

    Json::Value body;
    body["code"] = code;
    body["mapId"] = Json::Int64(mapId);
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "device_map_save_response: %s", jsonString.c_str());
    sendRequestData(jsonString);
}

void RobdogCenter::handleMapRepositionResponse(const Json::Value &jBody) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "APP_DEVICE_INTERACTION";
    response["event"] = "map_reposition";
    response["eventId"] = "robdog_plat_" + to_string(base::homiUtils::getCurrentTimeStamp());
    response["requestId"] = "requestId"+to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";
    Json::Value msg = jBody["msg"];
    Json::Value newBody;
    int code = jBody["code"].asInt();
    code = code +base_navigation;
    if(code == NAVIGATION_CODE_RELOCALIZATION_FINISHED){
        code = 0;//如果重定位成功，响应code为0
    }
    newBody["code"] = code;
    if (msg.isMember("msg")) {
        newBody["msg"] = msg["msg"];
        newBody["currentPoint"] = msg["currentPoint"];
    } else if (msg.isMember("error")) {
        newBody["msg"] = msg["error"];
    } 

    response["body"] = newBody;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleMapRepositionResponse: %s", jsonString.c_str());
    sendRequestData(jsonString);
}

void RobdogCenter::deviceAlarmReport(int code) {
    // ---------------------------------- 告警上报 ------------------------------------------
    Json::Value alarmReport;
    Json::Value alarmBody;
    alarmReport["deviceId"] = RobotState::getInstance().getDeviceId();
    alarmReport["domain"] = "DEVICE_ALARM";
    alarmReport["event"] = "device_alarm_report";
    alarmReport["eventId"] = "robdog_alarm_" + to_string(base::homiUtils::getCurrentTimeStamp());
    alarmReport["requestId"] = "requestId" + to_string(base::homiUtils::getCurrentTimeStamp());
    alarmReport["seq"] = std::to_string(base::homiUtils::getCurrentTimeStamp());
    
    // alarmBody["notifystrategy"] = Json::arrayValue;
    // Json::Value notifyRole1;
    // notifyRole1["notifyRole"] = 1;
    // notifyRole1["notifyway"] = Json::arrayValue;
    // // notifyRole1["notifyway"].append(1);
    // notifyRole1["notifyway"].append(3);
    // alarmBody["notifystrategy"].append(notifyRole1);

    //平台需求，暂时只上报异常情况，1100-1205为建图异常，2100-2208为导航异常
    //设置告警类别、告警模块、告警级别和告警描述
    if (1100 <= code && code <= 1205)
    {
        alarmBody["alarmType"] = "建图告警"; 
        alarmBody["launcherModel"] = "map_status";
        code = code + base_map;
        alarmBody["alarmCode"] = code;
        alarmBody["alarmName"] = getCodeName(code); 
        alarmBody["alarmLevel"] = 4;
        alarmBody["alarmDesc"] = getDescription(code); 
    }
    else if (2100 <= code && code <= 2208)
    {
        alarmBody["alarmType"] = "导航告警"; 
        alarmBody["launcherModel"] = "navigation_status"; 
        code = code + base_navigation;
        alarmBody["alarmCode"] = code;
        alarmBody["alarmName"] = getCodeName(code); 
        alarmBody["alarmLevel"] = 4;
        alarmBody["alarmDesc"] = getDescription(code); 
    }
    else
    {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "code = %d,code is not in the range",code);
        return;
    }

    alarmReport["body"] = alarmBody;
    Json::FastWriter writer;
    std::string alarmJsonString = writer.write(alarmReport);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "alarmJsonString : %s", alarmJsonString.c_str());
    sendRequestData(alarmJsonString);
}

void RobdogCenter::processRepositioningResult(int taskStatusCode, const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Starting processRepositioningResult at %ld", base::homiUtils::getCurrentTimeStamp());
    // 使用 unique_lock 锁定 resultMutex
    std::unique_lock<std::mutex> lock(resultMutex);
    
    if (repositioning && taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_FINISHED) {
        std::string file_path = node_->getResourcePath("audio/nav/reposition_success.wav");
        repositioningResult.store(true); // 设置重定位成功
        playAudio(file_path);
        handleMapRepositionResponse(jBody);
        repositioning = false;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "repositioning is true ");
    } else if (repositioning && 
               (taskStatusCode == NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION || 
                taskStatusCode == NAVIGATION_CODE_MAP_ID_MISMATCH || 
                taskStatusCode == NAVIGATION_CODE_COMMAND_FAILED || 
                taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE)) {
                    std::string file_path = node_->getResourcePath("audio/nav/reposition_failed.wav");
        repositioningResult.store(false); // 设置重定位失败
        playAudio(file_path);
        handleMapRepositionResponse(jBody);
        repositioning = false;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "repositioning is false ");
    }

    resultCV.notify_all(); // 通知等待的线程
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ending processRepositioningResult at %ld", base::homiUtils::getCurrentTimeStamp());
}


// ************************************************ 和感知主机相关的操作(导航) ************************************************
//处理webSocket消息（按照topic名称处理）
void RobdogCenter::parseWsActionMsg(Json::Value& value) {
    string action = value["action"].asString();
    //感知主机下发的机器人移动的角速度和线速度
    if (action == "motionArcWithObstacles") {
        Json::Value params = value["params"];
        double lineSpeed = params["lineSpeed"].asDouble();
        double angularSpeed = params["angularSpeed"].asDouble();
        geometry_msgs::msg::Twist twist;
        twist.linear.x = lineSpeed;
        twist.angular.z = angularSpeed;
        publishVelocity(std::make_shared<geometry_msgs::msg::Twist>(twist));
    }
    else if (action == "navigation_position") { // 感知主机需要上报给平台的导航路径信息
        Json::Value params = value["params"];
        if (params.isMember("mode")) {
            if (params["mode"].isInt()) {
                counterFreq++;
                if (counterFreq >= RAW_FREQUENCY||params["status"].asInt()==FINISH){
                    handleStripPathReport(params);
                    counterFreq=0;
                }
                return;
            }
        }
        if (!params["x"].isNull()) {
            Navigation_node = true; // 有路径上报，说明当前导航节点是开启的
            // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation_node is active!!!!!!!!");
        }
        RobotState::getInstance().setCurRobotPose(params); // 里面就包含了点位和路径
        // 接受感知主机上报的导航位置以及路径规划信息
        
    }
    else if (action == "playTts"){
        std::string textPlay= value["params"]["text"].asString();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "send String To Brocast,content is %s",textPlay.c_str());   
        sendStringToBrocast(textPlay);
    }
    else if (action == "console_status") {
        int consoleStatusCode = value["params"]["code"].asInt();
        if(consoleStatusCode == 100 && !navHeartbeat){
            std::string current_mapid = RobotState::getInstance().getMapId();
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---current_mapid: %s", __LINE__, current_mapid.c_str());
            checkNvidiaServiceStatus(true,current_mapid);
            navHeartbeat = true;
        }
    }
    else if (action == "task_status") {   // 和导航任务状态相关的部分
        Json::Value params = value["params"];
        Json::FastWriter writer;
        std::string paramsStr = writer.write(params);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "params: %s", paramsStr.c_str());
        if (value["params"].isMember("type")) {
            int type = value["params"]["type"].asInt();
        }
        if (!value["params"]["msg"].isObject()) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "msg is not a JSON object");
            return;
        }
        Json::Value msgValue = value["params"]["msg"];
         if (msgValue.isMember("uid")) {
            uid = msgValue["uid"].asString(); // 赋值给成员变量 uid
        }
        //对code进行处理
        int taskStatusCode = value["params"]["code"].asInt();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "task_status code = %d",taskStatusCode);
         //平台需求，暂时只上报异常情况，1100-1205为建图异常，2100-2208为导航异常
         if (1000 <= taskStatusCode && taskStatusCode <= 1205)
         {
             deviceAlarmReport(taskStatusCode);
             taskStatusCode = taskStatusCode + base_map;
             if(taskStatusCode == MAP_CODE_STOPPED)
             {
                int taskStatusCode = 0;
                if (msgValue.isMember("mapId")) {
                    std::string current_mapid = msgValue["mapId"].asString(); 
                    long mapId = std::stol(current_mapid);
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Converted mapId: %ld", mapId);
                    sendMapCompleteResponse(taskStatusCode,mapId);
                    // 收到算法的完成消息才更新 mapId 
                    RobotState::getInstance().setMapId(current_mapid);
                    RobotState::getInstance().saveConfig();
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---current_mapid: %s", __LINE__, current_mapid.c_str());
                }
             }
             if(taskStatusCode == ERROR_CODE_MAP_SAVE_FAILED || taskStatusCode == ERROR_CODE_MAP_ID_MISMATCH)
             {
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d----------- code = %d",__LINE__,taskStatusCode);
                if (msgValue.isMember("mapId")) {
                    std::string current_mapid = msgValue["mapId"].asString(); 
                    long mapId = std::stol(current_mapid);
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Converted mapId: %ld", mapId);
                    sendMapCompleteResponse(taskStatusCode,mapId);
                }
             }
         }
         else if (2000 <= taskStatusCode && taskStatusCode <= 2209)
         {
            deviceAlarmReport(taskStatusCode);
            taskStatusCode = taskStatusCode + base_navigation;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d-------taskStatusCode = %d",__LINE__,taskStatusCode);
         }
         else{
             RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "taskStatusCode = %d,taskStatusCode is not in the range",taskStatusCode);
             // return ;
         }
         
         auto duration = std::chrono::steady_clock::now() - response_start_time;
         if (waiting_for_response && duration <= timeout_duration) {
             if (taskStatusCode == MAP_CODE_STARTED) {
                 sendInitializationResponse(true); // 初始化成功
                 RobotState::getInstance().setCurrentState(RobotStateEnum::MAPPING);
             } else {
                 sendInitializationResponse(false); // 初始化失败
                 RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL);
             }
             waiting_for_response = false;
         }

        // ---------------------------------- 给平台的导航状态上报 ------------------------------------------
        Json::Value response;
        Json::Value body;
        int status = mapCodeToStatus(taskStatusCode);
        if(status < NAVIGATION_STATUS_SINGLE_POINT_NAVIGATION_COMPLETED){ // 0~3
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"mapCodeToStatus status == %d", status);
            body["status"] = status;
            response["deviceId"] = RobotState::getInstance().getDeviceId();
            response["domain"] = "DEVICE_INTERACTION";
            response["event"] = "navigation_report";
            std::string eventid_cur = RobotState::getInstance().getPathEventId();
            if(eventid_cur.empty())
                response["eventId"] = "robdog_plat_" + to_string(base::homiUtils::getCurrentTimeStamp());
            else 
                response["eventId"] = RobotState::getInstance().getPathEventId(); 
            response["seq"] = Json::Int64(base::homiUtils::getCurrentTimeStamp());
            response["body"] = body;
            Json::FastWriter writer;
            std::string jsonString = writer.write(response);
            // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "cmd : %s", jsonString.c_str());
            sendRequestData(jsonString);
        }
        // ------------------------------------------------------------------------------------
        // 判断是否到达单个点位
        if (taskStatusCode == NAVIGATION_CODE_SINGLE_POINT_NAVIGATION_COMPLETED) {
            atSinglePoint = true; // 表示已到达单个点位
        }
        // 判断导航任务是否取消
        if (taskStatusCode == NAVIGATION_CODE_NAVIGATION_TASK_CANCELED) {
            moveCancel_ = true;
        }
        // if(taskStatusCode == MAP_CODE_STARTED)
        // {
        //     map_node = true;
        // }
        //建图节点关闭，打开导航
        if(taskStatusCode == MAP_CODE_STOPPED || taskStatusCode == MAP_CODE_CANCELLED)
        {
            std::string current_mapid = RobotState::getInstance().getMapId();
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---current_mapid: %s", __LINE__, current_mapid.c_str());
            sendNavigationAction(1,current_mapid);
        }
        
        // 导航节点是否开启
        if(taskStatusCode == NAVIGATION_CODE_NODE_STARTED){
            Navigation_node = true;
        }
        if(taskStatusCode == NAVIGATION_CODE_NODE_STOPPED){
            Navigation_node = false;
        }

        //重定位之前关闭导航响应处理
        if (repositioning&&taskStatusCode == NAVIGATION_CODE_NODE_STOPPED) {
            std::string current_mapid = RobotState::getInstance().getMapId();
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Opening navigation with New mapId: %s", current_mapid.c_str());
            sendNavigationAction(1, current_mapid);
        }
        //重定位之前打开导航响应处理
        if (repositioning&&taskStatusCode == NAVIGATION_CODE_NODE_STARTED) { //导航节点开启成功
            std::string current_mapid = RobotState::getInstance().getMapId();
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation node started, sending navigation action 11 with Map ID: %s", current_mapid.c_str());
            sendNavigationAction(11, current_mapid);
        } else if(repositioning && //导航节点开启失败
            (taskStatusCode == NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_NAVIGATION ||
                taskStatusCode == NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_MAPPING ||
                taskStatusCode == NAVIGATION_CODE_NODE_CRASHED)) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received new navigation command or node crashed, handling map reposition response.");
            handleMapRepositionResponse(params);
            repositioning = false;
        }

        //重定位响应
        if (taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_FINISHED ||
            taskStatusCode == NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION || 
            taskStatusCode == NAVIGATION_CODE_MAP_ID_MISMATCH || 
            taskStatusCode == NAVIGATION_CODE_COMMAND_FAILED || 
            taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE){
            processRepositioningResult(taskStatusCode, params);
        }
        // processRepositioningResult(taskStatusCode, params);

        // if(repositioning && taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_FINISHED) {
        //     std::string file_path = "//resource/audio/reposition_success.wav";
        //     playAudio(file_path);
        //     handleMapRepositionResponse(params);
        //     repositioning = false;
        //     repositioningResult = true;
        // }
        // else if(repositioning && 
        //    (taskStatusCode == NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION || taskStatusCode == NAVIGATION_CODE_MAP_ID_MISMATCH || taskStatusCode == NAVIGATION_CODE_COMMAND_FAILED || taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE))
        // {
        //     std::string file_path = "/home/<USER>/resource/audio/reposition_failed.wav";
        //     playAudio(file_path);
        //     handleMapRepositionResponse(params);
        //     repositioning = false;
        //     repositioningResult = false;
        // }
        
        if (status == 1) { // 导航任务完成
            expresstion_count++;
            if(expresstion_count == 8)
            {
                std::string file_path = node_->getResourcePath("audio/fetchExpress2.wav");
                playAudio(file_path);
                expresstion_count = 0;
            }
            at_target_ = true;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "at_target_: %d", at_target_);
        }
    }
    else if (action == "pathPlanning")
    {
        Json::StreamWriterBuilder writer;
        std::string strParams = Json::writeString(writer, value["params"]);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "pathPlanning: %s", strParams.c_str());
        actionNavigationResponse(strParams);
    }
    else if(action == "robotPose")
    {
        actionRobdogPose(value["params"]);
    }
    else if(action == "tripAbnormal")
    {
        actionTripAbnormal(value["params"]);
    }
}

// 导航任务模块
void RobdogCenter::moveToTarget(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Prepare sent msg to nvidia.");

    std::string navCtrlMsgs = msg->event; // JSON字段
    // 打印 navCtrlMsgs
    // std::cout << "发送的 navCtrlMsgs: " << navCtrlMsgs ;
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "PThe msg will be sent to nvidia:%s ", navCtrlMsgs.c_str());
    
    Json::Reader reader;
    Json::Value value;
    reader.parse(navCtrlMsgs, value);
    Json::Value jBody = value["body"];
    string strEvent = jBody["event"].asString();
    RobotState::getInstance().setMoveTaskType(strEvent);

    // ------------------------ WebSocket传输给感知主机 ------------------------
    Json::Value reqValue;
    reqValue["client_type"] = CLIENT_LAUNCHER;
    reqValue["target_client"] = CLIENT_NVIDIA;         
    reqValue["action"] = "navigation_control";
    
    // ************ 按照导航协议构造navigation_control数据 ************
    // ------- 先发送导航开启的逻辑 [上报点位的时候已经发过了]------------
    Json::Value additionalParams;
    additionalParams["action"] = 1; // 开始导航
    // 下发的时候从本地获取
    additionalParams["mapId"] = RobotState::getInstance().getMapId();

    // additionalParams["entityId"] = "tmp6"; //  导航功能使用的地图
    uuid_t uuid;
    uuid_generate_random(uuid);  // 生成随机 UUID
    char uuid_str[37];
    uuid_unparse_lower(uuid, uuid_str); 
    additionalParams["batchId"] = uuid_str; // "1234"; // 多点导航任务的id【每次平台下发导航任务后都到这个地方来为taskid编号】
    RobotState::getInstance().setbatchId(uuid_str);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The UUID of TASK: %s", uuid_str);
    
    reqValue["params"] = additionalParams;
    // 通过 WebSocket 发送开始导航的消息
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of checkPatrolStatus: %s", reqValue.toStyledString().c_str());
    WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);  // 不需要重复发送了,以防万一还是发一下吧
    
    //  ------------需要等待导航节点已经开启的返回信号 ------------

    auto start_time = std::chrono::steady_clock::now();
    // auto timeout_duration = std::chrono::seconds(15);   //最多等待15秒后给算法平台发送开启导航消息
    auto timeout_duration = std::chrono::seconds(30);   //最多等待30秒后给算法平台发送开启导航消息

    while (Navigation_node == false) { // 等待导航节点开启才能下发定点移动请求
        auto duration = std::chrono::steady_clock::now() - start_time;
        if (duration > timeout_duration) {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Timeout waiting for Navigation_node to become true");
            break;
        }
        // 等待一段时间再检查
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // 只有在 Navigation_node 变为 true 时才发送 JSON 数据
    if (Navigation_node == true) {
        auto duration = std::chrono::steady_clock::now() - start_time;
        auto seconds = std::chrono::duration_cast<std::chrono::seconds>(duration).count();
        // 打印时间差
        std::cout << "Time difference: " << seconds << " seconds" << std::endl;
        // ----------- 下发多点导航消息 ------------------
        // 将 additionalParams 合并到 navCtrlMsgs 中
        Json::Value navCtrlMsgsJson;
        bool parsingSuccessful = reader.parse(navCtrlMsgs, navCtrlMsgsJson);
        if (!parsingSuccessful) {
            std::cerr << "Failed to parse navCtrlMsgs: " << reader.getFormattedErrorMessages();
            return;
        }
        additionalParams["action"] = 10; // 多点导航
        navCtrlMsgsJson["action"] = additionalParams["action"];
        navCtrlMsgsJson["mapId"] = additionalParams["mapId"];
        navCtrlMsgsJson["batchId"] = additionalParams["batchId"];
        reqValue["params"] = navCtrlMsgsJson;
        // 通过 WebSocket 发送多点导航的消息
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The Nvidia_node is active, and the message of checkPatrolStatus: %s", reqValue.toStyledString().c_str());
        WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
    }
}

// 园区巡逻
void RobdogCenter::moveToPatrolPoint(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
        moveToTarget(msg);
        // 创建一个定时器来检查目标状态
        std::string jsonStr = msg->event;
        Json::Reader reader;
        Json::Value jBody;
        if (reader.parse(jsonStr, jBody)) {
        // 解析成功，可以访问 jBody 中的数据
        // std::cout << "解析成功,jBody: " << jBody.toStyledString() << std::endl;
        auto callback = std::bind(&RobdogCenter::checkPatrolStatus, this, jBody);
        robPatrolStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), [callback]() { callback(); });
    } else {
        // 解析失败，输出错误信息
        std::cerr << "解析JSON字符串失败: " << reader.getFormattedErrorMessages() << std::endl;
    } 
}

void RobdogCenter::handleCancelMovement() {
    // 处理取消移动任务
    // ------------------------ WebSocket传输给感知主机 ------------------------
    Json::Value reqValue;
    reqValue["client_type"] = CLIENT_LAUNCHER;
    reqValue["target_client"] = CLIENT_NVIDIA;         
    reqValue["action"] = "navigation_control";
    
    // ************ 按照导航协议构造navigation_control数据 ************
    Json::Value value_cancel;
    value_cancel["action"] = 12;
    value_cancel["mapId"] = RobotState::getInstance().getMapId();
    reqValue["params"] = value_cancel;
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of checkPatrolStatus: %s", reqValue.toStyledString().c_str());
    WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
}

/*{
    x: 1.0,
    y: 6.0,
    angle: 0.0
}*/
void RobdogCenter::navPositionCallback(const geometry_msgs::msg::Pose::SharedPtr pos) {
    // std::string strMsg = msg->data;
    Json::Value value;
    value["x"] = pos->position.x;
    value["y"] = pos->position.y;
    // tf2::Quaternion quaternion;
    // tf2::convert(pos.orientation, quaternion);
    // 使用四元数构造函数来创建tf2::Quaternion
    tf2::Quaternion quaternion(pos->orientation.x, pos->orientation.y,
                               pos->orientation.z, pos->orientation.w);
    double roll, pitch, yaw;
    tf2::Matrix3x3(quaternion).getRPY(roll, pitch, yaw);
    yaw = yaw * 180.0 / M_PI;
    value["angle"] = yaw;
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received robot position %s",value.toStyledString().c_str());

    RobotState::getInstance().setCurRobotPose(value);
}

int RobdogCenter::mapCodeToStatus(int code) {
    // 根据 code 映射到对应的 task_status
    switch (code) {
        case NAVIGATION_CODE_RELOCALIZATION_FINISHED:
            return NAVIGATION_STATUS_RELOCATION_FINISHED; //重定位成功
        case NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED:
            return NAVIGATION_STATUS_MOVE_TASK_FINISHED;  // 导航任务完成
        case NAVIGATION_CODE_TARGET_UNREACHABLE:
            return NAVIGATION_STATUS_POINT_UNREACHABLE;  // 目标点不可达
        case NAVIGATION_CODE_CANNOT_GO:
            return NAVIGATION_STATUS_CANNOTGO;  // 无法出发
        case NAVIGATION_CODE_NAVIGATION_TASK_CANCELED:
            return NAVIGATION_STATUS_MOVEMENT_CANCEL_SUCCESS;  // 导航任务已取消
        case NAVIGATION_CODE_SINGLE_POINT_NAVIGATION_COMPLETED:
            return NAVIGATION_STATUS_SINGLE_POINT_NAVIGATION_COMPLETED; // 导航任务已完成
        default:
             // 处理未预期的代码值
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "unknown code is %d ",code);
            // return NAVIGATION_STATUS_POINT_UNREACHABLE;  // 其他情况默认为目标点不可达
            return NAVIGATION_STATUS_SINGLE_POINT_NAVIGATION_COMPLETED; // 其他情况，默认不可达会导致APP端一直报错
    }
}

std::string RobdogCenter::getCodeName(int code) {
    auto it = navigationCodeDescriptions.find(code);
    if (it != navigationCodeDescriptions.end()) {
        return std::get<0>(it->second);
    }
    return "未知状态码";
}

std::string RobdogCenter::getDescription(int code) {
    auto it = navigationCodeDescriptions.find(code);
    if (it != navigationCodeDescriptions.end()) {
        return std::get<1>(it->second);
    }
    return "未知状态码";
}

// 0-停止跟随 1-开启跟随 2-开启UWB跟随
void RobdogCenter::actionFollow(int status) {
    string strAction = 0 == status ? "endFollow"
                     : 1 == status ? "startFollow"
                     : 2 == status ? "startUwbFollow" : "endFollow";
    Json::Value value;
    Json::Value params;
    value["client_type"] = CLIENT_LAUNCHER;
    value["target_client"] = CLIENT_NVIDIA;
    value["action"] = strAction;
    WS_Send(value.toStyledString().c_str(), nConnectIndex_);
}

// ----------------------------------------- 和定点移动有关的 ----------------------------------------------
bool RobdogCenter::isAtTarget(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
    std::string strMsg = msg->event; // JSON字段
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    Json::Value jBody = value["body"];
    // 获取当前机器人位置
    Json::Value currentPose = RobotState::getInstance().getCurRobotPose();
    double curX = currentPose["x"].asDouble();
    double curY = currentPose["y"].asDouble();
    double curAngle = currentPose["angle"].asDouble();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "current pos x: %f, y: %f, theta: %f", curX, curY, curAngle);

    // 目标位置
    double targetX = 0.0;
    double targetY = 0.0;
    double targetTheta = 0.0;

    if (!jBody["points"].isNull()){
        Json::Value arrayPoints = jBody["points"];
        if (arrayPoints.isArray()) {
            for (auto it : arrayPoints) {
                Json::Value jPoint = it;
                targetX = jPoint["x"].asDouble();
                targetY = jPoint["y"].asDouble();
                targetTheta = jPoint["angle"].asDouble();
            }
        }
    }
    // 智能播报给的location
    else if (!jBody["remindLocation"].isNull()){
        Json::Value jPoint = jBody["remindLocation"];
        targetX = jPoint["x"].asDouble();
        targetY = jPoint["y"].asDouble();
        targetTheta = jPoint["angle"].asDouble();
    }
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "target pos x: %f, y: %f, theta: %f", targetX, targetY, targetTheta);
    
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "planning move pos x: %f, y: %f, theta: %f", targetX, targetY, targetTheta);
    // 比较当前位置与目标位置
    return (fabs(targetX - curX) < 0.01 && fabs(targetY - curY) < 0.01 &&
            fabs(targetTheta - curAngle) < 0.01);
}


// 园区巡逻相关
void RobdogCenter::checkPatrolStatus(const Json::Value &jBody) {
    homi_speech_interface::msg::RobdogAction msg;
    if (atSinglePoint) {
        if (jBody.isMember("points")) {
        const Json::Value& points = jBody["points"];
            for (const auto& point : points) {
                if (point.isMember("uid") && point["uid"].asString() == uid) {
                    if (point.isMember("actions")) {
                        const Json::Value& actions = point["actions"];
                        for (const auto& action : actions) {
                            int actionValue = action["action"].asInt();
                            std::string angle = action["angle"].asString();
                            if (actionValue >= 1 && actionValue <= static_cast<int>(actionVector.size())) {
                                Json::Value reqValue;
                                reqValue["client_type"] = CLIENT_LAUNCHER;
                                reqValue["target_client"] = CLIENT_NVIDIA;         
                                reqValue["action"] = "navigation_control";

                                // 暂停导航
                                Json::Value additionalParams;
                                additionalParams["action"] = 13; // 暂停导航
                                additionalParams["mapId"] = RobotState::getInstance().getMapId(); //  导航功能使用的地图
                                additionalParams["batchId"] = RobotState::getInstance().getbatchId(); // "1234"; // 多点导航任务的id(就是batchid)
                                reqValue["params"] = additionalParams;
                                // RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of checkPatrolStatus: %s", reqValue.toStyledString().c_str());
                                WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);

                                // 先旋转
                                msg.actiontype = "rotateCtl";
                                msg.actionargument = angle;
                                std::cout << "angle: " << msg.actionargument << std::endl;
                                publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
                                // 暂停时间stayDuration，等待动作完成
                                int stayDuration = point["stayDuration"].asInt();
                                std::this_thread::sleep_for(std::chrono::seconds(stayDuration));
                                // 再执行动作
                                msg.actiontype = "motorSkill";
                                msg.actionargument = actionVector[actionValue - 1];
                                std::cout << "Action: " << msg.actionargument << std::endl;
                                publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
                                // 暂停时间stayDuration，等待动作完成
                                std::this_thread::sleep_for(std::chrono::seconds(stayDuration));
                                // 继续导航
                                additionalParams["action"] = 14; 
                                reqValue["params"] = additionalParams;
                                // RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of checkPatrolStatus: %s", reqValue.toStyledString().c_str());
                                WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
                            } else {
                                msg.actionargument = "unknown";
                            }
                        }
                    }
                }
            }
        }
        atSinglePoint = false;
       
    }
     // 如果整个导航任务完成，则取消定时器
    if(at_target_){
        // 停止定时器
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "导航任务完成，取消定时器");
        robPatrolStatusTimer_->cancel(); 
        at_target_ = false; // 一次导航任务完成
    }
}


void RobdogCenter::checkNvidia_srv_callback(rclcpp::Client<std_srvs::srv::Trigger>::SharedFuture future, bool openNavigationIfClosed, std::string mapId) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Starting checkNvidia_srv_callback at %ld", base::homiUtils::getCurrentTimeStamp());
    auto response = future.get();
    if (!response->message.empty()) {
        // 解析响应消息
        Json::Value response_json;
        Json::Reader reader;
        bool parsingSuccessful = reader.parse(response->message, response_json);
        if (!parsingSuccessful) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to parse JSON response: %s", reader.getFormattedErrorMessages().c_str());
            return;
        }
        // 提取各个算法的状态信息
        bool mapping_active = response_json["mapping"]["active"].asBool();
        std::string mapping_mapId = response_json["mapping"]["mapId"].asString();
        bool navigation_active = response_json["navigation"]["active"].asBool();
        std::string navigation_mapId = response_json["navigation"]["mapId"].asString();
        bool ramble_active = response_json["ramble"]["active"].asBool();
        bool charge_active = response_json["charge"]["active"].asBool();
        std::string charge_stage = response_json["charge"]["stage"].asString();

        // 并行记录日志
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Nvidia service is running");
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Mapping active: %s, Map ID: %s", mapping_active ? "true" : "false", mapping_mapId.c_str());
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation active: %s, Map ID: %s", navigation_active ? "true" : "false", navigation_mapId.c_str());
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Ramble active: %s", ramble_active ? "true" : "false");
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Charge active: %s, Stage: %s", charge_active ? "true" : "false", charge_stage.c_str());

        // NORMAL&MAPPING状态：如果导航开着需要关闭，如果导航关闭不需要做任何处理；重定位指令：如果导航开着需要判断 mapId 与下发的 mapId 是否一致，如果一致不需要任何处理，如果不一致需要先关闭原来的导航，再开启 mapId 的导航，再下发重定位。
        if (navigation_active) {
            if (openNavigationIfClosed) {
                // 收到重定位指令
                if (navigation_mapId != mapId) {
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation is active with different Map ID, closing navigation with Map ID: %s", navigation_mapId.c_str());
                    sendNavigationAction(0, navigation_mapId);
                } else {
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation is active with the same Map ID, no action needed.");
                    sendNavigationAction(11, mapId);//导航已经开了，直接发送重定位指令
                }
            } else {
                // NORMAL&MAPPING状态，直接关闭
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation is active, closing navigation with Map ID: %s", navigation_mapId.c_str());
                sendNavigationAction(0, navigation_mapId);
            }
        } else if (openNavigationIfClosed) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation is not active, opening navigation with Plat_Map ID: %s", mapId.c_str());
            sendNavigationAction(1, mapId);
        } else {
            Navigation_node = false;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Nvidia service is not running");
        }
        if (mapping_active) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Mapping is active, closing Mapping with Map ID: %s", mapping_mapId.c_str());
            sendMapAction(2, "", mapping_mapId);
        }
    }
}

// 调用服务查询算法服务状态
void RobdogCenter::checkNvidiaServiceStatus(bool openNavigationIfClosed,std::string mapId) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Starting checkNvidiaServiceStatus at %ld", base::homiUtils::getCurrentTimeStamp());
    auto request = std::make_shared<std_srvs::srv::Trigger::Request>();
    if (!nvidiaService_client->wait_for_service(std::chrono::milliseconds(500))) { // 减少等待时间到500毫秒
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service not available after waiting");
        return;
    }
    auto future_result = nvidiaService_client->async_send_request(
        request, 
        [this,openNavigationIfClosed, mapId](rclcpp::Client<std_srvs::srv::Trigger>::SharedFuture future) {
            checkNvidia_srv_callback(future, openNavigationIfClosed, mapId);
        }
    );

    // // 等待结果（可选）
    // if (future_result.valid()) {
    //     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received response from checkNvidiaServiceStatus1 at %ld", base::homiUtils::getCurrentTimeStamp());
    // }

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received response from checkNvidiaServiceStatus2 at %ld", base::homiUtils::getCurrentTimeStamp());
    // auto result = nvidiaService_client->async_send_request(request, std::bind(&RobdogCenter::checkNvidia_srv_callback, this, std::placeholders::_1));
}

int RobdogCenter::checkTripReady(){
    // 如224001: 电量不足  224002：设备当前位置与导航时不一致 ...
    return 1;
}