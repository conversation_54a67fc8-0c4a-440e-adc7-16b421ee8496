
#include <homi_com/singleton.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>
#include <string>
#include <memory>
#include <mutex>
#include <future>
#include <shared_mutex>
class ExpressionChange: public base::singleton<ExpressionChange>  {
    friend class singleton<ExpressionChange>;
    std::string video_filename_;
    mutable std::shared_mutex callback_mutex; 
    std::future<void> fut_;
    ExpressionChange() {}
    bool is_directory(const std::string& path);
    std::string get_random_video_from_directory(const std::string& dir);
    void load_new_video(const std::string &filename);
    void async_work(const std::string& msg,const int cnt);
    long getVideoDurationInMilliseconds(const std::string& filePath);
public:
    void async_callback_work(const std::string& msg,const int cnt) ;
};
