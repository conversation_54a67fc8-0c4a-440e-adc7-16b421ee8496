#pragma once
#include <rclcpp/rclcpp.hpp>
using namespace std;

#define ROBDOG_CTRL_EVENT_MAX_LENTH        32

#define ROBDOGCTRL_ERROR_SUCCESS           0
#define ROBDOGCTRL_ERROR_FAILED            -1
#define ROBDOGCTRL_ERROR_INVALID_PARA      -2
#define ROBDOGCTRL_ERROR_INVALID_STATE     -3
#define ROBDOGCTRL_ERROR_NOT_CONNECT       -4

typedef enum 
{
    ROBDOGCTRL_GAIT_INVALID = 0,
    ROBDOGCTRL_GAIT_WALK,
    ROBDOGCTRL_GAIT_RUN,
    ROBDOGCTRL_GAIT_STAIRCLIMB,
    ROBDOGCTRL_GAIT_CLIMB,
    ROBDOGCTRL_GAIT_OBSTACLE_CROSS,
    ROBDOGCTRL_GAIT_FLATGROUND,
    ROBD<PERSON>GC<PERSON>L_GAIT_AICLASSIC,
    ROBDOGCTRL_GAIT_AINIMBLE,
    ROBDOGCTRL_GAIT_FREEBOUND,
    ROBDOGCTRL_GAIT_FREEJUMP,

    ROBDOGCTRL_GAIT_EXIT
}RobdogCtrlGait;

typedef enum 
{
    ROBDOGCTRL_MOTION_INVALID = 0,
    ROBDOGCTRL_MOTION_TWIST,
    ROBDOGCTRL_MOTION_TURNOVER,
    ROBDOGCTRL_MOTION_TWISTJUMP,
    ROBDOGCTRL_MOTION_DANCE,
    ROBDOGCTRL_MOTION_BACKFLIP,
    ROBDOGCTRL_MOTION_FINGERHEART,
    ROBDOGCTRL_MOTION_HELLO,
    ROBDOGCTRL_MOTION_TWISTASS,
    ROBDOGCTRL_MOTION_FASTSHAKEBODY,
    ROBDOGCTRL_MOTION_JUMPFORWARD,
    ROBDOGCTRL_MOTION_STRETCH,
    ROBDOGCTRL_MOTION_CHESTOUT,
    ROBDOGCTRL_MOTION_NEWYEARCALL,
    ROBDOGCTRL_MOTION_SHAKEBODY,
    ROBDOGCTRL_MOTION_HAPPY,
    ROBDOGCTRL_MOTION_LEAP,
    ROBDOGCTRL_MOTION_DANCEV2,
    ROBDOGCTRL_MOTION_WALKUPRIGHT,
    ROBDOGCTRL_MOTION_HANDSTAND
}RobdogCtrlMotion;

struct CtrlMoveData{
    char event[ROBDOG_CTRL_EVENT_MAX_LENTH];
    int32_t x;
    int32_t y;
    int32_t z;
    int32_t yaw;
    int32_t pitch;
    int32_t roll;
};

class RobDog_Ctrl_Dev
{
    public:
        virtual int32_t robdogCtrl_Init(void* ctrl_ptr_) = 0;                         // 初始化
        virtual int32_t robdogCtrl_ContinueMove(CtrlMoveData *pMoveData) = 0;      // 调整身体高度,0x21010102;前后平移,0x21010130;左右平移,0x21010131;调整偏航角,0x21010135
        virtual int32_t robdogCtrl_Move(double x, double y, double yaw)  = 0;  // 0x140,指定前后平移的速度(m/s); 0x145,指定左右平移的速度(m/s); 0x141, 指定旋转角速度(rad/s)
        virtual int32_t robdogCtrl_StopMove() = 0;                             // 调整身体高度,0x21010102;前后平移,0x21010130;左右平移,0x21010131;调整偏航角,0x21010135
        virtual int32_t robdogCtrl_StandUp() = 0;              	            // 起立/趴下,               0x21010202
        virtual int32_t robdogCtrl_GetDown() = 0;             	                // 起立/趴下,               0x21010202
        virtual int32_t robdogCtrl_Sit() = 0;       	        	            // 坐下,                   0x21010506
        virtual int32_t robdogCtrl_Locomotion(RobdogCtrlMotion motion) = 0;        // 做动作
        virtual int32_t robdogCtrl_ChangeGait(RobdogCtrlGait gait) = 0;
        virtual int32_t robdogCtrl_ManualMode() = 0;          		            // 手动模式,               0x21010C02
        virtual int32_t robdogCtrl_AutoMode() = 0;       	    	            // 自主模式,               0x21010C03
        virtual int32_t robdogCtrl_ResetZero() = 0;         		            // 回零,                   0x21010C05
        virtual int32_t robdogCtrl_MoveMode() = 0;        	    	            // 移动模式,               0x21010D06
        virtual int32_t robdogCtrl_VoiceStand(int32_t cmd) = 0;        	    // 语音指令（起立/趴下）,   0x21010C0A
        virtual int32_t robdogCtrl_AvoidClose() = 0;            	            // 关闭避障功能,           0x21012109
        virtual int32_t robdogCtrl_AvoidOpen() = 0;                            // 开启避障功能
        virtual int32_t robdogCtrl_EmergencyStop() = 0;    	    	        // 软急停,                 0x21020C0E
        virtual int32_t robdogCtrl_HeartBeat() = 0;        	    	        // 心跳,                   0x21040001
        virtual int32_t robdogCtrl_Temperature() = 0;       		            // 机器人温度信息,          0x21040002
        virtual int32_t robdogCtrl_Position(float x, float y,float radian) = 0;//                         0x31010D07
        virtual int32_t robdogCtrl_PositionAngVel() = 0;        	            //                         0x122
        virtual int32_t robdogCtrl_UserDefined(int32_t cmd) = 0;          	    // 用户定义,                  0x00000160
        virtual int32_t robdogCtrl_State() = 0;              	                // 机器人状态信息,           0x0901
};

