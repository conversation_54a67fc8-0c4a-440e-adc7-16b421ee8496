/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: ResponseHeader_.idl
  Source: ResponseHeader_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_RESPONSEHEADER__HPP
#define DDSCXX_RESPONSEHEADER__HPP

#include "RequestIdentity_.hpp"

#include "ResponseStatus_.hpp"


namespace unitree_api
{
namespace msg
{
namespace dds_
{
class ResponseHeader_
{
private:
 ::unitree_api::msg::dds_::RequestIdentity_ identity_;
 ::unitree_api::msg::dds_::ResponseStatus_ status_;

public:
  ResponseHeader_() = default;

  explicit ResponseHeader_(
    const ::unitree_api::msg::dds_::RequestIdentity_& identity,
    const ::unitree_api::msg::dds_::ResponseStatus_& status) :
    identity_(identity),
    status_(status) { }

  const ::unitree_api::msg::dds_::RequestIdentity_& identity() const { return this->identity_; }
  ::unitree_api::msg::dds_::RequestIdentity_& identity() { return this->identity_; }
  void identity(const ::unitree_api::msg::dds_::RequestIdentity_& _val_) { this->identity_ = _val_; }
  void identity(::unitree_api::msg::dds_::RequestIdentity_&& _val_) { this->identity_ = _val_; }
  const ::unitree_api::msg::dds_::ResponseStatus_& status() const { return this->status_; }
  ::unitree_api::msg::dds_::ResponseStatus_& status() { return this->status_; }
  void status(const ::unitree_api::msg::dds_::ResponseStatus_& _val_) { this->status_ = _val_; }
  void status(::unitree_api::msg::dds_::ResponseStatus_&& _val_) { this->status_ = _val_; }

  bool operator==(const ResponseHeader_& _other) const
  {
    (void) _other;
    return identity_ == _other.identity_ &&
      status_ == _other.status_;
  }

  bool operator!=(const ResponseHeader_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::unitree_api::msg::dds_::ResponseHeader_>::getTypeName()
{
  return "unitree_api::msg::dds_::ResponseHeader_";
}

template <> constexpr bool TopicTraits<::unitree_api::msg::dds_::ResponseHeader_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::ResponseHeader_>::type_map_blob_sz() { return 754; }
template<> constexpr unsigned int TopicTraits<::unitree_api::msg::dds_::ResponseHeader_>::type_info_blob_sz() { return 196; }
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::ResponseHeader_>::type_map_blob() {
  static const uint8_t blob[] = {
 0xe7,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0xf1,  0x9c,  0x43,  0xe5,  0x09,  0xd9,  0x26,  0x2e, 
 0x25,  0xe7,  0x3c,  0x21,  0xef,  0xbf,  0xee,  0x00,  0x51,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x41,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x8f,  0x78,  0x20,  0x91,  0xd8, 
 0x0b,  0x89,  0x0d,  0xad,  0xf3,  0xe7,  0xd6,  0x60,  0xe6,  0xff,  0x48,  0x3d,  0x1f,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x5b,  0x67,  0x75,  0x71,  0x30, 
 0x34,  0xdf,  0x53,  0x23,  0x66,  0x5f,  0xf9,  0x73,  0x40,  0x9a,  0xcb,  0x44,  0x54,  0xf1,  0x8f,  0x78, 
 0x20,  0x91,  0xd8,  0x0b,  0x89,  0x0d,  0xad,  0xf3,  0xe7,  0xd6,  0x60,  0xe6,  0x33,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0xb8, 
 0x0b,  0xb7,  0x74,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0xd3, 
 0x3a,  0xff,  0x5d,  0xf1,  0x5b,  0x67,  0x75,  0x71,  0x30,  0x34,  0xdf,  0x53,  0x23,  0x66,  0x5f,  0xf9, 
 0x73,  0x40,  0x00,  0x00,  0x23,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x04,  0xc1,  0x33,  0x67,  0x94,  0x00,  0x9f,  0x01,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0xf2,  0x0f,  0x90,  0xcc,  0xa0,  0xee,  0x3b,  0x25,  0x7f,  0xf8,  0x91,  0x0c, 
 0x07,  0xeb,  0x3e,  0x00,  0x95,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x30,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x61,  0x70,  0x69,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a, 
 0x52,  0x65,  0x73,  0x70,  0x6f,  0x6e,  0x73,  0x65,  0x48,  0x65,  0x61,  0x64,  0x65,  0x72,  0x5f,  0x00, 
 0x59,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x27,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0xf2,  0xf8,  0x8b,  0x48,  0x81,  0xc6,  0xcf,  0x5a,  0x29,  0xdc,  0xd6,  0x89,  0x3c,  0xdd, 
 0x22,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00,  0x69,  0x64,  0x65,  0x6e,  0x74,  0x69,  0x74,  0x79, 
 0x00,  0x00,  0x00,  0x00,  0x25,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x1a, 
 0x4c,  0x7d,  0xe4,  0xfc,  0x64,  0xee,  0x04,  0x5d,  0x30,  0x0c,  0xff,  0x55,  0xda,  0x00,  0x00,  0x00, 
 0x07,  0x00,  0x00,  0x00,  0x73,  0x74,  0x61,  0x74,  0x75,  0x73,  0x00,  0x00,  0x00,  0xf2,  0xf8,  0x8b, 
 0x48,  0x81,  0xc6,  0xcf,  0x5a,  0x29,  0xdc,  0xd6,  0x89,  0x3c,  0xdd,  0x22,  0x75,  0x00,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x31,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x29,  0x00,  0x00,  0x00, 
 0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f,  0x61,  0x70,  0x69,  0x3a,  0x3a,  0x6d,  0x73,  0x67, 
 0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x52,  0x65,  0x71,  0x75,  0x65,  0x73,  0x74,  0x49, 
 0x64,  0x65,  0x6e,  0x74,  0x69,  0x74,  0x79,  0x5f,  0x00,  0x00,  0x00,  0x00,  0x35,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x11,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x69,  0x64,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x15,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x05,  0x00,  0x07,  0x00,  0x00,  0x00,  0x61,  0x70,  0x69,  0x5f, 
 0x69,  0x64,  0x00,  0x00,  0x00,  0xf2,  0x1a,  0x4c,  0x7d,  0xe4,  0xfc,  0x64,  0xee,  0x04,  0x5d,  0x30, 
 0x0c,  0xff,  0x55,  0xda,  0x57,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x30,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x28,  0x00,  0x00,  0x00,  0x75,  0x6e,  0x69,  0x74,  0x72,  0x65,  0x65,  0x5f, 
 0x61,  0x70,  0x69,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a, 
 0x52,  0x65,  0x73,  0x70,  0x6f,  0x6e,  0x73,  0x65,  0x53,  0x74,  0x61,  0x74,  0x75,  0x73,  0x5f,  0x00, 
 0x1b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x13,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x04,  0x00,  0x05,  0x00,  0x00,  0x00,  0x63,  0x6f,  0x64,  0x65,  0x00,  0x00,  0x00,  0x00, 
 0x5e,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0xf2,  0x0f,  0x90,  0xcc,  0xa0,  0xee,  0x3b,  0x25, 
 0x7f,  0xf8,  0x91,  0x0c,  0x07,  0xeb,  0x3e,  0xf1,  0x9c,  0x43,  0xe5,  0x09,  0xd9,  0x26,  0x2e,  0x25, 
 0xe7,  0x3c,  0x21,  0xef,  0xbf,  0xee,  0xf2,  0xf8,  0x8b,  0x48,  0x81,  0xc6,  0xcf,  0x5a,  0x29,  0xdc, 
 0xd6,  0x89,  0x3c,  0xdd,  0x22,  0xf1,  0x8f,  0x78,  0x20,  0x91,  0xd8,  0x0b,  0x89,  0x0d,  0xad,  0xf3, 
 0xe7,  0xd6,  0x60,  0xe6,  0xf2,  0x1a,  0x4c,  0x7d,  0xe4,  0xfc,  0x64,  0xee,  0x04,  0x5d,  0x30,  0x0c, 
 0xff,  0x55,  0xda,  0xf1,  0x5b,  0x67,  0x75,  0x71,  0x30,  0x34,  0xdf,  0x53,  0x23,  0x66,  0x5f,  0xf9, 
 0x73,  0x40, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::unitree_api::msg::dds_::ResponseHeader_>::type_info_blob() {
  static const uint8_t blob[] = {
 0xc0,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x9c,  0x43,  0xe5,  0x09,  0xd9,  0x26,  0x2e,  0x25,  0xe7,  0x3c,  0x21, 
 0xef,  0xbf,  0xee,  0x00,  0x55,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x8f,  0x78,  0x20,  0x91,  0xd8,  0x0b,  0x89, 
 0x0d,  0xad,  0xf3,  0xe7,  0xd6,  0x60,  0xe6,  0x00,  0x37,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x5b,  0x67,  0x75,  0x71,  0x30,  0x34,  0xdf,  0x53,  0x23,  0x66,  0x5f,  0xf9,  0x73,  0x40,  0x00, 
 0x27,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x0f,  0x90,  0xcc,  0xa0,  0xee,  0x3b,  0x25,  0x7f,  0xf8,  0x91,  0x0c, 
 0x07,  0xeb,  0x3e,  0x00,  0x99,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0xf8,  0x8b,  0x48,  0x81,  0xc6,  0xcf,  0x5a, 
 0x29,  0xdc,  0xd6,  0x89,  0x3c,  0xdd,  0x22,  0x00,  0x79,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x1a,  0x4c,  0x7d,  0xe4,  0xfc,  0x64,  0xee,  0x04,  0x5d,  0x30,  0x0c,  0xff,  0x55,  0xda,  0x00, 
 0x5b,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::unitree_api::msg::dds_::ResponseHeader_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::unitree_api::msg::dds_::ResponseHeader_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::unitree_api::msg::dds_::ResponseHeader_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::unitree_api::msg::dds_::ResponseHeader_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::unitree_api::msg::dds_::ResponseHeader_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.identity(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.status(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::unitree_api::msg::dds_::ResponseHeader_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::ResponseHeader_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::unitree_api::msg::dds_::ResponseHeader_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.identity(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.status(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::unitree_api::msg::dds_::ResponseHeader_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::ResponseHeader_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::unitree_api::msg::dds_::ResponseHeader_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.identity(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.status(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::unitree_api::msg::dds_::ResponseHeader_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::ResponseHeader_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::unitree_api::msg::dds_::ResponseHeader_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.identity(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.status(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::unitree_api::msg::dds_::ResponseHeader_& instance, bool as_key) {
  auto &props = get_type_props<::unitree_api::msg::dds_::ResponseHeader_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_RESPONSEHEADER__HPP
