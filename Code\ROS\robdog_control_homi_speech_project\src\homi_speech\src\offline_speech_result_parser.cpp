#include "offline_speech_result_parser.h"
#include <iostream>


OfflineSpeechResultParser::OfflineSpeechResultParser(nlohmann::json jsonParserRule):m_jsonParserRule(jsonParserRule)
{
}

OfflineSpeechResultParser::~OfflineSpeechResultParser()
{
}
void sssfunct();
bool OfflineSpeechResultParser::parse(const nlohmann::json &jsonStr, nlohmann::json &result)
{
    //需要添加try
    bool isSuccess = false;
    
    // for(const auto& [key,value] : m_jsonParserRule.items()){
    //     std::cout << key << ":" << value << std::endl;        
    // }
    
    for(auto it = m_jsonParserRule.begin(); it != m_jsonParserRule.end(); ++it) {
        
        //size_t size = it.value()["match"].size();
        // std::cout << it.key() << ":" << size << std::endl;

        nlohmann::json array = jsonStr["ws"];
        for (const auto& item : array) {
            
            // if (1 == size) {
            if(0==strcmp(item["slot"].get<std::string>().c_str(),it.key().c_str())) {
                isSuccess = true;
                result = it.value();
            }
            // }
            
            
            // if(0==strcmp(item["slot"].get<std::string>().c_str(),it.key().c_str())) {
            //     isSuccess = true;
            //     if (size == 1) {
            //         result = it.value()["online"].dump();
            //     } else if (3 == size){
                    
            //     }           
            // }
        }                
    }

    return isSuccess;
}