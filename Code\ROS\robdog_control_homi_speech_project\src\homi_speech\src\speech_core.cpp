#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <homi_speech_interface/msg/pcm_stream.hpp>
#include <homi_speech_interface/msg/sigc_event.hpp>
#include <homi_speech_interface/srv/sigc_data.hpp>
#include <homi_speech_interface/srv/assistant_ctrl.hpp>
#include <homi_speech_interface/msg/assistant_event.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>
#include <homi_speech_interface/srv/upload_image_url.hpp>
#include <homi_speech/def.h>
#include <homi_speech/AlsaHelper.h>
#include <homi_speech/WavHelper.h>
#include <homi_sdk/OpenAPI.h>
#include <homi_speech/json.hpp>
#include <homi_sdk/InnerAPI.h>
#include <homi_sdk/AudioStream.h>
#include <homi_sdk/SimpleSpeechApp.h>
#include <homi_sdk/ThreadPool.h>
#include <fstream>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <string>
#include <chrono>
#include <homi_speech/OfflineAsrEngine.h>
#include <homi_speech/opusEngine.h>
#include <fstream>
#include <iostream>
#include <curl/curl.h>
#include <homi_speech_interface/msg/task_status.hpp>
#include "taskStatusContainer.h"
namespace happ =  homi::app;
rclcpp::Node::SharedPtr g_speech_core_node;
std::shared_ptr<homi::app::IOpusEngine> g_opusEngine;
std::shared_ptr<homi::app::IOffineAsrEngine> g_offlineAsrEngine;
std::shared_ptr<homi::app::ISimpleSpeechApp> g_speechApp; 
HomiTaskStatusContainer g_taskStatusContainer;
struct UploadImageUrl
{
    std::mutex _mutex;
    std::condition_variable _cv;
    std::string _url;
}g_UploadImageUrlOp;
bool checkFolderExists(const std::string& filepath) {
    size_t found = filepath.find_last_of("/\\");
    if (found == std::string::npos) {
        return false;  // 没有找到路径分隔符
    }
    std::string folder = filepath.substr(0, found);
    std::ifstream file(folder);
    return file.good();
}

class INotifyInputStream
{
public:
    virtual void notifyInputStream(const std::shared_ptr<happ::AudioFrame> &frame) = 0;
};
class InputStreamPublisher
{
public:
    void registerSubscriber(const std::weak_ptr<INotifyInputStream> & subscriber)
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _subscribersTmp.push_back(subscriber);
    }
    void notifySubscribers(const std::shared_ptr<happ::AudioFrame> &frame)
    {
        {
            std::lock_guard<std::mutex> lock(_mutex);
            _subscribers.insert(_subscribers.end(),_subscribersTmp.begin(),_subscribersTmp.end());
            _subscribersTmp.clear();
        }
        _subscribers.remove_if([frame](auto& callback){
            if (auto ptr = callback.lock())
            {
                ptr->notifyInputStream(frame);
                return false;
            }
            return true;
        });
    }
private:
    std::list<std::weak_ptr<INotifyInputStream>> _subscribers;
    std::list<std::weak_ptr<INotifyInputStream>> _subscribersTmp;
    std::mutex _mutex;
} g_NetStatusListener;

void pcmStreamCallback(const homi_speech_interface::msg::PCMStream::ConstSharedPtr ptr)
{
    if(ptr==nullptr) return ;
    auto frame = std::make_shared<happ::AudioFrame>();
    if(frame==nullptr) return ;
    happ::AudioConfig config{happ::SampleFormat::PCM_S16LE,16000,1};
    frame->config = config;
    frame->data = ptr->data;
    frame->ts = std::chrono::milliseconds(ptr->ts);
    g_NetStatusListener.notifySubscribers(frame);
}

void taskStatusCallback(const homi_speech_interface::msg::TaskStatus::ConstSharedPtr ptr)
{
    if(ptr==nullptr) return ;
    RCLCPP_INFO(rclcpp::get_logger("speech_core"),"taskStatusCallback task(%s), status=(%d)",ptr->name,ptr->status);
    if(ptr->status==1)
    {
        g_taskStatusContainer.set(ptr->name,0);
    }
    else
    {
        g_taskStatusContainer.del(ptr->name);
    }
    g_speechApp->update(g_taskStatusContainer.get());
}

static void SGICDataCallback(const std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> req,
					std::shared_ptr<homi_speech_interface::srv::SIGCData::Response> res)
{
    RCLCPP_INFO(rclcpp::get_logger("speech_core"),"SGICDataCallback %s",req->data.c_str());
    auto ret = homi::inner::sendEventOOB(std::make_shared<std::string>(req->data),false,2000);
    RCLCPP_INFO(rclcpp::get_logger("speech_core"),"SGICDataCallback ret=%d",ret);
    if(ret<0)
    {
        res->error_code = -1;
    }
    else
    {
        res->error_code = 0;
    }
}

static void UploadImageUrlCallback(const std::shared_ptr<homi_speech_interface::srv::UploadImageUrl::Request> req,
					std::shared_ptr<homi_speech_interface::srv::UploadImageUrl::Response> res)
{
    RCLCPP_INFO(rclcpp::get_logger("speech_core"),"UploadImageUrl file_size = %d",req->file_size);
    {
        try
        {
            nlohmann::json obj;
            obj["skillName"] = "photo_upload";
            obj["argumentV2"]["fileSize"] = req->file_size;
            auto ret = homi::inner::sendEvent("SKILL_EXECUTE","skill_photo_upload",true,obj);
            if(ret<0)
            {
                res->error_code = -1;
                return ;
            }
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
        }
        {
            std::unique_lock<std::mutex> lock(g_UploadImageUrlOp._mutex);
            g_UploadImageUrlOp._url.clear();
            g_UploadImageUrlOp._cv.wait_for(lock,std::chrono::milliseconds(10000),[](){ return !g_UploadImageUrlOp._url.empty();});
            if(g_UploadImageUrlOp._url.empty())
            {
                res->error_code = -2;
            }
            else
            {
                res->url = g_UploadImageUrlOp._url;
                res->error_code = 0;
            }
        }
    }
}


static void IotControlCallback(const std::shared_ptr<homi_speech_interface::srv::IotControl::Request> req,
					std::shared_ptr<homi_speech_interface::srv::IotControl::Response> res)
{
    
    nlohmann::json obj;
    // nlohmann::json controlParams;
    using json = nlohmann::json;
    try
    {    
        obj["skillName"] = "iot_control";
        nlohmann::json argumentV2;
        argumentV2["targetDeviceId"] = "CMCC-591022-34A6EF828EA8";
        json parsedJson = json::parse(req->param);
        int outletStatusValue = parsedJson.value("outletStatus", 1);
        std::vector<std::unordered_map<std::string, std::string>> controlParams;
        controlParams.push_back({{"name", "outletStatus"}, {"value", std::to_string(outletStatusValue)}});
        // controlParams.push_back({{"name", "outletStatus"}, {"value", "1"}});
        argumentV2["controlParams"] = controlParams;
        obj["argumentV2"] = argumentV2;
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"IotControlCallback:%s",obj.dump().c_str());
    }
    catch(const std::exception& e)
    {
        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
    }
    // 按照srv.request.param处理逻辑
    // nlohmann::json obj;
    // nlohmann::json parsedJson = nlohmann::json::parse(req.param);
    // obj.update(parsedJson);
    auto ret = homi::inner::sendEvent("SKILL_EXECUTE","skill_iot_control",true,obj);
    if(ret<0)
    {
        res->error_code = -1;
    }
}

class TimeoutChecker {
public:
    // 构造函数，接收一个超时时间（秒为单位）
    TimeoutChecker(int timeoutSeconds) {
        // 记录超时的时间点
        timeoutPoint = std::chrono::system_clock::now() + std::chrono::seconds(timeoutSeconds);
    }

    // 检查是否超时
    bool isTimeout() const {
        return std::chrono::system_clock::now() >= timeoutPoint;
    }

private:
    std::chrono::time_point<std::chrono::system_clock> timeoutPoint; // 超时的时间点
};
class VoicePrint : public INotifyInputStream
{
public:
    VoicePrint(const std::string &fileName,const std::string &shellPath):m_fileName(fileName),m_shellPath(shellPath)
    {
        
    }
    virtual void notifyInputStream(const std::shared_ptr<happ::AudioFrame> &frame) override
    {
        if(frame&&frame->data.size()>0)
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            try
            {
                if(outFile.is_open()&&m_timeoutChecker)
                {
                    if(!m_timeoutChecker->isTimeout())
                        outFile.write((char*)frame->data.data(),frame->data.size());
                    else
                    {
                        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"notifyInputStream: timeout ");
                        outFile.close();
                        if(m_timeoutChecker) m_timeoutChecker.reset();
                        isCancel = true;
                    }
                        
                }
            }
            catch(const std::exception& e)
            {
                if(outFile.is_open())outFile.close();
                if(m_timeoutChecker) m_timeoutChecker.reset();
                RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"VoicePrint::%s",e.what());
            }
        }
    }
    int start(const std::string &url,int timeout)
    {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"VoicePrint::start (url:%s,timeout:%d)",url.c_str(),timeout);
        std::lock_guard<std::mutex> lock(m_mutex);
        if(timeout<0)timeout = -timeout;
        if(timeout>60*10)timeout = 60*10;
        try
        {
            if(outFile.is_open())outFile.close();
            outFile.open(m_fileName,std::ios::binary | std::ios::out | std::ios::trunc);
            if(outFile.fail()) return -1;
            m_timeoutChecker = std::make_shared<TimeoutChecker>(timeout);
            m_url = url;
            isCancel = false;
        }
        catch(const std::exception& e)
        {
            if(outFile.is_open())outFile.close();
            return -1;
        }
        return 0;
    }
    int cancel()
    {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"VoicePrint::cancel");
        std::lock_guard<std::mutex> lock(m_mutex);
        if(outFile.is_open())outFile.close();
        if(m_timeoutChecker) m_timeoutChecker.reset();
        isCancel = true;
        return 0;
    }
    int finish()
    {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"VoicePrint::finish");
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            if(outFile.is_open())outFile.close();
            if(m_timeoutChecker) m_timeoutChecker.reset();
            if(isCancel)return -1;
        }
        return _uploadFile(m_shellPath,m_fileName,m_url);
    }
    bool isBusy()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        if(isCancel)return false;
        if(!m_timeoutChecker)return false;
        return !m_timeoutChecker->isTimeout();
    }
private:

    int _uploadFile(const std::string m_shellPath,const std::string &fileName,const std::string &url)
    {
        const std::string cmd = m_shellPath + " \"" + url + "\" " + fileName + " & ";
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"VoicePrint::_uploadFile %s",cmd.c_str());
        return std::system(cmd.c_str());
    }
    
    std::mutex m_mutex;
    const std::string m_fileName;
    const std::string m_shellPath;
    std::string m_url;
    std::ofstream outFile;
    std::shared_ptr<TimeoutChecker> m_timeoutChecker;
    bool isCancel;

};
std::shared_ptr<VoicePrint> g_voicePrintPtr;
static int assistantCtrl(std::string &eventId,bool isNormal, bool multipleWheel,bool notifyUserAbort
        ,const std::string &inquiryText,const int inquirySubType
        ,bool run,const std::chrono::milliseconds& runWait
        ,bool stop,const std::chrono::milliseconds& stopWait) 
{
    int waitrun;
    std::chrono::milliseconds wait1{10000},wait2{0};
    homi::app::SimpleSpeechAppErrorCode error;
    if(g_speech_core_node->get_parameter(PARAM_SPEECH_ROUND1WAIT_MS,waitrun))
    {
        wait1 = std::chrono::milliseconds(waitrun);
    }
    if(multipleWheel)
    {
        if(g_speech_core_node->get_parameter(PARAM_SPEECH_ROUNDnWAIT_MS,waitrun))
        {
            wait2 = std::chrono::milliseconds(waitrun);
        }
        else
        {
            wait2 = std::chrono::milliseconds(10000);
        }
    }
    if(stop)
    {
        error = g_speechApp->stop(notifyUserAbort,stopWait);
        if(error != homi::app::SimpleSpeechAppErrorCode::Success)
        {
            if(error==happ::SimpleSpeechAppErrorCode::EAgain)
            {
                return 1;
            }
            else
            {
                return -1;
            }
        }
    } 
    if(run)
    {
        if(isNormal)
        {
            error = g_speechApp->runNormal(eventId,std::chrono::milliseconds(runWait),wait1,wait2);
        }
        else
        {
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"runInquiry inquiryText: %s",inquiryText.c_str());
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"runInquiry inquirySubType: %d runWait: %ld",inquirySubType,runWait);
            error = g_speechApp->runInquiry(eventId,inquiryText,inquirySubType,std::chrono::milliseconds(runWait),wait1,wait2);
        }
        
        if(error != homi::app::SimpleSpeechAppErrorCode::Success)
        {
            if(error==happ::SimpleSpeechAppErrorCode::EAgain)
            {
                return 2;
            }
            else
            {
                return -2;
            }
        }
    } 
    return 0;  
}

static void AssistantCtrlCallback(const std::shared_ptr<homi_speech_interface::srv::AssistantCtrl::Request> req,
					std::shared_ptr<homi_speech_interface::srv::AssistantCtrl::Response> res)
{
    RCLCPP_INFO(rclcpp::get_logger("speech_core"),"Assistant isNormal(%d) start(%d) stop(%d) startWaitMS(%d) stopWaitMS(%d) inquiryText(%s) inquirySubType(%d) mutipleWheels(%d) notifyUserAbort(%d)",req->is_normal,req->start,req->stop,req->start_wait_ms,req->stop_wait_ms,req->inquiry_text.c_str(),req->inquiry_sub_type,req->mutiple_wheels,req->notify_user_abort);
    if(g_voicePrintPtr&&g_voicePrintPtr->isBusy())
    {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"AssistantCtrlCallback is busy");
        res->error_code = -1;
        res->event_id = "";
        return;
    }
    
    std::string eventId;
    res->error_code = assistantCtrl(eventId,req->is_normal,req->mutiple_wheels,req->notify_user_abort,req->inquiry_text,req->inquiry_sub_type,req->start,std::chrono::milliseconds(req->start_wait_ms),req->stop,std::chrono::milliseconds(req->stop_wait_ms));
    res->event_id = eventId;
    // RCLCPP_INFO(rclcpp::get_logger("speech_core"),"AssistantCtrlCallback return");
}

const char * homiAppResourceTypehelperToString(homi::app::ResourceType type) {
            switch (type) {
                case homi::app::ResourceType::Wakeup:          return "Wakeup";
                case homi::app::ResourceType::NetworkError:       return "NetworkError";
                case homi::app::ResourceType::DeviceInputError:  return "DeviceInputError";
                case homi::app::ResourceType::offlineInstruction:    return "offlineInstruction";
                case homi::app::ResourceType::Inquiry:        return "Inquiry";
                case homi::app::ResourceType::TimeOut:      return "TimeOut";
                case homi::app::ResourceType::offlineInstructionError:    return "offlineInstructionError";
                case homi::app::ResourceType::End:    return "End";
                default:                                  return "Unknown";
            }
}
static nlohmann::json g_ResourceFiles;
class ResStream : public homi::app::IAudioStreamInput
{
public:
    ResStream(happ::ResourceType rt,int subType)
    {
        s_count.fetch_add(1);
        _rt = rt;
        _subType = subType;
    }
    bool onInit()
    {
        std::string filePath;
        if(_subType<0) return false;
        try
        {
            auto key = g_ResourceFiles[homiAppResourceTypehelperToString(_rt)];
            if((int)(key.size())<_subType) return false;
            auto index = _subType==0?rand()%key.size():_subType-1;
            filePath = key[index].get<std::string>();
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"parse speech resource file error:%s",e.what());
        }
        if(_wf.openFile(filePath)<0)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"failed to open file %s", filePath.c_str());
            return false;
        }        
        _config.sampleRate = _wf.getRate();
        _config.channelCount = _wf.getChannels();
        _config.format = _wf.getFormat();
        if(_config.format==happ::SampleFormat::End)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"unknown sample format %d", (int)_wf.getFormat());
            return false;
        }
        return true;
    }
    virtual happ::StreamErrorCode read(std::shared_ptr<happ::AudioFrame> &InDataPtr,const std::chrono::milliseconds &wait) override
    {
        HOMI_SPEECH_UNUSED(wait);
        InDataPtr = std::make_shared<happ::AudioFrame>();
        InDataPtr->config = _config;
        InDataPtr->data = _wf.readData(-1,1024);
        InDataPtr->ts = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
        if(InDataPtr->data.size() == 0) return happ::StreamErrorCode::EEOF;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode getAudioConfig(happ::AudioConfig & config) override
    {
        config = _config;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode close(const std::chrono::milliseconds &wait) override
    {
        HOMI_SPEECH_UNUSED(wait);
        return happ::StreamErrorCode::Success;
    }
    virtual ~ResStream() override
    {
        _wf.close();
        auto k = s_count.fetch_sub(1);
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"~ResStream %d",k-1);
    };
private:
    static std::atomic<int> s_count;
    happ::AudioConfig _config;
    homi_speech::WavHelperFromFile _wf;
    happ::ResourceType _rt;
    int _subType;
};
std::atomic<int> ResStream::s_count{0};
class InStream : public std::enable_shared_from_this<InStream>
    , public homi::app::IAudioStreamInput ,public INotifyInputStream
{
public:
    InStream(const happ::AudioConfig &config):_config(config)
    {
        s_count.fetch_add(1);  
        _inputClosed = false;  
        _isRead = false;    
    }
    bool onInit()
    {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"InStream onInit() called");
        auto ptr = std::static_pointer_cast<INotifyInputStream>(shared_from_this());
        if(ptr==nullptr) 
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"onInit std::static_pointer_cast<INotifyInputStream>(shared_from_this())==nullptr");
            return false;
        }
        g_NetStatusListener.registerSubscriber(ptr);
        return true;
    }
    virtual void notifyInputStream(const std::shared_ptr<happ::AudioFrame> &frame) override
    {
        std::unique_lock<std::mutex> lock(_mutex);
        if(_inputClosed) {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"notifyInputStream but cloded");
            return;
        }
        _frames.push(frame);
        if(_frames.size()>20) _frames.pop();
        _cv.notify_one();
    }
    void readSyn()
    {
        if(!_isRead)
        {
            _isRead = true;
            _firstReadTS=std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
        }
        while(_frames.size() > 0)
        {
            auto frame = _frames.front();
            if(frame->ts > _firstReadTS) 
            {
                break;
            }
            _frames.pop();
        }
    }
    virtual happ::StreamErrorCode read(std::shared_ptr<happ::AudioFrame> &InDataPtr,const std::chrono::milliseconds &wait) override
    {
        auto endTime = std::chrono::system_clock::now();
        if(wait>=std::chrono::milliseconds::max()/2) endTime = std::chrono::system_clock::time_point::max();
        else endTime += wait;
        std::unique_lock<std::mutex> lock(_mutex);
        while(endTime > std::chrono::system_clock::now())
        {
            _cv.wait_until(lock,endTime,[this](){return _inputClosed || _frames.size()>0;});
            if(_inputClosed) return happ::StreamErrorCode::Failure;
            readSyn();
            if(_frames.size()>0)
            {
                InDataPtr = _frames.front();
                _frames.pop();
                return happ::StreamErrorCode::Success;
            }
        }
        return happ::StreamErrorCode::EAgain;
    }
    virtual happ::StreamErrorCode getAudioConfig(happ::AudioConfig & config) override
    {
        config = _config;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode close(const std::chrono::milliseconds &wait) override
    {
        HOMI_SPEECH_UNUSED(wait);
        std::unique_lock<std::mutex> lock(_mutex);
        _inputClosed = true;
        _cv.notify_all();
        return happ::StreamErrorCode::Success;
    }
    virtual ~InStream() override
    {
        {
            std::unique_lock<std::mutex> lock(_mutex);
            _inputClosed = true;
            _cv.notify_all();
        }
        auto k = s_count.fetch_sub(1);
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"InStream::~InStream %d",k-1);
    };
private:
    static std::atomic<int> s_count;
    const happ::AudioConfig _config;
    bool _inputClosed;
    bool _isRead;
    std::chrono::milliseconds _firstReadTS;
    std::mutex _mutex;
    std::condition_variable _cv;
    std::queue<std::shared_ptr<happ::AudioFrame>> _frames;
};
std::atomic<int> InStream::s_count{0};
class OutStream : public happ::IAudioStreamOutput
{
public:
    OutStream(const happ::AudioConfig &config):_config(config)
    {
        s_count.fetch_add(1);
    }
    std::string getPlaybackDeviceName()
    {
        std::string playbackShPath;
        std::string playbackShParam;
        if(!g_speech_core_node->get_parameter(PARAM_ALSA_PLAYBACK_SH_NAME, playbackShPath) 
                ||!g_speech_core_node->get_parameter(PARAM_ALSA_PLAYBACK_SH_PARAM, playbackShParam))
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"getparam (alsa_playback_sh_name or alsa_playback_sh_param) fail");
            return "default";
        }
        try
        {
            auto paramJson = nlohmann::json::parse(playbackShParam);
            auto names = paramJson["name"];
            int cardId=-1;
            for(auto e:names)
            {
                auto nameTmp = e.get<std::string>();
                RCLCPP_INFO(rclcpp::get_logger("speech_core"),"playbackDeviceName=%s",nameTmp.c_str());
                if(nameTmp=="default")
                {
                    return "default";
                }
                cardId = homi_speech::getCardId(playbackShPath,nameTmp);
                if(cardId>=0)
                {
                    return ("plughw:" + std::to_string(cardId) + ",0");
                }
            }
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
        }
        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"no mathch playbackDeviceName");
        return "";
    }
    bool onInit()
    {
        auto name = getPlaybackDeviceName();
        if(name.empty())
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"getPlaybackDeviceName fail");
            return false;
        }
        try
        {
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"playbackDeviceName=%s",name.c_str());
            playback.openAndConfig(name.c_str(),"S16_LE",_config.channelCount,_config.sampleRate);
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
            return false;
        }
        return true;
        
    }
    virtual happ::StreamErrorCode write(std::shared_ptr<happ::AudioFrame> OutDataPtr,const std::chrono::milliseconds &wait) override
    {       
        HOMI_SPEECH_UNUSED(wait); 
        try
        {
            playback.writei((const unsigned char *)OutDataPtr->data.data(), OutDataPtr->data.size());
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
            return happ::StreamErrorCode::Failure;
        }
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode getAudioConfig(happ::AudioConfig &config) override
    {
        config = _config;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode close(bool flush,const std::chrono::milliseconds &wait) override
    {
        HOMI_SPEECH_UNUSED(wait);
        try
        {
            if(flush)
            {
                playback.drain();
            }
            playback.close();
            
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
            return happ::StreamErrorCode::Failure;
        }
        return happ::StreamErrorCode::Success;
    }
    virtual ~OutStream() override
    {
        playback.close();
        auto k = s_count.fetch_sub(1);
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"OutStream::~OutStream %d",k-1);
    };
private:
    static std::atomic<int> s_count;
    const happ::AudioConfig _config;
    homi_speech::AlsaHelperPlayback playback;
};
std::atomic<int> OutStream::s_count{0};


class StreamFactory : public happ::IStreamsFactory
{
public:
    virtual std::shared_ptr<happ::IAudioStreamInput> createAudioStreamInput(const happ::AudioConfig & config) override
    {
        auto ptr = std::make_shared<InStream>(config);
        if(ptr->onInit())
            return ptr;
        else
            return nullptr;
    }
    virtual std::shared_ptr<happ::IAudioStreamOutput> createAudioStreamOutput(const happ::AudioConfig & config) override
    {
        auto ptr = std::make_shared<OutStream>(config);
        if(ptr->onInit())
            return ptr;
        else
            return nullptr;
    }
    virtual std::shared_ptr<happ::IAudioStreamInput> createResource(const happ::ResourceType rt,const int subType) override
    {
        auto ptr = std::make_shared<ResStream>(rt,subType);
        if(ptr->onInit())
            return ptr;
        else
            return nullptr;
    }
    virtual std::shared_ptr<happ::IAudioStreamOutput> createOfflineInstruction(const std::string &eventId,const std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> &callback) override
    {
        if(g_offlineAsrEngine==nullptr) return nullptr;
        return g_offlineAsrEngine->createAudioStreamOutput(eventId,callback);
    }
    virtual std::shared_ptr<happ::IAudioDecode> createAudioDecode(const happ::AudioCodeConfig &config) override
    {
        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createAudioDecode");
        if(g_opusEngine==nullptr)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createAudioDecode:g_opusEngine==nullptr");
            return nullptr;
        } 
        return g_opusEngine->createAudioDecode(config);
    }
    virtual std::shared_ptr<happ::IAudioEncode> createAudioEncode(const happ::AudioCodeConfig &config) override
    {
        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createAudioEncode");
        if(g_opusEngine==nullptr)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createAudioEncode:g_opusEngine==nullptr");
            return nullptr;
        } 
        return g_opusEngine->createAudioEncode(config);
    }
    virtual ~StreamFactory() override
    {}
};

int main(int argc, char *argv[])
{
    setlocale(LC_ALL, "");
    rclcpp::init(argc, argv);
    g_speech_core_node = rclcpp::Node::make_shared("speech_core",rclcpp::NodeOptions().automatically_declare_parameters_from_overrides(true));
    RCLCPP_INFO(rclcpp::get_logger("speech_core"),"speech_core start up");
    std::string initParam;
    std::string logfile="";
    homi::LogLevel loglevel=homi::LogLevel::LOG_LEVEL_DEBUG;
    std::string pcmStreamTopic;
    std::string playbackShPath;
    std::string playbackShParam;
    std::string speechResourceFiles;
    std::string displayAssistantWakeupCMD;
    std::string displayAssistantIdleCMD;
    std::string expressTopic;
    std::string connectStatusTopic;
    if(!g_speech_core_node->get_parameter(PARAM_SPEECH_DISPLAY_WAKEUP_CMD, displayAssistantWakeupCMD)
        ||!g_speech_core_node->get_parameter(PARAM_SPEECH_DISPLAY_IDLE_CMD, displayAssistantIdleCMD)
        ||!g_speech_core_node->get_parameter(PARAM_EXPRESS_TOPIC, expressTopic)
        ||!g_speech_core_node->get_parameter(PARAM_SIGC_CONNECT_STATUS_TOPIC, connectStatusTopic))
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s , %s ,%s ,%s) fail",PARAM_SPEECH_DISPLAY_WAKEUP_CMD,PARAM_SPEECH_DISPLAY_IDLE_CMD,PARAM_EXPRESS_TOPIC,PARAM_SIGC_CONNECT_STATUS_TOPIC);
        return -1;        
    }
    if(!g_speech_core_node->get_parameter(PARAM_SPEECH_RESSOURCE_FILE_CONFIG, speechResourceFiles))
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_SPEECH_RESSOURCE_FILE_CONFIG);
        return -1;
    }
    try
    {
        g_ResourceFiles=nlohmann::json::parse(speechResourceFiles);
    }
    catch(const std::exception& e)
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"parse (%s) fail:%s",PARAM_SPEECH_RESSOURCE_FILE_CONFIG,e.what() );
        return -1;
    }
    
    if(!g_speech_core_node->get_parameter(PARAM_SPEECH_SDK_INITPARAM, initParam))
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_SPEECH_SDK_INITPARAM);
        return -1;
    }
    std::string sTmp;
    if(g_speech_core_node->get_parameter(PARAM_SPEECH_SDK_LOGFILE, sTmp))
    {
        if(checkFolderExists(sTmp))
        {
            logfile = sTmp;
        }
    }
    sTmp.clear();
    if(g_speech_core_node->get_parameter(PARAM_SPPECH_SDK_LOGLEVEL, sTmp))
    {
        if (sTmp=="info")
        {
            loglevel = homi::LogLevel::LOG_LEVEL_INFO;
        }
    }
    if(!g_speech_core_node->get_parameter(PARAM_PCM_STREAM_TOPIC,pcmStreamTopic))
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_PCM_STREAM_TOPIC);
        return -1;
    }
    std::string aiserJson=R"({})";;
    if(!g_speech_core_node->get_parameter(PARAM_OFFLINE_ASR_ENGINE_CONFIG,aiserJson))
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_OFFLINE_ASR_ENGINE_CONFIG);
        return -1;
    }
    std::string offlineParseRule=R"({})";
    if(!g_speech_core_node->get_parameter(PARAM_OFFLINE_PARSE_RULE_CONFIG,offlineParseRule))
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_OFFLINE_PARSE_RULE_CONFIG);
        return -1;
    } 
    RCLCPP_INFO(rclcpp::get_logger("speech_core"),"get offline asr config:\n%s \nrule=%s",aiserJson.c_str(),offlineParseRule.c_str());
    RCLCPP_INFO(rclcpp::get_logger("speech_core"),"OpenAPI init param:\n%s\nloglevel=%d\nlogfile=%s",initParam.c_str(),(int)loglevel,logfile.c_str());
    std::string myDeviceId;
    try
    {
        auto json = nlohmann::json::parse(initParam);
        myDeviceId = json["config"]["deviceId"].get<std::string>();
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"deviceId=%s",myDeviceId.c_str());
    }
    catch(const std::exception& e)
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"parse (myDeviceId) fail:%s",e.what() );
        return -1;
    }
    std::string voicePrintPCMFile;
    std::string voicePrintShFile;
    if(!g_speech_core_node->get_parameter(PARAM_SPEECH_VOICE_PRINT_PCM_FILE,voicePrintPCMFile)
        ||!g_speech_core_node->get_parameter(PARAM_SPEECH_VOICE_PRINT_SH_FILE,voicePrintShFile))
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s , %s) fail",PARAM_SPEECH_VOICE_PRINT_PCM_FILE,PARAM_SPEECH_VOICE_PRINT_SH_FILE);
    }
    g_voicePrintPtr = std::make_shared<VoicePrint>(voicePrintPCMFile,voicePrintShFile);
    g_NetStatusListener.registerSubscriber(std::static_pointer_cast<INotifyInputStream>(g_voicePrintPtr));
    auto h = homi::getOpenAPI();
    int ret; 
    ret = h->Init(initParam,loglevel,logfile);
    if(ret<0)
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"OpenAPI init fail");
        return -1;
    }

    homi::inner::regeisterUserEvent("ROBOT_BUSINESS_DEVICE","voiceprint_record",[](const std::string &json){
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"voiceprint_record : %s",json.c_str());
        if(!g_voicePrintPtr)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:voicePrintPtr is null");
            return;
        }
        try
        {
            auto obj = nlohmann::json::parse(json);
            auto action = obj["action"].get<std::string>();
            if(action=="start")
            {
                auto url = obj["url"].get<std::string>();
                auto to = obj["time"].get<int>();
                auto ret = g_voicePrintPtr->start(url,to);
                if(ret<0)
                {
                    RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:start ret=%d",ret);
                    return ;
                }
                if(g_speechApp) g_speechApp->stop(false,std::chrono::milliseconds(100));
            }else if(action=="end")
            {
                auto ret = g_voicePrintPtr->finish();
                RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:finish ret=%d",ret);
            }else if(action=="exit")
            {
                auto ret = g_voicePrintPtr->cancel();
                RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:cancel ret=%d",ret);
            }
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:%s",e.what());
        }
        
    });
    g_offlineAsrEngine = homi::app::createOfflineAsrEngine(aiserJson,offlineParseRule);
    if(g_offlineAsrEngine==nullptr)
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"createOfflineAsrEngine fail");
        return -1;
    }
    g_opusEngine = homi::app::createOpusEngine();
    if(g_opusEngine==nullptr)
    {
        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createOpusEngine fail");
    }
    ret = homi::inner::regeisterUserEvent("DEVICE_ABILITY","photo_upload",[](const std::string &json){
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"upload json=%s",json.c_str());
        try
        {
            auto obj = nlohmann::json::parse(json);
            std::unique_lock<std::mutex> lock(g_UploadImageUrlOp._mutex);
            g_UploadImageUrlOp._url = obj["url"];
            g_UploadImageUrlOp._cv.notify_one();
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
        }
    });
    if(ret<0)
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"regeisterUserEvent fail");
        return -1;
    }
    auto tPcmStream = g_speech_core_node->create_subscription<homi_speech_interface::msg::PCMStream>(pcmStreamTopic,50,&pcmStreamCallback);
    auto sSIGCDataService = g_speech_core_node->create_service<homi_speech_interface::srv::SIGCData>(PARAM_SPEECH_SIGC_DATA_SERVICE,&SGICDataCallback);

    auto sAssistantService = g_speech_core_node->create_service<homi_speech_interface::srv::AssistantCtrl>(PARAM_SPEECH_ASSISTANT_CTRL_SERVICE,&AssistantCtrlCallback);
    auto sUploadImageUrlService = g_speech_core_node->create_service<homi_speech_interface::srv::UploadImageUrl>(PARAM_SPEECH_UPLOAD_IMAGE_URL_SERVICE,&UploadImageUrlCallback);
    auto sIotControlService = g_speech_core_node->create_service<homi_speech_interface::srv::IotControl>(PARAM_SPEECH_IOT_CONTROL_SERVICE,&IotControlCallback);

    auto tSIGCEvent = g_speech_core_node->create_publisher<homi_speech_interface::msg::SIGCEvent>(PARAM_SPEECH_SIGC_EVENT_TOPIC,10);
    auto tExpressEvent = g_speech_core_node->create_publisher<std_msgs::msg::String>(expressTopic,10);
    
    auto tConnectStatusEvent = g_speech_core_node->create_publisher<std_msgs::msg::String>(connectStatusTopic,10);
    auto tTaskStatus = g_speech_core_node->create_subscription<homi_speech_interface::msg::TaskStatus>(PARAM_TASK_STATUS_TOPIC,10,&taskStatusCallback);
    homi::inner::registerSIGCNetWorkStatus([&tConnectStatusEvent](const int status){
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"network status:%d",status);
        std_msgs::msg::String msg;
        if(status==0)
        {
            msg.data = R"({"netstatus":1})";
        }
        else
        {
            msg.data = R"({"netstatus":0})";
        }
        tConnectStatusEvent->publish(msg);
    });
    homi::inner::registerDefaultEvent([&tSIGCEvent](const std::string &json){
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"inner event: %s",json.c_str());
        homi_speech_interface::msg::SIGCEvent sigcEvent;
        sigcEvent.event = json;
        tSIGCEvent->publish(sigcEvent);
    });
    auto tAssistantEvent = g_speech_core_node->create_publisher<homi_speech_interface::msg::AssistantEvent>(PARAM_SPEECH_ASSISTANT_STATUS_TOPIC,10);

    std::shared_ptr<homi::framework::ThreadPool> threadPoolPtr = std::make_shared<homi::framework::ThreadPool>(1);
    if(threadPoolPtr==nullptr)
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"create thread pool failed");
        return -1;
    }
    g_speechApp = happ::SimpleSpeechAppFactory(std::make_shared<StreamFactory>()
        ,[&tExpressEvent,&tAssistantEvent,&tSIGCEvent,myDeviceId,threadPoolPtr,displayAssistantWakeupCMD,displayAssistantIdleCMD](const happ::SimpleSpeechAppEvent event,const std::string & msg,const std::string & eventId){
        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"event: %s, msg: %s, id: %s",happ::SimpleSpeechAppEventHelper::to_string(event),msg.c_str(),eventId.c_str());
        homi_speech_interface::msg::AssistantEvent assistantEvent;
        static int s_seq = 0;
        assistantEvent.status = (int)event;
        assistantEvent.description = happ::SimpleSpeechAppEventHelper::to_string(event);
        assistantEvent.msg = msg;
        assistantEvent.section_id = eventId;
        tAssistantEvent->publish(assistantEvent);
        if(event==happ::SimpleSpeechAppEvent::OfflineInstructionMatched)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"OfflineInstructionMatched: %s",msg.c_str());
            homi_speech_interface::msg::SIGCEvent sigcEvent;
            try
            {
                auto json = nlohmann::json::parse(msg);
                json["deviceId"] = myDeviceId;
                json["eventId"] = eventId;
                json["seq"] = s_seq++;
                sigcEvent.event = json.dump();
            }
            catch(const std::exception& e)
            {
                RCLCPP_WARN(rclcpp::get_logger("speech_core"),"OfflineInstructionMatched %s",e.what());
            }
            tSIGCEvent->publish(sigcEvent);
        }else if(event==happ::SimpleSpeechAppEvent::Running)
        {
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"Running");
            // threadPoolPtr->enqueue([displayAssistantWakeupCMD](){
            //     try
            //     {
            //         std::system(displayAssistantWakeupCMD.c_str());
            //     }
            //     catch(const std::exception& e)
            //     {
            //         RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
            //     }
            // });
            std_msgs::msg::String msg;
            msg.data = displayAssistantWakeupCMD;
            tExpressEvent->publish(msg);
            
        }else if (event==happ::SimpleSpeechAppEvent::Idle)
        {
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"Idle");
            // threadPoolPtr->enqueue([displayAssistantIdleCMD](){
            //     try
            //     {
            //         std::system(displayAssistantIdleCMD.c_str());
            //     }
            //     catch(const std::exception& e)
            //     {
            //         RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
            //     }
            // });
            std_msgs::msg::String msg;
            msg.data = displayAssistantIdleCMD;
            tExpressEvent->publish(msg);
        }
        else{}
    });
    if(g_speechApp==nullptr)
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"SimpleSpeechAppFactory fail");
        return -1;
    }
    rclcpp::spin(g_speech_core_node);//回旋函数
    return 0;
}