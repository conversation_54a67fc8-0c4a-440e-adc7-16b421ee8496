#include "libWebSocket.h"
#include "robdog_hand_pos.h"
#include "robotState/RobotState.h"
#include "robotInfoCfg/read_map_point_cfg.h"
#include "robotMgr/robot_info_mgr.h"

#include <rclcpp/rclcpp.hpp>
#include <netinet/in.h>
#include <stdio.h>
#include <unistd.h>

#include <geometry_msgs/msg/twist.hpp>
#include <std_msgs/msg/string.hpp>
#include <cmath> 
#include <filesystem>
#include <fstream>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/assistant_take_photo.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>
#include <iostream>
#include <jsoncpp/json/json.h>
#include <random>
#include <string>
#include <tf2/transform_datatypes.h> // ROS 2 中的 tf2
#include <thread>
#include <vector>
#include <atomic>
#include <chrono>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <iostream>
#include <thread>
#include "xiaoli_com/xiaoli_pub_def.h"
#include <homi_com/homi_utils.hpp>


#include "public/tools.h" // 为了播放表情
#include <sys/stat.h>
#include <dirent.h>

using namespace std;
using namespace WS;


/*自主导航的步态，下发多点任务时带上
"option": {
    "even_low_speed": False,      // 平地低速
    "even_medium_speed": False,   // 平地中速
    "uneven_high_step": False,    // 越障高速
    "even_rl": False,             // 平地学习
    "uneven_rl": False            // 越障学习
}*/

// unsigned long currentTimeStramp_ = base::homiUtils::getCurrentTimeStamp();
// bool bConnected_ = false;
// string strConnectUrl_ = "ws://192.168.1.110:19002";
// int nConnectIndex_ = 0;

// void notifyWsMsgCallback(void *handle, const char *msg, int index) {
//     nConnectIndex_ = index;
//     std::cout << "notifyWsMsgCallback: " << msg << std::endl;
//     currentTimeStramp_ = base::homiUtils::getCurrentTimeStamp();

//     RobdogHandPosCtrl *parent = (RobdogHandPosCtrl *)handle;
//     Json::Reader reader;
//     Json::Value value;

//     if (false == reader.parse(msg, value)) {
//         return;
//     }
//     if (!value["type"].isNull()) {
//         string strType = value["type"].asString();
//         if ("connect_success" == strType) {
//             bConnected_ = true;
//             std::this_thread::sleep_for(std::chrono::milliseconds(20));
//             Json::Value value;
//             Json::Value params;
//             value["client_type"] = CLIENT_LAUNCHER;
//             value["action"] = "success";
//             WS_Send(value.toStyledString().c_str(), nConnectIndex_);
//         }
//     }
//     if (!value["action"].isNull()) {
//         if (parent) {
//             parent->parseWsActionMsg(value);
//         }
//     }
// }

RobdogHandPosCtrl::RobdogHandPosCtrl() {
    // ctrlNode = std::make_unique<RobdogCtrlNode>();
}

RobdogHandPosCtrl::~RobdogHandPosCtrl() {
    handPosCtrl("stop"); // 关闭手势识别算法
}

void RobdogHandPosCtrl::init(RobdogCtrlNode* node) {
    ctrl_ = node;
    lastMoveMessageTime = ctrl_->now();

    //  ******************************************** 和感知主机的通信  ******************************************** 
    //Yaml文件读取参数，写到形参
    // ctrl_->declare_parameter<string>("ws_connect_url", "ws://192.168.1.110:19002"); 
    // strConnectUrl_ = ctrl_->get_parameter("ws_connect_url").as_string();  
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ws_connect_url: %s", strConnectUrl_.c_str());

    // ctrl_->declare_parameter<int>("ws_connect_port", 19002); 
    // uint32_t ws_port = ctrl_->get_parameter("ws_connect_port").as_int();  
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ws_connect_port: %d", ws_port);

    // // WS服务启动
    // WS_Init(EN_WS_ClIENT, ws_port);
    // //设置接受msg的回调函数
    // // WS_SetMsgCallback(notifyWsMsgCallback, this);        // 暂时不用websocket
    // WS_Connect(strConnectUrl_.c_str());

    // ******************************************** 订阅和发布器 ******************************************** 

    // ******************************* 机器狗的控制相关 ********************** 
    velCmd_pub = ctrl_->create_publisher<geometry_msgs::msg::Twist>(
        "/catch_turtle/ctrl_instruct", 10); // 向狗子发布速度命令话题（robot_move）
    actionCmd_pub = ctrl_->create_publisher<homi_speech_interface::msg::RobdogAction>(
        "/catch_turtle/action_type", 1); // 向狗子发送特定运动指令消息（robot_action）
    continueMoveCmd_pub = ctrl_->create_publisher<homi_speech_interface::msg::ContinueMove>(
        "/catch_turtle/continue_move", 1); // 向狗子发送持续运动信息（robot_move）

    //********************************* 手势识别相关 ***************************
    // 接收识别到的动作
    handPosition_sub_ = ctrl_->create_subscription<std_msgs::msg::String>("/action/humanpos_result", 
        10, std::bind(&RobdogHandPosCtrl::handPositionCallback, this, std::placeholders::_1));

    // 接收算法上报的状态
    handPosStatus_sub_ = ctrl_->create_subscription<std_msgs::msg::String>("/action/humanpos_status", 
        10, std::bind(&RobdogHandPosCtrl::handPositionStatusCallback, this, std::placeholders::_1));

    // 控制识别算法的开始
    publishhandPosStart = ctrl_->create_publisher<std_msgs::msg::String>("/action/humanpos_start", 10);

    // 控制识别算法【需要调取机器狗目前的状态】
    publishhandPosCtrl = ctrl_->create_publisher<std_msgs::msg::String>("/action/humanpos_control", 10);
    
    // 智能播报服务的客户端【上传播报文本】
    brocast_client = ctrl_->create_client<homi_speech_interface::srv::AssistantSpeechText>(
        "/homi_speech/helper_assistant_speech_text_service");

    // 开启手势识别算法
    // handPosOpenStart();
    // ****************************** 定时器 **********************************
    // 定时器，1秒触发一次
    // timer_2 = ctrl_->create_wall_timer(
    //     std::chrono::seconds(1),
    //     std::bind(&RobdogHandPosCtrl::publishProperties2APP, this));

    // 每走一步之后的停顿
    timer_move = ctrl_->create_wall_timer(
        std::chrono::milliseconds(static_cast<int>(timer_interval * 1000)),
        std::bind(&RobdogHandPosCtrl::timerCallback, this));

    // 设置定时器每三分钟检查一次是否超时，三分钟没收到
    timer_stophandpos = ctrl_->create_wall_timer(
        std::chrono::minutes(3),
        std::bind(&RobdogHandPosCtrl::timerCallback_stophandpos, this));

    // robPoseStatusTimer_ = ctrl_->create_wall_timer(
    //     std::chrono::seconds(1),
    //     std::bind(&RobdogHandPosCtrl::timerRobotPoseCallback, this));

}

// 播放语音
void RobdogHandPosCtrl::playAudio(const std::string& filePath) {
    if (ctrl_){
        ctrl_->playAudio(filePath);
    }
}

// 调用服务的方式播放语音
void RobdogHandPosCtrl::sendStringToBrocast(const std::string &message) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Sending to brocast: %s", message.c_str()); // 播报文本
   
    // 创建请求消息
    auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();
    request->msg = message;

    // 调用服务并处理响应
    if (!brocast_client->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service not available after waiting");
        return;
    }

    auto ret = brocast_client->wait_for_service(std::chrono::seconds(1));
    if(ret==false)
    {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
    }
    auto result = brocast_client->async_send_request(request, std::bind(&RobdogHandPosCtrl::brocast_srv_callback, this, std::placeholders::_1));   

}

void RobdogHandPosCtrl::brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response)
{
    // 使用response的get()获取
    auto response_value = response.get();
    std::string sectionId  = response_value->section_id;
    RobotState::getInstance().setSectionId(sectionId);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received sectionId from server: %s", sectionId.c_str());

}

void RobdogHandPosCtrl::timerCallback_stophandpos()
{
    int closeCount = 0; 
    if(flag_recvHandPos){
        flag_recvHandPos = false;
    } else if (flag_openHandPos){ // 超时且算法是开启状态
        // 超过三分钟没收到消息，执行某项处理
        // RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "No message received for over 3 minutes. Performing timeout handling.");
        // handPosCtrl("stop"); // 关闭手势识别算法
        if (closeCount < 5) {  // 检查是否已经执行了5次
            handPosCtrl("stop");  // 关闭手势识别算法
            closeCount++;  // 增加计数器
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Gesture recognition algorithm stopped %d times.", closeCount);
        }
    }
}

// ******************************* 对手势识别算法的控制 ***************************************
void RobdogHandPosCtrl::handPosStart(){  // 语音交互开启手势识别算法
    Json::Value jsonData;
    jsonData["code"] = 0;
    jsonData["msg"] = "ok";
    jsonData["data"] = 1; 

    // 将 JSON 对象转换为字符串
    Json::StreamWriterBuilder writer;
    writer.settings_["indentation"] = ""; // 关键：禁用缩进和换行
    std::string jsonString = Json::writeString(writer, jsonData);

    // Json::Reader reader;
    // Json::Value value;
    // if(false == reader.parse(jsonString, value)) {
    //     return;
    // }
    // RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "publishLauncherMsg: %s", jsonString.c_str());

    // 创建消息并填充 JSON 字符串
    auto message = std_msgs::msg::String();
    // std_msgs::msg::String message;
    message.data = jsonString;

    // 发布消息
    publishhandPosStart->publish(message);
    flag_openHandPos = true;
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published JSON of handPosStart: %s", message.data.c_str());
}

// 接收感知主机上报的信息
void RobdogHandPosCtrl::handPositionStatusCallback(const std_msgs::msg::String::SharedPtr message) {    
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received message form nav: %s", message->data.c_str());
    std::string strMsg = message->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "json parse error");
        return;
    }
    // 提取信息
    int code = value["code"].asInt();
    int data = value["data"].asInt();
    if(code == 0 && data == 10){
        handPosOpenStart();
        sendStringToBrocast("雷达开启成功！");
    }
}

void RobdogHandPosCtrl::handPosOpenStart(){
    Json::Value jsonData;
    jsonData["code"] = 0;
    jsonData["msg"] = "ok";
    jsonData["data"] = 2;

    // 将 JSON 对象转换为字符串
    Json::StreamWriterBuilder writer;
    writer.settings_["indentation"] = ""; // 关键：禁用缩进和换行
    std::string jsonString = Json::writeString(writer, jsonData);

    // 创建消息并填充 JSON 字符串
    auto message = std_msgs::msg::String();
    // std_msgs::msg::String message;
    message.data = jsonString;

    // 发布消息
    publishhandPosStart->publish(message);
    flag_openHandPos = true;
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published JSON of handPosStart: %s", message.data.c_str());
}

void RobdogHandPosCtrl::handPosCtrl(std::string command_ctrl){
    Json::Value jsonData;
    jsonData["code"] = 0;
    jsonData["msg"] = "ok";
    if (command_ctrl == "stop"){
        flag_openHandPos = false;
        jsonData["data"] = 0;
    } 
    else if (command_ctrl == "pause") jsonData["data"] = 2;
    else if (command_ctrl == "continue") jsonData["data"] = 3;

    // 将 JSON 对象转换为字符串
    Json::StreamWriterBuilder writer;
    writer.settings_["indentation"] = ""; // 关键：禁用缩进和换行
    std::string jsonString = Json::writeString(writer, jsonData);

    // 创建消息并填充 JSON 字符串
    auto message = std_msgs::msg::String();
    // std_msgs::msg::String message;
    message.data = jsonString;

    // 发布消息
    publishhandPosCtrl->publish(message);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published JSON of handPosCtrl: %s", message.data.c_str());
}

// ********************************* 根据感知主机上报的识别结果做相应的处理 ***************************************
/*
1: 挥手——打招呼
2: 鼓掌——扭一扭
3: 坐下——坐下
4: 站起——站立
5: 手指左边——机器狗向左转90°再转回
6: 手指右边——机器狗向右转90°再转回
*/

// ----------------------------------- 手势识别相关操作 --------------------------------
// 接收感知主机上报的信息
void RobdogHandPosCtrl::handPositionCallback(const std_msgs::msg::String::SharedPtr message) {
    flag_recvHandPos = true; // 接收到消息
    
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received message form nav: %s", message->data.c_str());
    std::string strMsg = message->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "json parse error");
        return;
    }
    // 提取信息
    int code = value["code"].asInt();
    std::string msgs = value["msg"].asString();
    if (code == 0){ // 非异常
        // 解析 data 数组
        // const Json::Value dataArray = value["data"];
        // for (const auto& item : dataArray) {
        //     double score = item["score"].asDouble();
        //     int pos_type = item["pos_type"].asInt();
        // }

        // 暂时只读取数组的第一个值
        const Json::Value& firstItem = value["data"][0];
        double score = firstItem["score"].asDouble();
        int pos_type = firstItem["pos_type"].asInt();
        int action_interv = 0;
        if (pos_type >= 1 && pos_type <= 6) {    // 目前接收到1～6都说明有对应的动作
            // functions[pos_type-1]();  // 调用相应的函数（pos_type=0表示未定义）
            // ------------------- 1.调用原本的接口执行动作 ---------------------------
            /*
            switch (pos_type) {
                case 1: {handPositionGreeting(); action_interv = 9; break;}
                case 2: {handPositionTwistBody(); action_interv = 21; break;} // 扭身体如果时间比较长设为21，时间比较短设为7
                case 3: {handPositionSitDown(); action_interv = 4; break;}
                case 4: {handPositionStandUp(); action_interv = 4; break;}
                case 5: {handPositionLeftAndTurnback(); action_interv = 3; break;}
                case 6: {handPositionRightAndTurnback(); action_interv = 3; break;}
                default:
                    RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Unknown value: %d", pos_type);
                    return;
            }*/
            // ------------------- 1.调用新的接口执行动作 ---------------------------
            handPosCtrl("pause"); 
            
            switch (pos_type) {
                case 1: {ctrl_->handleHandPosAction(1); action_interv = 9; break;}
                case 2: {ctrl_->handleHandPosAction(2); action_interv = 21; break;} // 扭身体如果时间比较长设为21，时间比较短设为7
                case 3: {ctrl_->handleHandPosAction(3); action_interv = 4; break;}
                case 4: {ctrl_->handleHandPosAction(4); action_interv = 4; break;}
                case 5: {ctrl_->handleHandPosAction(5); action_interv = 3; break;}
                case 6: {ctrl_->handleHandPosAction(6); action_interv = 3; break;}
                default:
                    RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Unknown value: %d", pos_type);
                    return;
            }
            
            // ------------ 1.拉取状态的方式判断动作结束 ----------------
            // 创建一个定时器来检查目标状态
            // robActionStatusTimer_ = ctrl_->create_wall_timer(
            //     std::chrono::seconds(5),   // 5秒之后检查一次【动作的执行时间一般比较长】
            //     [this, pos_type]() { isActionOver(pos_type); }
            // );

            // ------------ 2.间隔固定的时延开启算法 ----------------
            robActionSuspendTimer_ = ctrl_->create_wall_timer(
                std::chrono::seconds(action_interv),   
                std::bind(&RobdogHandPosCtrl::ActionSuspend, this)
            );
            // std::this_thread::sleep_for(std::chrono::seconds(action_interv));
            // handPosCtrl("continue"); // 动作结束后开启算法       
        } else {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Gesture recognition invalid value!");
        }
    }
    else 
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handPosition message exception: %s", msgs);
}

// --------------------- 持续监视动作是否执行完毕 ---------------------
void RobdogHandPosCtrl::isActionOver(int action_id){  //******* 这里需要拉取状态以及作判断
    // 输出当前的状态
    HomiRobotStatus status = RobotInfoMgr::getInstance().getRobotStatus();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Now Action is: %d", status);
    // 目前所有状态的结束姿态都是站立力控姿态,除了坐下
    if(action_id == 3 && status == ROBDOG_STATUS_GETDOWN){ // 指令是坐下，状态也是坐下
        handPosCtrl("continue"); // 动作结束后开启算法
        // 停止定时器
        if (robActionStatusTimer_) {
            robActionStatusTimer_->cancel();
        }
        return;
    }
    if (status == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) { // 动作执行完毕【从肉眼来看并未执行完毕】
        handPosCtrl("continue"); // 动作结束后开启算法
        timer_triggered_ = false;
        // 停止定时器
        if (robActionStatusTimer_) {
            robActionStatusTimer_->cancel();
        }
        return;
    }
    // return;
}

// -----------间隔固定的时延开启算法 -----------------
void RobdogHandPosCtrl::ActionSuspend(){
    handPosCtrl("continue"); // 动作结束后开启算法
    // 停止定时器
    if (robActionSuspendTimer_) {
        robActionSuspendTimer_->cancel();
    }
}

// // ---------- 动作的具体实现 ---------------------
// void RobdogHandPosCtrl::handPositionGreeting(){
//     // 判断机器狗当前状态是否是动作可执行的初始状态
//     HomiRobotStatus status = RobotInfoMgr::getInstance().getRobotStatus();
//     if (status != ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) return;
//     handPosCtrl("pause"); 
//     // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/shake/",1); // 播放表情 心心眼
//     sendStringToBrocast("你好主人nice to meet you");
//     // 你好主人nice to meet you
//     // std::thread audioTwist(&RobdogHandPosCtrl::playAudio,this, "/home/<USER>/resource/handpos/handposGreeting");
//     // audioTwist.detach();
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Result of handPositionCallback is: Greeting");
//     publishAction("greeting");
// }
// void RobdogHandPosCtrl::handPositionTwistBody(){
//     // 判断机器狗当前状态是否是动作可执行的初始状态
//     // HomiRobotStatus status = RobotInfoMgr::getInstance().getRobotStatus();
//     // if (status != ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) return;
//     // handPosCtrl("pause"); 
//     // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/dance/",1); // 播放表情 墨镜
//     sendStringToBrocast("主人让我们一起来活动一下吧");
//     // 主人，让我们一起来活动一下吧
//     // std::thread audioTwist(&RobdogHandPosCtrl::playAudio,this, "/home/<USER>/resource/audio/twist/");
//     // audioTwist.detach();
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Result of handPositionCallback is: twistBody");
//     publishAction("twistBody");
// }

// void RobdogHandPosCtrl::handPositionSitDown(){
//     // 判断机器狗当前状态是否是动作可执行的初始状态
//     HomiRobotStatus status = RobotInfoMgr::getInstance().getRobotStatus();
//     if (status != ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) return;
//     handPosCtrl("pause"); 
//     // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/shake/",1); // 播放表情 心心眼
//     sendStringToBrocast("主人我们坐下来一起聊会天吧");
//     // 主人，我们坐下来一起聊会天吧
//     // std::thread audioTwist(&RobdogHandPosCtrl::playAudio,this, "/home/<USER>/resource/audio/twist/");
//     // audioTwist.detach();
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Result of handPositionCallback is: sitDown");
//     publishAction("sitDown");
// } 

// void RobdogHandPosCtrl::handPositionStandUp(){
//     // 判断机器狗当前状态是否是动作可执行的初始状态
//     HomiRobotStatus status = RobotInfoMgr::getInstance().getRobotStatus();
//     if (status != ROBDOG_STATUS_GETDOWN) return;
//     handPosCtrl("pause"); 
//     // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/touch/", 1); // 播放表情 星星眼
//     sendStringToBrocast("主人是准备要带我出去玩吗");
//     // 主人，是准备要带我出去玩吗
//     // std::thread audioTwist(&RobdogHandPosCtrl::playAudio,this, "/home/<USER>/resource/audio/twist/");
//     // audioTwist.detach();
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Result of handPositionCallback is: standUp");
//     publishAction("standUp");
// }   

// void RobdogHandPosCtrl::handleTurnback(const Json::Value &jBody){
//     if (!timer_triggered_)
//     {
//         handleRobotMove(jBody);
//         // 标记定时器已触发，防止重复执行
//         timer_triggered_ = true;
//         // 停止定时器
//         delay_timer_->cancel();
//     }
// }

// void RobdogHandPosCtrl::handPositionLeftAndTurnback(){
//     // 判断机器狗当前状态是否是动作可执行的初始状态
//     HomiRobotStatus status = RobotInfoMgr::getInstance().getRobotStatus();
//     if (status != ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) return;
//     handPosCtrl("pause"); 
//     // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/touch/", 1); // 播放表情 常规表情
//     sendStringToBrocast("好的");
//     // std::thread audioTwist(&RobdogHandPosCtrl::playAudio,this, "/home/<USER>/resource/audio/twist/");
//     // audioTwist.detach();
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Result of handPositionCallback is: LeftAndTurnback");
//     // 构造 "direction" 对象
//     Json::Value direction;
//     direction["x"] = 0;
//     direction["y"] = 0;
//     direction["z"] = 0;
//     direction["yaw"] = 90;
//     direction["pitch"] = 0;
//     direction["roll"] = 0;

//     // 构造 "body" 对象
//     Json::Value body;
//     body["direction"] = direction;
//     body["actionType"] = 2;

//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Turn Left!");
//     handleRobotMove(body);
//     // 防止下一次会把上一次打断
//     // sleepForDuration(15);
//     // body["direction"]["yaw"] = 90; // 接着右转
//     // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Turn Right!");
//     // handleRobotMove(body);
//     if (!timer_triggered_)
//     {
//         // 在10秒后执行另一个操作
//         Json::Value body_copy = body;
//         body_copy["direction"]["yaw"] = -90; // 接着右转
//         delay_timer_ = ctrl_->create_wall_timer(
//             std::chrono::seconds(8),
//             [this, body_copy]() { handleTurnback(body_copy); }
//         );
//     }
// }  

// void RobdogHandPosCtrl::handPositionRightAndTurnback(){
//     // 判断机器狗当前状态是否是动作可执行的初始状态
//     HomiRobotStatus status = RobotInfoMgr::getInstance().getRobotStatus();
//     if (status != ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) return;
//     handPosCtrl("pause"); 
//     // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/touch/", 1); // 播放表情 常规表情
//     sendStringToBrocast("好的");
//     // std::thread audioTwist(&RobdogHandPosCtrl::playAudio,this, "/home/<USER>/resource/audio/twist/");
//     // audioTwist.detach();
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Result of handPositionCallback is: RightAndTurnback");
//     // 构造 "direction" 对象
//     Json::Value direction;
//     direction["x"] = 0;
//     direction["y"] = 0;
//     direction["z"] = 0;
//     direction["yaw"] = -90;
//     direction["pitch"] = 0;
//     direction["roll"] = 0;

//     // 构造 "body" 对象
//     Json::Value body;
//     body["direction"] = direction;
//     body["actionType"] = 2;
    
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Turn Right!");
//     handleRobotMove(body);
//     // sleepForDuration(15);
//     // body["direction"]["yaw"] = -90; // 接着左转
//     // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Turn Left!");
//     // handleRobotMove(body);
//     if (!timer_triggered_)
//     {
//         // 在10秒后执行另一个操作
//         Json::Value body_copy = body;
//         body_copy["direction"]["yaw"] = 90; // 接着左转
//         delay_timer_ = ctrl_->create_wall_timer(
//             std::chrono::seconds(8),
//             [this, body_copy]() { handleTurnback(body_copy); }
//         );
//     }
// }  

// ----------------------------------------------------------------------------------------

// 动作技能
void RobdogHandPosCtrl::publishAction(std::string actionargument) {
    homi_speech_interface::msg::RobdogAction msg;
    msg.actiontype = "motorSkill";
    msg.actionargument = actionargument;

    actionCmd_pub->publish(msg);
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published ActionType: %s, ActionArgument: %s",robdogAction.actiontype.c_str(),robdogAction.actionargument.c_str());
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published ActionType: %s, ActionArgument: %s",
                    msg.actiontype.c_str(),
                    msg.actionargument.c_str());
}

// // 机器狗移动
// void RobdogHandPosCtrl::handleRobotMove(const Json::Value &jBody) {
//     // Json::StreamWriterBuilder writer;
//     // std::string output = Json::writeString(writer, jBody);
//     // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Turn: %s", output.c_str());

//     // geometry_msgs::msg::Twist current_twist_msg_; // 默认构造
//     current_twist_msg_.linear.x = 0.0; // 根据需要初始化每个字段
//     current_twist_msg_.linear.y = 0.0;
//     current_twist_msg_.linear.z = 0.0;
//     current_twist_msg_.angular.x = 0.0;
//     current_twist_msg_.angular.y = 0.0;
//     current_twist_msg_.angular.z = 0.0;

//     // 对于其他消息类型，使用类似的方法
//     current_continue_msg_.event = "strType";
//     current_continue_msg_.x = 0;
//     current_continue_msg_.y = 0;
//     current_continue_msg_.z = 0;
//     current_continue_msg_.yaw = 0;
//     current_continue_msg_.pitch = 0;
//     current_continue_msg_.roll = 0;

//     // 处理direction的信息
//     Json::Value direction = jBody["direction"];
//     Json::StreamWriterBuilder writer1;
//     std::string output1 = Json::writeString(writer1, direction);
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "direction: %s", output1.c_str());

//     if (!jBody["actionType"].isNull()) {
//         int actionType = jBody["actionType"].asInt();
//         // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "actionType: %d", actionType);
        
//         // 更新移动状态
//         if (actionType == 1 && !move_status_flag) {
//             move_status_flag = true;
//             system("/home/<USER>/updateexpression2.sh /home/<USER>/resource/vedio/look_right_left_step1.mp4 2 /home/<USER>/resource/vedio/look_right_left_step2.mp4");
//         } else if (actionType == 0 && move_status_flag) {
//             move_status_flag = false;
//             system("/home/<USER>/updateexpression.sh /home/<USER>/resource/vedio/default.mp4");
//         }

//         if (actionType == 2) {
//             // 每次执行一个动作之前先静止一会【只有步进模式下才需要】
//             timer_move->cancel();
//             velCmd_pub->publish(current_twist_msg_);
//         }
//         // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "fspeed_x: %f, fspeed_y: %f, fspeed_z: %f", fspeed_x, fspeed_y, fspeed_z);
//         int count = 0; // 要走的步数
//         // 处理方向信息
//         for (const auto& axis : {"x", "y", "z", "yaw", "pitch", "roll"}) {
//             if (!direction[axis].isNull() && direction[axis].asInt() != 0) {
//                 int step = direction[axis].asInt();
//                 // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "axis: %s, step: %d", axis, step);
//                 if (std::string(axis) == "x") {
//                     count = std::abs(step);
//                     current_continue_msg_.x = step;
//                     current_twist_msg_.linear.x = (step > 0) ? fspeed_x : -fspeed_x;
//                     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Continue Move, CMD FROM PLATFORM OR APP. STEP_X: %d, fspeed_x: %f", step, fspeed_x);
//                 } else if (std::string(axis) == "y") {
//                     count = std::abs(step);
//                     current_continue_msg_.y = step;
//                     current_twist_msg_.linear.y = (step > 0) ? fspeed_y : -fspeed_y;
//                     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Continue Move, CMD FROM PLATFORM OR APP. STEP_Y: %d, fspeed_y: %f", step, fspeed_y);
//                 } else if (std::string(axis) == "z") {
//                     current_continue_msg_.z = step;
//                 } else if (std::string(axis) == "yaw") {
//                     count = std::ceil(static_cast<double>(step) / 15);
//                     current_continue_msg_.yaw = step;
//                     current_twist_msg_.angular.z = (step > 0) ? fspeed_z : -fspeed_z; // 转一次的角速度
//                 } else if (std::string(axis) == "pitch") {
//                     current_continue_msg_.pitch = step;
//                 } else if (std::string(axis) == "roll") {
//                     current_continue_msg_.roll = step;
//                 }
//             }
//         }

//         Proceationtype(actionType, count);
//     }
// }

// 和定时器相关的操作
// 暂停函数
// void RobdogHandPosCtrl::sleepForDuration(int seconds) {
//     // ros::Duration(seconds).sleep();
//     rclcpp::sleep_for(std::chrono::seconds(seconds)); 
// }

void RobdogHandPosCtrl::setTotalCount(int count) {
    total_count_ = count;
    send_count_ = 0; // 重置已发送次数
}

void RobdogHandPosCtrl::triggerTimerCallback() {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Timer Start");
    timer_move->reset();
    timerCallback();
}

void RobdogHandPosCtrl::timerCallback() {
    if (send_count_ < total_count_) {
        // publishVelocity(current_twist_msg_);
        publishVelocity(std::make_shared<geometry_msgs::msg::Twist>(current_twist_msg_));

        ++send_count_; // 增加了定时器次数
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Pub times: %d, Total Count_: %d", send_count_, total_count_);
    } else {
        // 完成发送后停止定时器
        // timer_.stop();
        timer_move->cancel();
        send_count_ = 0;
    }
}

//  ************************* 移动方式（步进还是持续移动） ************************* 
void RobdogHandPosCtrl::Proceationtype(int actiontype, int step) {
    // 步进【基本上是语音发送的指令，直接把twist传到ctrl执行即可】
    if (actiontype == 2) {
       
        // sleepForDuration(resting_time); // 暂停 [是为了让上一次的指令动作停止下来]
        // 方式一：直接发送速度【配置在qt里面的方法】
        // int count = std::abs(step); // 假设肯定不会发送0值
        // setTotalCount(count);
        // triggerTimerCallback();

        // 方式二：利用云深处新提供的接口
        current_continue_msg_.event = "stepMode";
        continueMoveCmd_pub->publish(current_continue_msg_);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), 
                    "Published current_continue_msg_:\n"
                    "event: %s, x = %d, y = %d, z = %d\n"
                    "yaw = %d, pitch = %d, roll = %d",
                    current_continue_msg_.event.c_str(), current_continue_msg_.x ,current_continue_msg_.y, current_continue_msg_.z ,
                    current_continue_msg_.yaw ,current_continue_msg_.pitch ,current_continue_msg_.roll);
    }
    // 连续移动【摇杆的指令，要通过轴指令控制狗】
    else if (actiontype == 1) {
        // 把current_continue_msg_传给控制节点
        watchDogMonitor = true;
        // lastMoveMessageTime = ros::Time::now();
        lastMoveMessageTime = ctrl_->now();
        continueMoveCmd_pub->publish(current_continue_msg_);
    } else {
        current_continue_msg_.event = "stopAction";
        continueMoveCmd_pub->publish(current_continue_msg_);
    }
}

// 一些发布函数
//  ************************* 发给机器狗控制节点 ************************* 
void RobdogHandPosCtrl::publishVelocity(const geometry_msgs::msg::Twist::SharedPtr velocity) {
    velCmd_pub->publish(*velocity);
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published velocity: linear.x = %f, linear.y = %f, angular.z = "
    //         "%f, angular.x = %f, angular.y = %f, angular.z = %f",velocity.linear.x, velocity.linear.y, velocity.linear.z,velocity.angular.x, velocity.angular.y, velocity.angular.z);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), 
                    "Published velocity:\n"
                    "Linear Velocity: x = %f, y = %f, z = %f\n"
                    "Angular Velocity: x = %f, y = %f, z = %f",
                    velocity->linear.x, velocity->linear.y, velocity->linear.z,
                    velocity->angular.x, velocity->angular.y, velocity->angular.z);
}

// void RobdogHandPosCtrl::publishStatusCtrl(int cmd, int value, int exvalue) {
//     homi_speech_interface::msg::ProprietySet msg;
//     msg.cmd = cmd;
//     msg.value = value;
//     msg.exvalue = exvalue;
//     status_pub_->publish(msg);
//     // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "cmd [0x%x] value[%d] exvalu[%d]", cmd, value, exvalue);
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "cmd [0x%x] value[%d] exvalue[%d]", cmd, value, exvalue);
// }
