/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:05:05
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-01 10:26:34
 * @Description: 考虑当前机器人当前可能只能执行一条任务（比如主动跟随中、语音通话中、建图中、导航任务中等）
                 只管理一个任务的状态，其他任务扔到队列里面去等待执行
                 (如果跟随过程中还可以进行视频通话，那需要考虑多任务并行，任务管理模块需要重新优化)
 */
#pragma once
#include <homi_com/singleton.hpp>
#include <homi_com/context.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>

#include <jsoncpp/json/json.h>

#include "taskMgr/task_info.h"

using namespace std;

class RobdogCtrlNode;
class TaskInfoMgr: public base::singleton<TaskInfoMgr>, public base::Context<TaskInfoPtr>  {
public:
    TaskInfoMgr();
    ~TaskInfoMgr();
    void init(RobdogCtrlNode* ctrl_ptr_);
    //执行任务(从消息队列里面取)
    void executeTaskMsg();
    //取消任务（取消的是当前执行的任务，如果该任务需要重新执行，需要重新放到队列里面）
    void cancelTask();
    //根据任务状态更新任务
    void updateTaskStatus(const string& uuid, HomiTaskStatus status);
      
private:
    RobdogCtrlNode* ctrl_node_ = nullptr;
    TaskInfoPtr cur_task_info_ = nullptr;  
};