/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to CXX Translator
  File name: Pose_.idl
  Source: Pose_.hpp
  Cyclone DDS: v0.10.2

*****************************************************************/
#ifndef DDSCXX_UNITREE_IDL_ROS2_POSE__HPP
#define DDSCXX_UNITREE_IDL_ROS2_POSE__HPP

#include "unitree/idl/ros2/Point_.hpp"

#include "unitree/idl/ros2/Quaternion_.hpp"


namespace geometry_msgs
{
namespace msg
{
namespace dds_
{
class Pose_
{
private:
 ::geometry_msgs::msg::dds_::Point_ position_;
 ::geometry_msgs::msg::dds_::Quaternion_ orientation_;

public:
  Pose_() = default;

  explicit Pose_(
    const ::geometry_msgs::msg::dds_::Point_& position,
    const ::geometry_msgs::msg::dds_::Quaternion_& orientation) :
    position_(position),
    orientation_(orientation) { }

  const ::geometry_msgs::msg::dds_::Point_& position() const { return this->position_; }
  ::geometry_msgs::msg::dds_::Point_& position() { return this->position_; }
  void position(const ::geometry_msgs::msg::dds_::Point_& _val_) { this->position_ = _val_; }
  void position(::geometry_msgs::msg::dds_::Point_&& _val_) { this->position_ = _val_; }
  const ::geometry_msgs::msg::dds_::Quaternion_& orientation() const { return this->orientation_; }
  ::geometry_msgs::msg::dds_::Quaternion_& orientation() { return this->orientation_; }
  void orientation(const ::geometry_msgs::msg::dds_::Quaternion_& _val_) { this->orientation_ = _val_; }
  void orientation(::geometry_msgs::msg::dds_::Quaternion_&& _val_) { this->orientation_ = _val_; }

  bool operator==(const Pose_& _other) const
  {
    (void) _other;
    return position_ == _other.position_ &&
      orientation_ == _other.orientation_;
  }

  bool operator!=(const Pose_& _other) const
  {
    return !(*this == _other);
  }

};

}

}

}

#include "dds/topic/TopicTraits.hpp"
#include "org/eclipse/cyclonedds/topic/datatopic.hpp"

namespace org {
namespace eclipse {
namespace cyclonedds {
namespace topic {

template <> constexpr const char* TopicTraits<::geometry_msgs::msg::dds_::Pose_>::getTypeName()
{
  return "geometry_msgs::msg::dds_::Pose_";
}

template <> constexpr bool TopicTraits<::geometry_msgs::msg::dds_::Pose_>::isKeyless()
{
  return true;
}

#ifdef DDSCXX_HAS_TYPE_DISCOVERY
template<> constexpr unsigned int TopicTraits<::geometry_msgs::msg::dds_::Pose_>::type_map_blob_sz() { return 878; }
template<> constexpr unsigned int TopicTraits<::geometry_msgs::msg::dds_::Pose_>::type_info_blob_sz() { return 196; }
template<> inline const uint8_t * TopicTraits<::geometry_msgs::msg::dds_::Pose_>::type_map_blob() {
  static const uint8_t blob[] = {
 0x27,  0x01,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0xf1,  0x2f,  0x93,  0xf0,  0x5b,  0x8c,  0xef,  0xbe, 
 0x11,  0x90,  0x31,  0xc4,  0x88,  0x17,  0xeb,  0x00,  0x51,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x41,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x5e,  0x73,  0x97,  0xe7,  0xe8, 
 0x64,  0x40,  0xdf,  0x64,  0xaf,  0x76,  0xcd,  0x4c,  0xbc,  0x47,  0x57,  0xfe,  0x07,  0x00,  0x00,  0x00, 
 0x19,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf1,  0x2e,  0xd7,  0x30,  0x7b,  0x8e, 
 0xc5,  0x7c,  0x4b,  0x34,  0x86,  0x46,  0xa9,  0x62,  0xa1,  0xda,  0x16,  0x39,  0x42,  0xf1,  0x5e,  0x73, 
 0x97,  0xe7,  0xe8,  0x64,  0x40,  0xdf,  0x64,  0xaf,  0x76,  0xcd,  0x4c,  0xbc,  0x43,  0x00,  0x00,  0x00, 
 0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x33,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x9d, 
 0xd4,  0xe4,  0x61,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x41, 
 0x52,  0x90,  0x76,  0x00,  0x0b,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0xfb, 
 0xad,  0xe9,  0xe3,  0xf1,  0x2e,  0xd7,  0x30,  0x7b,  0x8e,  0xc5,  0x7c,  0x4b,  0x34,  0x86,  0x46,  0xa9, 
 0x62,  0xa1,  0x00,  0x00,  0x53,  0x00,  0x00,  0x00,  0xf1,  0x51,  0x01,  0x00,  0x01,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x43,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x9d,  0xd4,  0xe4,  0x61,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x41,  0x52,  0x90,  0x76,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0xfb,  0xad,  0xe9,  0xe3,  0x00,  0x0b,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0xf1,  0x29,  0x01,  0x86,  0x00,  0xdc,  0x01,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0xf2,  0x0d,  0x47,  0x26,  0x4b,  0x4a,  0x66,  0xfa,  0x94,  0xbb,  0xfe,  0x34, 
 0xf3,  0x2f,  0x7d,  0x00,  0x92,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00,  0x28,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x20,  0x00,  0x00,  0x00,  0x67,  0x65,  0x6f,  0x6d,  0x65,  0x74,  0x72,  0x79, 
 0x5f,  0x6d,  0x73,  0x67,  0x73,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f, 
 0x3a,  0x3a,  0x50,  0x6f,  0x73,  0x65,  0x5f,  0x00,  0x5e,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x27,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x04,  0xac,  0x28,  0xf9,  0x19, 
 0x64,  0x23,  0x5d,  0xc9,  0x81,  0xe3,  0xf8,  0xfa,  0xdf,  0x00,  0x00,  0x00,  0x09,  0x00,  0x00,  0x00, 
 0x70,  0x6f,  0x73,  0x69,  0x74,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0x00,  0x2a,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0xf2,  0x6f,  0x01,  0xea,  0x49,  0x00,  0xbc,  0x02,  0x80,  0x58, 
 0xc3,  0xa8,  0xda,  0xe3,  0x52,  0x00,  0x00,  0x00,  0x0c,  0x00,  0x00,  0x00,  0x6f,  0x72,  0x69,  0x65, 
 0x6e,  0x74,  0x61,  0x74,  0x69,  0x6f,  0x6e,  0x00,  0x00,  0x00,  0xf2,  0x04,  0xac,  0x28,  0xf9,  0x19, 
 0x64,  0x23,  0x5d,  0xc9,  0x81,  0xe3,  0xf8,  0xfa,  0xdf,  0x00,  0x00,  0x00,  0x78,  0x00,  0x00,  0x00, 
 0xf2,  0x51,  0x01,  0x00,  0x29,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x21,  0x00,  0x00,  0x00, 
 0x67,  0x65,  0x6f,  0x6d,  0x65,  0x74,  0x72,  0x79,  0x5f,  0x6d,  0x73,  0x67,  0x73,  0x3a,  0x3a,  0x6d, 
 0x73,  0x67,  0x3a,  0x3a,  0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x50,  0x6f,  0x69,  0x6e,  0x74,  0x5f, 
 0x00,  0x00,  0x00,  0x00,  0x40,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00,  0x78,  0x00,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x79,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x7a,  0x00,  0x00,  0x00,  0xf2,  0x6f,  0x01,  0xea,  0x49,  0x00,  0xbc,  0x02, 
 0x80,  0x58,  0xc3,  0xa8,  0xda,  0xe3,  0x52,  0x00,  0x90,  0x00,  0x00,  0x00,  0xf2,  0x51,  0x01,  0x00, 
 0x2e,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x00,  0x26,  0x00,  0x00,  0x00,  0x67,  0x65,  0x6f,  0x6d, 
 0x65,  0x74,  0x72,  0x79,  0x5f,  0x6d,  0x73,  0x67,  0x73,  0x3a,  0x3a,  0x6d,  0x73,  0x67,  0x3a,  0x3a, 
 0x64,  0x64,  0x73,  0x5f,  0x3a,  0x3a,  0x51,  0x75,  0x61,  0x74,  0x65,  0x72,  0x6e,  0x69,  0x6f,  0x6e, 
 0x5f,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00,  0x04,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00, 
 0x00,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00,  0x78,  0x00,  0x00,  0x00, 
 0x10,  0x00,  0x00,  0x00,  0x01,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00, 
 0x79,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x01,  0x00,  0x0a,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x7a,  0x00,  0x00,  0x00,  0x10,  0x00,  0x00,  0x00,  0x03,  0x00,  0x00,  0x00, 
 0x01,  0x00,  0x0a,  0x00,  0x02,  0x00,  0x00,  0x00,  0x77,  0x00,  0x00,  0x00,  0x5e,  0x00,  0x00,  0x00, 
 0x03,  0x00,  0x00,  0x00,  0xf2,  0x0d,  0x47,  0x26,  0x4b,  0x4a,  0x66,  0xfa,  0x94,  0xbb,  0xfe,  0x34, 
 0xf3,  0x2f,  0x7d,  0xf1,  0x2f,  0x93,  0xf0,  0x5b,  0x8c,  0xef,  0xbe,  0x11,  0x90,  0x31,  0xc4,  0x88, 
 0x17,  0xeb,  0xf2,  0x04,  0xac,  0x28,  0xf9,  0x19,  0x64,  0x23,  0x5d,  0xc9,  0x81,  0xe3,  0xf8,  0xfa, 
 0xdf,  0xf1,  0x5e,  0x73,  0x97,  0xe7,  0xe8,  0x64,  0x40,  0xdf,  0x64,  0xaf,  0x76,  0xcd,  0x4c,  0xbc, 
 0xf2,  0x6f,  0x01,  0xea,  0x49,  0x00,  0xbc,  0x02,  0x80,  0x58,  0xc3,  0xa8,  0xda,  0xe3,  0x52,  0xf1, 
 0x2e,  0xd7,  0x30,  0x7b,  0x8e,  0xc5,  0x7c,  0x4b,  0x34,  0x86,  0x46,  0xa9,  0x62,  0xa1, };
  return blob;
}
template<> inline const uint8_t * TopicTraits<::geometry_msgs::msg::dds_::Pose_>::type_info_blob() {
  static const uint8_t blob[] = {
 0xc0,  0x00,  0x00,  0x00,  0x01,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf1,  0x2f,  0x93,  0xf0,  0x5b,  0x8c,  0xef,  0xbe,  0x11,  0x90,  0x31,  0xc4, 
 0x88,  0x17,  0xeb,  0x00,  0x55,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf1,  0x5e,  0x73,  0x97,  0xe7,  0xe8,  0x64,  0x40, 
 0xdf,  0x64,  0xaf,  0x76,  0xcd,  0x4c,  0xbc,  0x00,  0x47,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf1,  0x2e,  0xd7,  0x30,  0x7b,  0x8e,  0xc5,  0x7c,  0x4b,  0x34,  0x86,  0x46,  0xa9,  0x62,  0xa1,  0x00, 
 0x57,  0x00,  0x00,  0x00,  0x02,  0x10,  0x00,  0x40,  0x58,  0x00,  0x00,  0x00,  0x54,  0x00,  0x00,  0x00, 
 0x14,  0x00,  0x00,  0x00,  0xf2,  0x0d,  0x47,  0x26,  0x4b,  0x4a,  0x66,  0xfa,  0x94,  0xbb,  0xfe,  0x34, 
 0xf3,  0x2f,  0x7d,  0x00,  0x96,  0x00,  0x00,  0x00,  0x02,  0x00,  0x00,  0x00,  0x34,  0x00,  0x00,  0x00, 
 0x02,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00,  0xf2,  0x04,  0xac,  0x28,  0xf9,  0x19,  0x64,  0x23, 
 0x5d,  0xc9,  0x81,  0xe3,  0xf8,  0xfa,  0xdf,  0x00,  0x7c,  0x00,  0x00,  0x00,  0x14,  0x00,  0x00,  0x00, 
 0xf2,  0x6f,  0x01,  0xea,  0x49,  0x00,  0xbc,  0x02,  0x80,  0x58,  0xc3,  0xa8,  0xda,  0xe3,  0x52,  0x00, 
 0x94,  0x00,  0x00,  0x00, };
  return blob;
}
#endif //DDSCXX_HAS_TYPE_DISCOVERY

} //namespace topic
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

namespace dds {
namespace topic {

template <>
struct topic_type_name<::geometry_msgs::msg::dds_::Pose_>
{
    static std::string value()
    {
      return org::eclipse::cyclonedds::topic::TopicTraits<::geometry_msgs::msg::dds_::Pose_>::getTypeName();
    }
};

}
}

REGISTER_TOPIC_TYPE(::geometry_msgs::msg::dds_::Pose_)

namespace org{
namespace eclipse{
namespace cyclonedds{
namespace core{
namespace cdr{

template<>
propvec &get_type_props<::geometry_msgs::msg::dds_::Pose_>();

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool write(T& streamer, const ::geometry_msgs::msg::dds_::Pose_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.position(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!write(streamer, instance.orientation(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool write(S& str, const ::geometry_msgs::msg::dds_::Pose_& instance, bool as_key) {
  auto &props = get_type_props<::geometry_msgs::msg::dds_::Pose_>();
  str.set_mode(cdr_stream::stream_mode::write, as_key);
  return write(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool read(T& streamer, ::geometry_msgs::msg::dds_::Pose_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.position(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!read(streamer, instance.orientation(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool read(S& str, ::geometry_msgs::msg::dds_::Pose_& instance, bool as_key) {
  auto &props = get_type_props<::geometry_msgs::msg::dds_::Pose_>();
  str.set_mode(cdr_stream::stream_mode::read, as_key);
  return read(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool move(T& streamer, const ::geometry_msgs::msg::dds_::Pose_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.position(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!move(streamer, instance.orientation(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool move(S& str, const ::geometry_msgs::msg::dds_::Pose_& instance, bool as_key) {
  auto &props = get_type_props<::geometry_msgs::msg::dds_::Pose_>();
  str.set_mode(cdr_stream::stream_mode::move, as_key);
  return move(str, instance, props.data()); 
}

template<typename T, std::enable_if_t<std::is_base_of<cdr_stream, T>::value, bool> = true >
bool max(T& streamer, const ::geometry_msgs::msg::dds_::Pose_& instance, entity_properties_t *props) {
  (void)instance;
  if (!streamer.start_struct(*props))
    return false;
  auto prop = streamer.first_entity(props);
  while (prop) {
    switch (prop->m_id) {
      case 0:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.position(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
      case 1:
      if (!streamer.start_member(*prop))
        return false;
      if (!max(streamer, instance.orientation(), prop))
        return false;
      if (!streamer.finish_member(*prop))
        return false;
      break;
    }
    prop = streamer.next_entity(prop);
  }
  return streamer.finish_struct(*props);
}

template<typename S, std::enable_if_t<std::is_base_of<cdr_stream, S>::value, bool> = true >
bool max(S& str, const ::geometry_msgs::msg::dds_::Pose_& instance, bool as_key) {
  auto &props = get_type_props<::geometry_msgs::msg::dds_::Pose_>();
  str.set_mode(cdr_stream::stream_mode::max, as_key);
  return max(str, instance, props.data()); 
}

} //namespace cdr
} //namespace core
} //namespace cyclonedds
} //namespace eclipse
} //namespace org

#endif // DDSCXX_UNITREE_IDL_ROS2_POSE__HPP
