#ifndef FOLLOWMENODE_H
#define FOLLOWMENODE_H

#pragma once
#include "rclcpp/rclcpp.hpp"
#include <std_msgs/msg/string.hpp>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <jsoncpp/json/json.h>
#include <string>
#include <memory>
#include <unordered_map>
#include <ament_index_cpp/get_package_share_directory.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <homi_speech_interface/srv/sigc_data.hpp>
#include <homi_com/singleton.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>
#include "robotState/RobotState.h" 
#include "robotMgr/robot_info_mgr.h" 
#include <std_msgs/msg/u_int8.hpp>

#define NO_TARGETPOSION_TIMEOUT 2
#define FOLLOW_TARGET_LOSE_TIMEOUT 15
#define FIRSTBASE 10000
#define SERVICE_SUCCESS 0
#define FOLLOW_BATTERYLOWLEVEL    15
#define FOLLOW_JOINT_TMP_THRED 110
#define FOLLOW_CPU_TMP_THRED 67

#define FollowNodeStarted 4000
#define FollowNodeStopped 4001
#define FollowFunctionInitialized 4002
#define FollowFunctionPaused 4003
#define TargetDetected 4004
#define InvalidMessageFormat 4100
#define NoUWBData 4101     //UWB检测失败异常
#define NOUWBDataFIRST (FIRSTBASE + 4101)
#define NoCameraData 4102   //相机检测失败异常
#define NoCameraDataFIRST (FIRSTBASE + 4102)
#define TargetLostDuringTracking 4103   //跟随目标丢失异常
#define NoFollowTargetDetectedFirstTime 4105
#define NoFollowTargetDetected 4106 //启动时前方未检测到目标
#define ObstacleAvoidanceError_DeepCam 7101 //避障功能异常(无深度相机数据)
#define ObstacleAvoidanceError_Elevation_Map 7102 //避障功能异常(无高程图数据)
#define ObstacleAvoidanceError_TIMEOUT 7103 //避障功能异常(避障超时)
#define ObstacleAvoidanceError_Elevation_IMU 7104 //避障功能异常(无IMU数据)
#define FollowNodeFailedToStart 4200
#define FollowNodeCrashed 4201
#define ConflictingInstructionInFollowState 4202
#define FollowStartInOtherState 4203
#define InvalidFollowCommandInNonFollowState 4204
#define SUMMON_FUNCTION_STARTED     4604
#define SUMMON_FUNCTION_REACHED     4605
#define SUMMON_WITHOUT_UWB          4108

#define LowBattery  2020040006  //电量不足异常
#define AbnormalTemperatures    2020040007 //温度异常
#define NotMovedLongtime        2020040008 //长时间未移动
#define ObstacleAvoidanceError  2020040009 //避障功能异常

#define ERROR_LOW_BATTERY      0x01// 电量不足异常
#define ERROR_UWB_DETECTION_FAIL  0x02// UWB检测失败异常
#define ERROR_CAMERA_DETECTION_FAIL  0x03// 相机检测失败异常
#define ERROR_TARGET_NOT_DETECTED  0x04// 启动时前方未检测到目标
#define ERROR_TEMPERATURE_ABNORMAL  0x05// 温度异常
#define ERROR_OBSTACLE_AVOIDANCE_FAIL  0x06// 避障功能异常
#define ERROR_FOLLOW_TARGET_LOST  0x07// 跟随目标丢失异常
#define ERROR_DOG_NOT_MOVING_LONG_TIME  0x08// 跟随过程中机器狗长时间未移动

namespace FollowErrors {
    inline const std::unordered_map<int, int>& get_error_map() {
        static const std::unordered_map<int, int> error_map = {
            {NoUWBData, ERROR_UWB_DETECTION_FAIL},
            {NoCameraData, ERROR_CAMERA_DETECTION_FAIL},
            {TargetLostDuringTracking, ERROR_FOLLOW_TARGET_LOST},
            {NoFollowTargetDetected, ERROR_TARGET_NOT_DETECTED},
            {ObstacleAvoidanceError_DeepCam, ERROR_OBSTACLE_AVOIDANCE_FAIL},
            {ObstacleAvoidanceError_Elevation_Map, ERROR_OBSTACLE_AVOIDANCE_FAIL},
            {ObstacleAvoidanceError_TIMEOUT, ERROR_OBSTACLE_AVOIDANCE_FAIL},
            {ObstacleAvoidanceError_Elevation_IMU, ERROR_OBSTACLE_AVOIDANCE_FAIL},
            {LowBattery, ERROR_LOW_BATTERY},
            {AbnormalTemperatures, ERROR_TEMPERATURE_ABNORMAL},
            {NotMovedLongtime, ERROR_DOG_NOT_MOVING_LONG_TIME}
        };
        return error_map;
    }
}

class FollowNode : public rclcpp::Node {
public:
    explicit FollowNode(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    void play_audio_async(const std::string& audio_file_path);
    std::string getResourcePath(const std::string &file_name);
private:
    void follow_me_status_callback(const std_msgs::msg::String::SharedPtr msg);
    void watchDogforsubTargetCalibrationReq();
    void target_position_callback(const geometry_msgs::msg::PoseStamped::SharedPtr msg);
    void subRbtServerInfo(const std_msgs::msg::String::SharedPtr msg);
    void pull_up_down_processs(int switchKey);
    void stopFollow();
    void tripsigHandle(const std_msgs::msg::String::SharedPtr msg);
    void navCallback(const std_msgs::msg::String::SharedPtr msg);
    Json::Value asembleWarningBody(int code,const std::string &alarmName,std::string &alarmDesc);
    void sendDataToPlatform(const std::string &data);
    void sendWarnigtoPlat(Json::Value warningBody);
    void nofifyAPPFollowStatus(int warningCode);
    void sendWarningandNotifyAPP(int errorCode, const std::string& errorSource, std::string& errorMsg);

    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr audio_command_subscription_;
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr target_position_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr subPubRbtSever_  = nullptr;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr tripsigSub_  = nullptr;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navPositionSub_ = nullptr; 

    rclcpp::Client<homi_speech_interface::srv::AssistantAbort>::SharedPtr client_assistant_abort_;
    rclcpp::Client<homi_speech_interface::srv::SetWakeEvent>::SharedPtr client_set_wake_event_;

    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pubCatchPos_ = nullptr;
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pubTargetPos_ = nullptr;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pullUpProcessPub_ = nullptr; 
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr followWarnPub_ = nullptr; 
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr velPub_=nullptr;
    rclcpp::Publisher<std_msgs::msg::UInt8>::SharedPtr mode_publisher_=nullptr;

    rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedPtr platClint_;

    rclcpp::TimerBase::SharedPtr watchdogTimer_ = nullptr;

    using Clock = std::chrono::steady_clock;  
    std::chrono::time_point<Clock> lastTargetCaliReqTime=Clock::now();  
    size_t msg_cnt_=0; 
    std::mutex msg_cnt_mutex_;
    geometry_msgs::msg::PoseStamped lastPose;

    bool bStartServer_ = false;
    bool bContinue_ = false;
    bool alreadyStarted_=false;
    geometry_msgs::msg::PoseStamped cached_pos_;
    enum class OperationMode: uint8_t{
        FOLLOW_MODE=0,  
        TRIP_MODE=1
    };
    std::atomic<OperationMode> current_mode_;
    std::mutex mode_mutex_;
};
#endif