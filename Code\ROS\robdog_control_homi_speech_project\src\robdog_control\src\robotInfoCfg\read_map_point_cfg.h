/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:05:05
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-01 10:26:34
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_config.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#pragma once
#include <string>
#include <vector>
#include <map>
#include <homi_com/singleton.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>

using namespace std;

class ReadMapCfg: public base::singleton<ReadMapCfg>  {
public:
    ReadMapCfg();
    ~ReadMapCfg();
    
    //配置文件的点位解析
    void loadConfig(std::string& configPath);

    //最新的点位信息更新到本地配置文件
    void update(); 

    PropertyBuilderByName(std::string, ConfigPath, "");      //点位路径
    PropertyBuilderByName(std::string, PressPoints, "");     //取快递点位信息
    PropertyBuilderByName(std::string, PhonePoints, "");     //拍照点位信息
    PropertyBuilderByName(std::string, FamilyMovePoints, "");     //家庭移动点位信息
    PropertyBuilderByName(std::string, PatrolPoints, "");    //巡检点位信息
    PropertyBuilderByName(std::string, GoHomePoints, "");    //家庭点位信息
    PropertyBuilderByName(std::string, DeliverCakePoints, "");    //蛋糕点位信息
    PropertyBuilderByName(std::string, BatteryChargingPoints, "");
    PropertyBuilderByName(std::string, ChargeNavPoints, "");
};