/*************************************************************
 *
 * This is a part of the homiWake SDK.
 * Copyright:2024 homiWake
 * File name:homiWakeOpenAPI.h
 * Version:1.0.1
 * All rights reserved.
 *
 *************************************************************/

#ifndef HOMIWAKEOPENAPI_H
#define HOMIWAKEOPENAPI_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

typedef struct
{
 
    /**
     * 【回调】Homi接受唤醒引擎下发的唤醒事件
	 * @description：Homi接受AIUI下发引擎下发的唤醒事件，返回给用户
     * @param [in] param  唤醒结果数据
     * @param [in] pUserInfo  唤醒结果数据
     * {"rlt":[{"sid":".\test_wav\2sec.wav","istart":22,"iresid":0,"iduration":122,"nfillerscore":22,"nkeywordscore":148906,"ncm":2233,"ncmKeyword":1364,"ncmFiller":-869,"ncmThreshold":842,"keyword":"xiao3 zhi4 xiao3 zhi4"}]}
     * @return 
     * - = 0:  成功
     * - < 0:  失败
     */
    int (*Homi_WakeUpResult)(char *param, char *pUserInfo);
	
} homiWakeCallBackFunList;

int homiWakeInit(homiWakeCallBackFunList *callBackFunList, char *mlp_path, char *keyword_path, char *minglingci_path);

/**
 * 功能：开始唤醒引擎任务
 *
 * @return
 * - = 0:  成功
 * other:  失败
 */
extern int homiStartWake(void);



int homiWriteWakeUpPcm(char *data, int len);

int homiStopWake();

int homiUnInitWake();

#ifdef __cplusplus
}
#endif

#endif //HomiSPEECH_OPENAPI_H
