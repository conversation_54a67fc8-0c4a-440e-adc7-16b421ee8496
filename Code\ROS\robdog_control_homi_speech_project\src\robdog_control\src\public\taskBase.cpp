#include "taskBase.h"
#include <algorithm>  // for std::clamp
#include <chrono>
#include <bits/algorithmfwd.h>

// 构造函数
TaskBase::TaskBase(int task_id) :
    task_id_(task_id),
    current_state_(State::Idle),
    pause_requested_(false),
    stop_requested_(false),
    progress_percent_(0),
    last_error_(ErrorType::None) {}

// 析构函数
TaskBase::~TaskBase() {
    Stop();
    if (worker_thread_.joinable()) {
        worker_thread_.join();
    }
}

// 启动任务
void TaskBase::Start() {
    if (current_state_ == State::Idle || 
        current_state_ == State::Stopped ||
        current_state_ == State::Failed) {
        
        TransitionState(State::Running);
        stop_requested_.store(false);
        pause_requested_.store(false);
        worker_thread_ = std::thread(&TaskBase::RunThread, this);
        OnStart();
    }
}

// 停止任务（线程安全）
void TaskBase::Stop() {
    if (!stop_requested_.exchange(true)) {
        pause_cond_.notify_all();
        
        {
            std::lock_guard<std::mutex> lock(state_mutex_);
            if (current_state_ == State::Running || 
                current_state_ == State::Paused) {
                TransitionState(State::Stopped);
            }
        }
        
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        OnStop();
    }
}

// 暂停控制
void TaskBase::Pause() {
    if (current_state_ == State::Running) {
        pause_requested_.store(true);
        TransitionState(State::Paused);
        OnPause();
    }
}

// 恢复控制
void TaskBase::Resume() {
    if (current_state_ == State::Paused) {
        pause_requested_.store(false);
        pause_cond_.notify_one();
        TransitionState(State::Running);
        OnResume();
    }
}

// 核心线程执行体
void TaskBase::RunThread() {
    try {
        while (!stop_requested_) {
            // 处理暂停请求
            if (pause_requested_) {
                std::unique_lock<std::mutex> lock(state_mutex_);
                pause_cond_.wait(lock, [this] {
                    return !pause_requested_ || stop_requested_;
                });
            }
            
            if (stop_requested_) break;
            
            Execute();  // 执行具体任务
        }
        
        TransitionState(stop_requested_ ? State::Stopped : State::Completed);
    } 
    catch (const std::exception& ex) {
        std::lock_guard<std::mutex> lock(error_mutex_);
        last_error_ = ErrorType::RuntimeError;
        error_message_ = ex.what();
        TransitionState(State::Failed);
    } 
    catch (...) {
        std::lock_guard<std::mutex> lock(error_mutex_);
        last_error_ = ErrorType::RuntimeError;
        error_message_ = "Unknown exception occurred";
        TransitionState(State::Failed);
    }
}

// 状态转换方法
void TaskBase::TransitionState(State new_state) {
    auto old_state = current_state_.exchange(new_state);
    
    if (old_state != new_state) {
        std::lock_guard<std::mutex> lock(listener_mutex_);
        for (auto& handler : state_listeners_) {
            handler(new_state);
        }
    }
}

// 其他方法实现
void TaskBase::AddStateListener(StateChangedHandler handler) {
    std::lock_guard<std::mutex> lock(listener_mutex_);
    state_listeners_.push_back(handler);
}

TaskBase::State TaskBase::GetState() const {
    return current_state_.load();
}

TaskBase::ErrorType TaskBase::GetLastError() const {
    std::lock_guard<std::mutex> lock(error_mutex_);
    return last_error_;
}

std::string TaskBase::GetErrorMessage() const {
    std::lock_guard<std::mutex> lock(error_mutex_);
    return error_message_;
}

int TaskBase::GetTaskId() const {
    return task_id_;
}

void TaskBase::SetProgress(int percentage) {
    progress_percent_.store(std::clamp(percentage, 0, 100));
}

int TaskBase::GetProgress() const {
    return progress_percent_.load();
}