#pragma once
#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/pose.hpp>
#include <std_msgs/msg/string.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2_ros/transform_listener.h>
#include <tf2/LinearMath/Quaternion.h>

#include <homi_speech_interface/msg/sigc_event.hpp>
#include <homi_speech_interface/msg/robdog_action.hpp>
#include <homi_speech_interface/srv/sigc_data.hpp>
#include <homi_speech_interface/srv/net_ctrl.hpp>
#include <homi_speech_interface/msg/propriety_set.hpp>
#include <homi_speech_interface/msg/proper_to_app.hpp>
#include <homi_speech_interface/msg/continue_move.hpp>
#include <homi_speech_interface/msg/assistant_event.hpp>
#include <homi_speech_interface/srv/assistant_speech_text.hpp>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/assistant_take_photo.hpp>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>

// #include <xiaoli_com/deep_cmd.h>
#include <jsoncpp/json/json.h>
#include <fstream>
#include <unordered_set>
// #include "public/litedb.h"
// #include "public/audio_ctrl.h"
#include <homi_com/singleton.hpp>
// #include <xiaoli_com/xiaoli_pub_def.h>

#include "robdogNode/robdog_ctrl_node.h"

class RobdogCtrlNode;
class RobdogHandPosCtrl : public base::singleton<RobdogHandPosCtrl>  {
public:
    // RobdogHandPosCtrl(rclcpp::Node::SharedPtr node, const std::string &configPath);
    RobdogHandPosCtrl();
    ~RobdogHandPosCtrl();
    void init(RobdogCtrlNode* node);

    // ********************** 和手势识别相关的函数
    void Proceationtype(int actiontype, int step);
    void handPosStart();
    void handPositionStatusCallback(const std_msgs::msg::String::SharedPtr message);
    void handPosOpenStart();
    void handPosCtrl(std::string command_ctrl);
    void handPositionGreeting();
    void handPositionTwistBody();
    void handPositionSitDown();
    void handPositionStandUp();
    void handleTurnback(const Json::Value &jBody);
    void handPositionLeftAndTurnback();
    void handPositionRightAndTurnback();
    void isActionOver(int action_id);
    void handPositionCallback(const std_msgs::msg::String::SharedPtr message);
    void ActionSuspend();
    
    // ************************ 和定时器相关的操作
    void setTotalCount(int count); // 设置发送次数的函数
    void triggerTimerCallback(); // 公开的函数用于手动调用定时器回调
    void timerCallback();
    // void timerRobotPoseCallback();
    void sleepForDuration();  // 暂停函数 int seconds
    void timerCallback_stophandpos();
    

    // ************************ 和设备信息相关的
    void playAudio(const std::string& filePath);
    void brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response);
    void sendStringToBrocast(const std::string &message);

    void deepStatusCallback(const homi_speech_interface::msg::ProprietySet::SharedPtr msg);
    std::string get_robot_properties(const Json::Value &inValue);
    std::string get_connect_info_request(const Json::Value &inValue);
    void setProperties(const Json::Value& request);

    // ************************  一些发布函数
    void publishVelocity(const geometry_msgs::msg::Twist::SharedPtr velocity); // 发布者功能：发布速度命令
    void publishAction(std::string actionargument);
    void handleRobotMove(const Json::Value &jBody);
    void publishStatusCtrl(int cmd,int value,int exvalue);

    //处理webSocket消息
    void parseWsActionMsg(Json::Value& valueMsg);


private:
    RobdogCtrlNode* ctrl_ = nullptr;
    // RobdogCtrlNode ctrlNode;  // 创建对象
    std::unique_ptr<RobdogCtrlNode> ctrlNode;
    // 步进次数
    int total_count_ = 0;  // 总共发送的次数
    int send_count_ = 0;   // 已发送的次数
    // 智能播报次数
    int brocast_send_count_ = 0;
    int brocast_total_count_ = 0;
    std::string brocast_text; // 当前智能播报的文本
    // 设置初始状态为未到达目标
    // bool at_target_ = false;
    // 初始化为 false 表示尚未到达单个点位
    bool atSinglePoint = false;  
    // 设置初始状态为导航任务未被取消
    bool moveCancel_ = false;
    std::string uid; // 导航任务下发点位对应id
    geometry_msgs::msg::Twist current_twist_msg_;  // 保存的消息（要传给控制节点的速度【语音控制】）
    homi_speech_interface::msg::ContinueMove current_continue_msg_; // 要传给控制节点的持续移动信息【摇杆控制】
    rclcpp::Time last_heartbeat_time;  // 上次接收心跳的时间
    rclcpp::Time lastMoveMessageTime; // 更新最后收到action消息的时间
    rclcpp::Time last_received_time_; // 记录最后接收到消息的时间
    rclcpp::Clock::SharedPtr clock_; // = ctrl_->get_clock();
    // std::shared_ptr<AudioPlayer> audioCtrl;
    bool flag_recvHandPos; // 是否接收到识别结果的标志
    bool flag_openHandPos; // 维护算法是否开启的状态

    // ************************ 用到的yaml的数据
    double fspeed_x = 0.6;
    double fspeed_y = 0.6;
    double fspeed_z = 0.5; // 大约是15度
    int timer_interval = 1;
    double resting_time = 0.5;
    bool  move_status_flag = false;
    int  expresstion_count = 0;
    bool watchDogMonitor=false; // 启动监测

    bool timer_triggered_ = false; // 标志变量，确保延迟操作只执行一次
    // std::string robotdog_file_path = "/home/<USER>/.config/config_robotdog.xml";
	// std::string map_points_path = "/home/<USER>/.config/map_points.json";

    // std::map<std::string,int> sportModeMap={
    //     {"walk",DEEP_CMD_ACTION_WALK},
    //     {"run",DEEP_CMD_ACTION_RUN},
    //     {"stairClimbe",DEEP_CMD_ACTION_STAIR},
    //     {"climbe",DEEP_CMD_ACTION_CLIMBE},
    //     {"traction",DEEP_CMD_ACTION_WALK},
    //     {"emergencyStop",DEEP_CMD_ACTION_SOFTSTOP}
    // };  //平台运动模式字段

    // // 动作映射
    // std::map<std::string, std::string> actionMap = {
    //     {"standUp", "standUp"},
    //     {"getDown", "getDown"},
    //     {"greeting", "greeting"},
    //     {"twistBody", "twistBody"},
    //     {"backflip", "backflip"},
    //     {"jumpForward", "jumpForward"},
    //     {"turnOver", "turnOver"},
    //     {"twistJump", "twistJump"},
    //     {"sitDown", "sitDown"},
    //     {"fingerHeart", "fingerHeart"},
    //     {"makeBow", "makeBow"},
    //     {"dance", "dance"},
    //     {"shakeBody", "shakeBody"},
    //     {"twistAss", "twistAss"},
    //     {"twistBody_emergency", "twistBody_emergency"},
    //     {"greeting_emergency", "greeting_emergency"}
    // };

    // std::vector<std::string> actionVector = {
    //     "standUp",
    //     "getDown",
    //     "greeting",
    //     "twistBody",
    //     "backflip",
    //     "jumpForward",
    //     "turnOver",
    //     "twistJump",
    //     "sitDown",
    //     "fingerHeart",
    //     "makeBow",
    //     "dance",
    //     "shakeBody",
    //     "twistAss",
    //     "twistBody_emergency",
    //     "greeting_emergency"
    // };
    
    // 订阅器
    // rclcpp::Subscription<homi_speech_interface::msg::SIGCEvent>::SharedPtr platCmd_sub_;
    // rclcpp::Subscription<homi_speech_interface::msg::SIGCEvent>::SharedPtr app_sub;
    // rclcpp::Subscription<homi_speech_interface::msg::ProprietySet>::SharedPtr deepCtrl_sub_;
    // rclcpp::Subscription<geometry_msgs::msg::Pose>::SharedPtr navPosition_sub_;
    // rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navStatus_sub_;
    // rclcpp::Subscription<homi_speech_interface::msg::AssistantEvent>::SharedPtr brocast_sub;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr handPosition_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr handPosStatus_sub_;

    // 发布器
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr velCmd_pub;
    rclcpp::Publisher<homi_speech_interface::msg::RobdogAction>::SharedPtr actionCmd_pub;
    rclcpp::Publisher<homi_speech_interface::msg::ContinueMove>::SharedPtr continueMoveCmd_pub;
    // rclcpp::Publisher<std_msgs::msg::String>::SharedPtr actionPlanningMove_pub;
    // rclcpp::Publisher<std_msgs::msg::String>::SharedPtr mappingControl_pub;
    // rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publishVirtualWall;
    // rclcpp::Publisher<homi_speech_interface::msg::ProperToApp>::SharedPtr prope2app_pub;
    // rclcpp::Publisher<homi_speech_interface::msg::ProprietySet>::SharedPtr status_pub_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publishhandPosStart;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publishhandPosCtrl;

    // 服务器
    // rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedPtr platform_client;
    // rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedPtr app_client;
    // rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedPtr net_client;
    rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedPtr brocast_client;
    // rclcpp::Client<homi_speech_interface::srv::AssistantAbort>::SharedPtr brocast_abort_client;
    // rclcpp::Client<homi_speech_interface::srv::SetWakeEvent>::SharedPtr set_wake_client;
    // rclcpp::Client<homi_speech_interface::srv::IotControl>::SharedPtr iot_control_client_;
    // rclcpp::Client<homi_speech_interface::srv::AssistantTakePhoto>::SharedPtr take_photo_client_;
    
    // 定时器
    // rclcpp::TimerBase::SharedPtr ws_heartbeat_timer_;
    // rclcpp::TimerBase::SharedPtr timer_2;
    rclcpp::TimerBase::SharedPtr timer_move;
    // rclcpp::TimerBase::SharedPtr robPoseStatusTimer_;
    // rclcpp::TimerBase::SharedPtr timer_brocast;
    // rclcpp::TimerBase::SharedPtr timerDog;
    // rclcpp::TimerBase::SharedPtr robMoveStatusTimer_;
    // rclcpp::TimerBase::SharedPtr robMoveStatusTimer_brocast;
    // rclcpp::TimerBase::SharedPtr robActionCheckTimer_;
    // rclcpp::TimerBase::SharedPtr robPatrolStatusTimer_;
    rclcpp::TimerBase::SharedPtr timer_stophandpos; // 超时无动作关闭算法
   
    rclcpp::TimerBase::SharedPtr delay_timer_; // 定时器：用于延迟执行操作

    
    rclcpp::TimerBase::SharedPtr robActionStatusTimer_; // 动作是否执行完毕
    rclcpp::TimerBase::SharedPtr robActionSuspendTimer_; // 间隔固定的时延开启算法
};