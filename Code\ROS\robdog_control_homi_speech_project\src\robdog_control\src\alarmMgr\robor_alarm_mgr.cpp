/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:04:29
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-02 21:02:13
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_cfg.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "alarmMgr/robor_alarm_mgr.h"
#include "robdogNode/robdog_ctrl_node.h"

AlarmInfoMgr::AlarmInfoMgr() {
}

AlarmInfoMgr::~AlarmInfoMgr() {
}

void AlarmInfoMgr::init(RobdogCtrlNode* ctrl_ptr_) {
    if(nullptr == ctrl_ptr_) {
        return;
    }
    ctrl_node_ = ctrl_ptr_;
}
