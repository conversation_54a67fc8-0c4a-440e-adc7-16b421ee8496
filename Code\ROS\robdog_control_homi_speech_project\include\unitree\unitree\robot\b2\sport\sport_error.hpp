#ifndef __UT_ROBOT_B2_SPORT_ERROR_HPP__
#define __UT_ROBOT_B2_SPORT_ERROR_HPP__

#include <unitree/common/decl.hpp>

namespace unitree
{
namespace robot
{
namespace b2
{
UT_DECL_ERR(UT_ROBOT_SPORT_ERR_CLIENT_POINT_PATH,   4101,   "point path error.")
UT_DECL_ERR(UT_ROBOT_SPORT_ERR_SERVER_OVERTIME,   4201,   "server overtime.")
UT_DECL_ERR(UT_ROBOT_SPORT_ERR_SERVER_NOT_INIT,   4205,   "server function not init.")
}
}
}

#endif//__UT_ROBOT_B2_SPORT_ERROR_HPP__
