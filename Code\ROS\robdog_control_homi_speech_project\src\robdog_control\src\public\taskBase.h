#pragma once

#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <string>
#include <vector>
#include <functional>
#include <stdexcept>

class TaskBase {
public:
    enum class State {
        Idle, Running, Paused, 
        Completed, Stopped, Failed
    };

    enum class ErrorType {
        None, RuntimeError, Timeout, ResourceError
    };

    explicit TaskBase(int task_id = 0);
    virtual ~TaskBase();

    // 控制接口
    void Start();
    void Stop();
    void Pause();
    void Resume();

    // 状态访问
    State GetState() const;
    ErrorType GetLastError() const;
    std::string GetErrorMessage() const;
    int GetTaskId() const;

    // 进度控制
    void SetProgress(int percentage);
    int GetProgress() const;

    // 事件监听
    using StateChangedHandler = std::function<void(State)>;
    void AddStateListener(StateChangedHandler handler);

protected:
    virtual void Execute() = 0;  // 纯虚函数
    
    virtual void OnStart() {}    // 可选重载的钩子函数
    virtual void OnStop() {}
    virtual void OnPause() {}
    virtual void OnResume() {}

private:
    void RunThread();
    void TransitionState(State new_state);

    // 线程控制成员
    std::thread worker_thread_;
    mutable std::mutex state_mutex_;
    std::condition_variable pause_cond_;
    
    // 原子状态
    std::atomic<State> current_state_;
    std::atomic<bool> pause_requested_;
    std::atomic<bool> stop_requested_;
    
    // 错误处理
    ErrorType last_error_;
    std::string error_message_;
    mutable std::mutex error_mutex_;
    
    // 其他成员
    const int task_id_;
    std::atomic<int> progress_percent_;
    
    // 事件系统
    std::vector<StateChangedHandler> state_listeners_;
    mutable std::mutex listener_mutex_;
};