cmake_minimum_required(VERSION 3.5)
project(homi_speech)

if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(ALSA REQUIRED)
find_package(CURL REQUIRED)
find_package(homi_speech_interface REQUIRED)
find_package(Boost REQUIRED COMPONENTS system locale)


if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # uncomment the line when a copyright and license is not present in all source files
  #set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

set(HOMI_SDK_DIR  ${CMAKE_CURRENT_SOURCE_DIR}/extlib/homi_sdk)
if (${CMAKE_HOST_UNIX})
  if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
    set(HOMI_SDK_LIB_DIRS ${HOMI_SDK_DIR}/libx86_64)
  elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
    set(HOMI_SDK_LIB_DIRS ${HOMI_SDK_DIR}/libaarch64)
  endif()
else()
  message(FATAL_ERROR "Unsupported platform")
endif()
add_executable(speech_core 
  ${CMAKE_CURRENT_SOURCE_DIR}/src/speech_core.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/src/OfflineAsrEngine.cpp 
  ${CMAKE_CURRENT_SOURCE_DIR}/src/offline_speech_result_parser.cpp 
  ${CMAKE_CURRENT_SOURCE_DIR}/src/opusEngine.cpp
)
target_link_libraries(speech_core
  ${HOMI_SDK_LIB_DIRS}/libhomi.a
  ${HOMI_SDK_LIB_DIRS}/libhv_static.a
  ${HOMI_SDK_LIB_DIRS}/libmbedx509.a
  ${HOMI_SDK_LIB_DIRS}/libmbedtls.a
  ${HOMI_SDK_LIB_DIRS}/libmbedcrypto.a
  ${HOMI_SDK_LIB_DIRS}/libvs.a
  ${HOMI_SDK_LIB_DIRS}/libcjson.a
  ${HOMI_SDK_LIB_DIRS}/libw_esr.so
  ${HOMI_SDK_LIB_DIRS}/libduerwen_order_demo.a
  ${HOMI_SDK_LIB_DIRS}/libopus.a
  ${Boost_LIBRARIES}
  ${ALSA_LIBRARIES}
  ${CURL_LIBRARIES}
  )
  
target_include_directories(speech_core
  PRIVATE ${HOMI_SDK_DIR}/include
  PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include
)
ament_target_dependencies(speech_core rclcpp homi_speech_interface)

add_executable(helper 
  ${CMAKE_CURRENT_SOURCE_DIR}/src/helper.cpp
)
target_include_directories(helper
  PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include
)
ament_target_dependencies(helper rclcpp homi_speech_interface)

add_executable(capture 
  ${CMAKE_CURRENT_SOURCE_DIR}/src/capture.cpp
)
target_include_directories(capture
  PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include
)
target_link_libraries(capture
  ${Boost_LIBRARIES}
  ${ALSA_LIBRARIES})
ament_target_dependencies(capture rclcpp homi_speech_interface)

add_executable(wakeup 
  ${CMAKE_CURRENT_SOURCE_DIR}/src/wakeup.cpp
)
target_include_directories(wakeup
  PRIVATE ${HOMI_SDK_DIR}/include
  PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include
)
target_link_libraries(wakeup
  ${HOMI_SDK_LIB_DIRS}/libhomiWake.a
  ${HOMI_SDK_LIB_DIRS}/libminiesr.a)
ament_target_dependencies(wakeup rclcpp homi_speech_interface)

install(TARGETS
  speech_core
  helper
  capture
  wakeup
  DESTINATION lib/${PROJECT_NAME})
install(DIRECTORY
  launch
  scripts
  DESTINATION share/${PROJECT_NAME})

install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/upload_image.py
  DESTINATION lib/${PROJECT_NAME}
)

if(NOT CMAKE_CUSTOM_LIB_INSTALL)
install(FILES
  ${HOMI_SDK_LIB_DIRS}/libw_esr.so
  DESTINATION lib
)
else()
install(FILES
  ${HOMI_SDK_LIB_DIRS}/libw_esr.so
  DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL}
)
endif()


ament_package()
