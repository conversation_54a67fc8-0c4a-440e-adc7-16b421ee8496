sdk_init_param: '{
                    "url":"http://36.140.17.36:10000/robot/business/api/device/client/connect/url",
                    "config":{
                        "sn":"1830004229212345670000039",
                        "deviceId":"1830004229212345670000039",
                        "macId":"55:EF:45:97:73:2E",	
                        "deviceType":"2320647",	
                        "firmwareVersion":"1.0.1",
                        "innerType":1
                    },
                    "speech":{
                        "sampleRate":"16000",
                        "voiceId":"x4_lingxiaowan_boy",
                        "domain":"VOICE_INTERACTION_DOG",
                        "textDomain":"VOICE_INTERACTION_XF_SEPARATE_TTS",
                        "encodingConfig":{
                            "up":{
                            "type":"pcm",
                            "sampleRate":"16000"
                            },
                            "down":{
                            "type":"pcm",
                            "sampleRate":"16000"
                            }
                        }
                    }
                }'

sdk_log_level: "debug"
sdk_log_file: "/var/log/cmcc_robot/speechcore"
sppech_round1_waitMs: 10000
sppech_roundn_waitMs: 10000
pcm_stream_topic: "/audio_recorder/pcm_stream"
pcm_stream_wakeup_topic: "/audio_recorder/wakeup_event"
express_topic_name: "/robdog_control/changeExpression"
alsa_playback_sh_param: '{
                            "name":[
                                "default"
                            ]
                        }'
alsa_playback_sh_name: "sh $(find-pkg-share homi_speech)/scripts/get_playback_card.sh"
# speech_display_wakeup_sh_file: "$(find-pkg-share homi_speech)/scripts/display_assistant_wakeup.sh"
# speech_display_idle_sh_file: "$(find-pkg-share homi_speech)/scripts/display_assistant_idle.sh"
speech_display_wakeup_cmd: '
        {
            "path":"$(find-pkg-share homi_speech)/launch/res/voice_wakeup_start.mp4",
            "cnt":1
        }
    '
speech_display_idle_cmd: '
        {
            "path":"$(find-pkg-share homi_speech)/launch/res/voice_wakeup_end.mp4",
            "cnt":1
        }    
    '
speech_voice_print_sh_file: "sh $(find-pkg-share homi_speech)/scripts/homi_speech_voice_print.sh"
speech_voice_print_pcm_file: "/tmp/voice_print.pcm"
sigc_connect_status_topic_name: "/net_monitor"
