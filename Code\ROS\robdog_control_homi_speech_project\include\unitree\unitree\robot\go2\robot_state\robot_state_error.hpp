#ifndef __UT_ROBOT_GO2_ROBOT_STATE_CLIENT_ERROR_HPP__
#define __UT_ROBOT_GO2_ROBOT_STATE_CLIENT_ERROR_HPP__

#include <unitree/common/decl.hpp>

namespace unitree
{
namespace robot
{
namespace go2
{
UT_DECL_ERR(UT_ROBOT_STATE_CLIENT_ERR_SERVICE_SWITCH,      5201,   "service switch error.")
UT_DECL_ERR(UT_ROBOT_STATE_CLIENT_ERR_SERVICE_PROTECTED,   5202,   "service is protected.")
}
}
}

#endif//__UT_ROBOT_GO2_ROBOT_STATE_CLIENT_ERROR_HPP__
