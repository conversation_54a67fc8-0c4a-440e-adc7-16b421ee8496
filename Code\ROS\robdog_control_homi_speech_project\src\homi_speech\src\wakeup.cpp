#include <rclcpp/rclcpp.hpp>
#include <homi_speech_interface/msg/pcm_stream.hpp>
#include <homi_speech_interface/srv/assistant_ctrl.hpp>
#include <homi_speech/InputStream.h>
#include <homi_speech/def.h>
#include <homi_sdk/homiWakeOpenAPI.h>
#include <string>
#include <thread>
#include <vector>
#define SPEECH_SIZE     (320*16)
rclcpp::Node::SharedPtr g_wakeup_node;
int Homi_WakeUpResult(char *param, char *pUserInfo)
{
    RCLCPP_INFO(rclcpp::get_logger("wakeup"),"homiWake: wakeup:ivw param=%s user_param=%s ", param, pUserInfo);
    auto srvReq = std::make_shared<homi_speech_interface::srv::AssistantCtrl::Request>();
    srvReq->is_normal = true;
    srvReq->start = true;
    srvReq->stop = true;
    srvReq->start_wait_ms = 5000;
    srvReq->stop_wait_ms = 5000;
    srvReq->mutiple_wheels = true;
    srvReq->inquiry_text = "";
    srvReq->inquiry_sub_type = -1;
    srvReq->notify_user_abort = true;
    auto assistantClient = g_wakeup_node->create_client<homi_speech_interface::srv::AssistantCtrl>(PARAM_SPEECH_ASSISTANT_CTRL_SERVICE);
    if(assistantClient->wait_for_service(std::chrono::seconds(1)))
    {
        auto result = assistantClient->async_send_request(srvReq);
        auto status = result.wait_for(std::chrono::seconds(1));
        if(status == std::future_status::ready)
        {
            RCLCPP_INFO(rclcpp::get_logger("wakeup"),"wakeup:ivw assistantCtrl success %d", result.get()->error_code);
        }
        else
        {
            RCLCPP_WARN(rclcpp::get_logger("wakeup"),"wakeup:ivw assistantCtrl failed");
        }
    }
    else
    {
        RCLCPP_WARN(rclcpp::get_logger("wakeup"),"Failed to wait_for_service service assistant");
    }

    return 0;
}
static void pcmStreamCallback(const homi_speech_interface::msg::PCMStream::ConstSharedPtr ptr)
{
    if(ptr==nullptr) return ;
    homi_speech::InputStreamData data;
    data.ms = ptr->ts;
    data.data = ptr->data;
    homi_speech::InputStream::getInstance().push(std::make_shared<homi_speech::InputStreamData>(data));
}
int main(int argc, char *argv[])
{
    setlocale(LC_ALL, "");
    rclcpp::init(argc, argv);
    g_wakeup_node = rclcpp::Node::make_shared("wakeup",rclcpp::NodeOptions().automatically_declare_parameters_from_overrides(true));
    RCLCPP_INFO(rclcpp::get_logger("wakeup"),"wake_up_xf start up");
    homiWakeCallBackFunList callBackFunList;
    callBackFunList.Homi_WakeUpResult = Homi_WakeUpResult;
    std::string mlpPath;
    std::string keywordMainPath;
    std::string keywordMinglingci;
    std::string pcm_stream_topic;
    if(!g_wakeup_node->get_parameter(PARAM_RES_XF_MLP_PATH,mlpPath)
        ||!g_wakeup_node->get_parameter(PARAM_RES_XF_KEYWORD_MAIN_PATH,keywordMainPath)
        ||!g_wakeup_node->get_parameter(PARAM_RES_XF_KEYWORD_MINGLINGCI_PATH,keywordMinglingci)
        ||!g_wakeup_node->get_parameter(PARAM_PCM_STREAM_TOPIC,pcm_stream_topic))
    {
        RCLCPP_ERROR(rclcpp::get_logger("wakeup"),"read param error ,exit");
        return -1;
    }
    RCLCPP_INFO(rclcpp::get_logger("wakeup"),"mlpPath=%s keywordMainPath=%s keywordMinglingci=%s",
                        mlpPath.c_str(),keywordMainPath.c_str(),keywordMinglingci.c_str());
    auto ret = homiWakeInit(&callBackFunList, (char *)mlpPath.c_str(), (char *)keywordMainPath.c_str(),(char *)keywordMinglingci.c_str()); 
    if(ret)
    {
        RCLCPP_ERROR(rclcpp::get_logger("wakeup"),"homiWakeInit failed , ret = %d\n",ret);
        return -1;
    }

    ret = homiStartWake();
    if(ret)
    {
        RCLCPP_ERROR(rclcpp::get_logger("wakeup"),"homiStartWake failed , ret = %d\n",ret);
        return -1;
    }
    long long lcount = 0;
    auto timer = g_wakeup_node->create_wall_timer(std::chrono::seconds(5),[&lcount](){
        lcount++;
        RCLCPP_INFO(rclcpp::get_logger("wakeup"),"idle ,run count =%lld s ...",(lcount*5));
    });
    bool stop=false;
    auto mythread = std::thread([&stop](){
        unsigned int count=0;
        std::vector<unsigned char> speechBuf;
        while(!stop)
        {
            auto dataPtr = homi_speech::InputStream::getInstance().pop(std::chrono::seconds(2));
            if(dataPtr==nullptr)
            {
                continue;
            }
            auto readSize = dataPtr->data.size();
            auto buffer = dataPtr->data.data();
            count++;
            if(readSize>0)
            {
                speechBuf.insert(speechBuf.end(),buffer,buffer+readSize);
            }
            while(speechBuf.size()>=SPEECH_SIZE)
            {
                homiWriteWakeUpPcm((char *)speechBuf.data(),SPEECH_SIZE);
                speechBuf.erase(speechBuf.begin(), speechBuf.begin() + SPEECH_SIZE);
            }
            if(count%(50*5)==0)
            {
                RCLCPP_INFO(rclcpp::get_logger("wakeup"),"update wake_up_xf");
            }
            
        }
        

    });
    auto sub2 = g_wakeup_node->create_subscription<homi_speech_interface::msg::PCMStream>(pcm_stream_topic,50,&pcmStreamCallback);
    rclcpp::spin(g_wakeup_node);
    stop = true;
    if(mythread.joinable())
    {
        mythread.join();
    }
    ret = homiStopWake();
    if(ret)
    {
        RCLCPP_WARN(rclcpp::get_logger("wakeup"),"homiStopWake failed , ret = %d\n",ret);
    }

    ret = homiUnInitWake();
    if(ret)
    {
        RCLCPP_WARN(rclcpp::get_logger("wakeup"),"homiUnInitWake failed , ret = %d\n",ret);
    }
    RCLCPP_INFO(rclcpp::get_logger("wakeup"),"wake_up_xf end");
    return 0;
}