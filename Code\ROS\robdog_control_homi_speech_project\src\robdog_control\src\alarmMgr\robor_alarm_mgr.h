/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:05:05
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-01 10:26:34
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_config.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#pragma once
#include <fstream>
#include <tuple>
#include <unordered_map>
#include <map>
#include <chrono>
#include <string>
#include <jsoncpp/json/json.h>

#include <homi_com/singleton.hpp>
#include <homi_com/context.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>

#include "alarmMgr/alarm_info.h"

using namespace std;

class RobdogCtrlNode;
class AlarmInfoMgr: public base::singleton<AlarmInfoMgr>, public base::Context<AlarmInfoPtr>
{
public:
    AlarmInfoMgr();
    ~AlarmInfoMgr();
    void init(RobdogCtrlNode* ctrl_ptr_);

  
public:
    RobdogCtrlNode* ctrl_node_ = nullptr;

};