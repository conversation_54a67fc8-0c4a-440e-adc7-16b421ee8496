#ifndef __HY_ESR_API_H__
#define __HY_ESR_API_H__
#include <string>
#include <memory>
namespace hy_esr
{
	//离线识别引擎初始化
	//cfg:配置文件路径,配置信息都放到配置文件中
	int ESRinit(const std::string cfg);

	//逆初始化
	int ESRunInit(void);

	class IESR{
	public:
		virtual ~IESR()=0;

		//往离线识别引擎写入音频数据，音频格式为：16bit,单声道
		//pSamples:音频数据
		//nLen:音频数据长度，单位字节
		//成功执行，返回值为0，否则为非0,
		virtual int ESRwrite(const char* pSamples, int nLen)=0;

		//每次ESRwrite后调用,获取识别结果
		//result:识别结果,json格式，例子如下：往前走三米
		/*
		{"ws":[
				{"sc":"0","w":"往前走","slot":"moveFront"}
				,{"sc":"0","w":"三","slot":"number"}
				,{"sc":"0","w":"米","slot":"unit"}
			]
		}
		*/
		//如果有识别结果，返回值为0，否则为非0
		virtual int ESRresult(std::string &result)=0;
	};
	//离线识别以一个轮次为单位，每轮离线识别开始，调该接口获取一个ESR实例，每次交互结束后，销毁该实例。
	//成功执行，返回值为0，否则为非0,
	std::shared_ptr<IESR> createESR();

}
#endif