#!/usr/bin/env python3
import rclpy                                     # ROS2 Python接口库
import rclpy.callback_groups
import rclpy.exceptions
import rclpy.executors
from rclpy.node   import Node 
import requests
from homi_speech_interface.srv import UploadImageUrl,UploadImage
import os



class upload_image_service(Node):
    def __init__(self, name):
        super().__init__(name) 
        cb_group = rclpy.callback_groups.ReentrantCallbackGroup()  
        self.client = self.create_client(UploadImageUrl, '/homi_speech/speech_upload_image_url_service',callback_group=cb_group)              # 创建服务客户端对象（服务接口类型，服务名）
        
        self.srv = self.create_service(UploadImage, 'upload_image_service', self.adder_callback,callback_group=cb_group )    # 创建服务器对象（接口类型、服务名、服务器回调函数）

    def upload_image(self,file_name,url):
        with open(file_name, 'rb') as f:
            files = {'file': (file_name, f)}
            r = requests.post(url, files=files)
            return r.text

    def send_url_request(self,file_size):                                                       # 创建一个发送服务请求的函数
        if(not self.client.wait_for_service(timeout_sec=1.0)):                  # 循环等待服务器端成功启动
            raise Exception('upload_image_service not available, return error')
        req = UploadImageUrl.Request() 
        req.file_size = file_size
        return self.client.call(req)                        # 异步方式发送服务请求
        
 
    def adder_callback(self, request, response):                                           # 创建回调函数，执行收到请求后对数据的处理                                          # 完成加法求和计算，将结果放到反馈的数据中
        self.get_logger().info('Upload image service called')
        try:
            self.get_logger().info("Image fileName: " + request.file_name)
            file_name = request.file_name
            file_size = os.path.getsize(file_name)
            url_res = self.send_url_request(file_size)
            self.get_logger().info("url_res: "+str(url_res))
            if url_res.error_code != 0 or url_res.url == "":
                response.error_code = -2  
                return response 
            url = url_res.url   
            res = self.upload_image(file_name,url)
            self.get_logger().info("upload_image res: " + res)
            response.error_code = 0
        except rclpy.exceptions.InvalidServiceNameException as e:
            self.get_logger().info('Service call failed %r' % (e,))
            response.error_code = -3
        except Exception as e:
            self.get_logger().info('Service call failed %r' % (e,))
            response.error_code = -1
        return response    

def main(args=None):                                 # ROS2节点主入口main函数
    rclpy.init(args=args)                            # ROS2 Python接口初始化
    node = upload_image_service("upload_image")       # 创建ROS2节点对象并进行初始化
    executor = rclpy.executors.MultiThreadedExecutor()
    executor.add_node(node)                          # 将节点对象添加到执行器中
    executor.spin()
    executor.shutdown()                              # 关闭执行器

if __name__ == '__main__':
    main()