#ifndef __SRC_HOMI_INCLUDE_HOMI_AUDIOCODE_H_
#define __SRC_HOMI_INCLUDE_HOMI_AUDIOCODE_H_
#include "AudioStream.h"
namespace homi::app 
{
    struct AudioCodeConfig
    {
        std::string json;
    };
    struct AudioCodeFrame
    {
        AudioCodeConfig codeConfig;
        AudioConfig config;
        std::chrono::milliseconds ts;
        std::vector<unsigned char> data;
    };
    class IAudioDecode
    {
    public:
        virtual bool decode(std::shared_ptr<AudioCodeFrame> &inDataPtr,std::vector<std::shared_ptr<AudioFrame>> &outData) = 0;
    };
    class IAudioEncode
    {
    public:
        virtual bool encode(std::shared_ptr<AudioFrame> &inDataPtr,std::vector<std::shared_ptr<AudioCodeFrame>> &outData) = 0;
    };  
    class IAudioDecodeFactory
    {
    public:
        virtual std::shared_ptr<IAudioDecode> create(const AudioCodeConfig &config) = 0;
    };
    class IAudioEncodeFactory
    {
    public:
        virtual std::shared_ptr<IAudioEncode> create(const AudioConfig &config) = 0;
    };
}

#endif  // __SRC_HOMI_INCLUDE_HOMI_AUDIOCODE_H_
