import launch
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, IncludeLaunchDescription
from launch.substitutions import LaunchConfiguration
from launch_ros.substitutions import FindPackageShare
from launch_ros.actions import Node
from launch.actions import RegisterEvent<PERSON>and<PERSON>, OpaqueFunction
from launch.event_handlers import OnProcessExit
import os
from ament_index_python.packages import get_package_share_directory
from launch.launch_description_sources import PythonLaunchDescriptionSource
import yaml
from configparser import ConfigParser
# 读取并处理BOM
def read_ini_file(file_path):
    with open(file_path, 'r', encoding='utf-8-sig') as file:
        content = file.read()
    return content

# 写入INI文件
def write_ini_file(file_path, config):
    with open(file_path, 'w', encoding='utf-8') as configfile:
        config.write(configfile)

conf = ConfigParser()
conf.read('/etc/cmcc_robot/cmcc_dev.ini')

def generate_launch_description():
    config_share_dir = get_package_share_directory('launch_package')

    andlink = Node(
        package="andlink",
        executable="andlink_node",
        #name='andlink_node',
        output='log',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
    )
    ble = Node(
        package="ble",
        executable="ble_node",
        #name='andlink_node',
        output='log',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
        # respawn=True
    )
    network = Node(
        package="network",
        executable="network_node",
        #name='andlink_node',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
        # output='log',
        # respawn=True
    )

    expression = Node(
        package="expression",
        executable="expression_node",
        #name='expression_node',  
        output='log',  
        respawn=True
    )

    robdog_control = Node(
        package="robdog_control",
        executable="robdog_control_node",
        output='log',
        parameters=[config_share_dir + '/configs/robot_config.yaml'],
    )

    homi_speech_yaml = IncludeLaunchDescription(        # 包含指定路径下的另外一个launch文件
        PythonLaunchDescriptionSource([os.path.join(
        get_package_share_directory('homi_speech'), 'launch'),
        '/speech_helper_near.launch.py'])
    )


    return LaunchDescription([
            expression,
            andlink,
            network,
            ble,
            robdog_control,
            homi_speech_yaml,
    ])