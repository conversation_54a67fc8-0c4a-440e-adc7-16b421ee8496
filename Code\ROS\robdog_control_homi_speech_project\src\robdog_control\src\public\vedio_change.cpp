#include "tools.h"
#include <iostream>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>
#include <vector>
#include <future>
#include <stdexcept>
#include <array>
#include <regex>
#include <thread>
#include <chrono>
#include <unistd.h>
#include <ament_index_cpp/get_package_share_directory.hpp>
bool ExpressionChange::is_directory(const std::string &path){
        struct stat statbuf;
        if (stat(path.c_str(), &statbuf) != 0) {
            return false;
        }
        return S_ISDIR(statbuf.st_mode);
}

std::string ExpressionChange::get_random_video_from_directory(const std::string &directory)
{
    DIR *dir = opendir(directory.c_str());
    if (dir == nullptr) {
        std::cerr << "Failed to open directory: " << directory << std::endl;
        return "";
    }
    struct DirectoryCloser {
        void operator()(DIR* dir) const {
            if (dir != nullptr) closedir(dir);
        }
    };
    std::unique_ptr<DIR, DirectoryCloser> safe_dir(dir);
    std::vector<std::string> video_files;
    struct dirent *entry;
    while ((entry = readdir(safe_dir.get())) != nullptr) {
        if (entry->d_name[0] == '.' && (entry->d_name[1] == '\0' || (entry->d_name[1] == '.' && entry->d_name[2] == '\0'))) {
            continue;
        }
        std::string full_path = directory + "/" + entry->d_name;
        struct stat file_info;
        if (stat(full_path.c_str(), &file_info) == 0 && S_ISREG(file_info.st_mode)) {
            video_files.push_back(full_path);
        }
    }
    if (video_files.empty()) {
        std::cerr << "No files found in directory: " << directory << std::endl;
        return "";
    }
    static bool seeded = false;
    if (!seeded) {
        srand(static_cast<unsigned>(time(nullptr)));
        seeded = true;
    }
    int random_index = rand() % video_files.size();
    std::cout << "Selected file: " << video_files[random_index] << std::endl;
    return video_files[random_index];
}
long ExpressionChange::getVideoDurationInMilliseconds(const std::string& filePath) {
    if (filePath.empty()) {
        std::cout<<"File path cannot be empty."<<std::endl;
        throw std::invalid_argument("File path cannot be empty.");
    }
    std::string command = "ffmpeg -i \"" + filePath + "\" 2>&1 | grep \"Duration\"";
    std::array<char, 128> buffer;
    std::string result;
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        std::cout<<"popen() failed!"<<std::endl;
    }
    while (fgets(buffer.data(), buffer.size(), pipe) != nullptr) {
        result += buffer.data();
    }
    pclose(pipe);
    std::regex durationRegex(R"(Duration: (\d+):(\d+):(\d+)\.(\d+))");
    std::smatch match;
    if (std::regex_search(result, match, durationRegex)) {
        int hours = std::stoi(match[1]);
        int minutes = std::stoi(match[2]);
        int seconds = std::stoi(match[3]);
        int milliseconds = std::stoi(match[4]);
        long totalMilliseconds = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds * 10;
        return totalMilliseconds;
    } else {
        throw std::runtime_error("Could not extract duration from ffmpeg output.");
    }
}

void ExpressionChange::load_new_video(const std::string &filename) {
    if (access(filename.c_str(), F_OK) != 0) {
        std::cerr << "[ERROR] File not found: " << filename << std::endl;
        return;
    }
    try {
        std::string command = "echo '{\"command\": [\"loadfile\", \"" + filename + "\", \"replace\"]}' | socat - /tmp/mpv-socket";
        int result = std::system(command.c_str());
        if (result != 0) {
            throw std::runtime_error("Failed to send loadfile command to mpv");
        }
        std::cout<< "Sent loadfile command to mpv to load"<<filename<<std::endl;
    } catch (const std::exception &e) {
        std::cerr<< "Error loading new video:"<< e.what()<<std::endl;
    }
}

void ExpressionChange::async_callback_work(const std::string& msg,const int cnt) {
    std::thread([this, msg, cnt]() {
        try {
            this->async_work(msg, cnt);
        } catch (const std::exception& e) {
            std::cerr << "Error in async_work: " << e.what() << std::endl;
        }
    }).detach();
}
void ExpressionChange::async_work(const std::string& msg,const int cnt){
    {
        std::unique_lock<std::shared_mutex> lock(callback_mutex);
        video_filename_ = msg;
    }    
    std::cout<< "Received new video filename: " <<video_filename_<<std::endl;
    
    if (is_directory(video_filename_)) {
        std::string random_video = get_random_video_from_directory(video_filename_);
        if (!random_video.empty()) {
            std::unique_lock<std::shared_mutex> lock(callback_mutex);
            video_filename_ = random_video;
        } else {
            std::cerr<< "No video files found in directory:"<<video_filename_<<std::endl;
            return;
        }
    }
    auto duration = getVideoDurationInMilliseconds(video_filename_);
    if (cnt){
        load_new_video(video_filename_);
        auto start_time = std::chrono::steady_clock::now();
        while (std::chrono::steady_clock::now() - start_time < std::chrono::milliseconds(cnt * duration)) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        std::string default_video_path = 
            ament_index_cpp::get_package_share_directory("robdog_control") + 
            "/resource/video/default/";
        load_new_video(default_video_path);
    }
    else
        load_new_video(video_filename_);
}

