!**/install.sh
.cursor
**/build
**/build*
**/install
**/install*

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 缓存文件夹
.cache/
__pycache__/
*.py[cod]
*$py.class

# 依赖管理
**/venv/
**/.venv/
**/env/
**/.env/
**/node_modules/
**/.poetry/

# IDE和编辑器文件
.idea/
.vscode/
*.sublime-*
.atom/
.project
.settings/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 其他开发文件
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/
dist/
*.egg-info/

# ROS特定文件
**/.ros/
**/devel/
**/.catkin_workspace

# ROS2特定文件
**/.ament_prefix_path
**/.colcon_install_layout
**/log/
