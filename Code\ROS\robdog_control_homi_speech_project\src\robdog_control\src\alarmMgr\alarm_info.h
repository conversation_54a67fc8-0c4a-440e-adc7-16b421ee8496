/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:05:05
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-01 10:26:34
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_config.h
 * @Description: 告警类型数据类，包括告警主类型，告警子类型，告警等级等（可以用宏定义去定义主子类型）
 */
#pragma once
#include <homi_com/singleton.hpp>
#include <homi_com/context.hpp>
#include <xiaoli_com/xiaoli_pub_def.h>

using namespace std;

class AlarmInfo
{
public:
    AlarmInfo();
    ~AlarmInfo();

public:
    PropertyBuilderByName(std::string, AlarmType, "");                            //任务UUid
    PropertyBuilderByName(std::string, SubAlarmType, "");                         //任务UUid

};

#define AlarmInfoPtr std::unique_ptr<AlarmInfo>